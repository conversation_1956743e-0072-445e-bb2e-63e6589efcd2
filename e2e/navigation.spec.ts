import { expect, test } from '@playwright/test';
import { retry, waitForElementStable, waitForPageReady } from './utils/test-helpers';

test.describe('Navigation', () => {
  test('should navigate between pages', async ({ page, browserName }) => {
    // Start at the homepage with longer timeout for Firefox
    await page.goto('/', { timeout: browserName === 'firefox' ? 60000 : 30000 });

    // Wait for the page to be fully loaded
    await waitForPageReady(page);

    // Use retry for more reliable testing
    await retry(
      async () => {
        // Find and click on a service category or link
        // Note: You'll need to adjust these selectors based on your actual HTML structure
        const serviceLinks = page.locator('a[href*="/servicos/"]').first();

        // If service links exist, click the first one
        if ((await serviceLinks.count()) > 0) {
          // Store the URL we're navigating to
          const href = await serviceLinks.getAttribute('href');

          // Wait for the link to be stable before clicking
          await waitForElementStable(serviceLinks);

          // Add extra wait for Firefox
          if (browserName === 'firefox') {
            await page.waitForTimeout(1000);
          }

          // Force click for Firefox which can have issues with regular clicks
          if (browserName === 'firefox') {
            await serviceLinks.click({ force: true });
          } else {
            await serviceLinks.click();
          }

          // Wait for navigation to complete with longer timeout for Firefox
          await page.waitForURL(`**${href}`, {
            timeout: browserName === 'firefox' ? 30000 : 15000,
          });

          // Verify we're on the expected page
          expect(page.url()).toContain('/servicos/');

          // Take a screenshot for visual verification
          await page.screenshot({ path: `screenshots/service-page-${browserName}.png` });
        } else {
          console.warn('No service links found to test navigation');
        }
      },
      {
        timeout: browserName === 'firefox' ? 40000 : 20000,
        interval: browserName === 'firefox' ? 2000 : 1000,
        errorMessage: 'Failed to navigate between pages',
      }
    );
  });

  test('should handle mobile menu toggle', async ({ page, browserName }) => {
    // Set viewport to mobile size
    await page.setViewportSize({ width: 375, height: 667 });

    // Navigate to the homepage
    await page.goto('/');

    // Wait for the page to be fully loaded
    await waitForPageReady(page);

    // Use retry for more reliable testing of the mobile menu
    await retry(
      async () => {
        // Look for a mobile menu button with more specific selectors based on the actual implementation
        // Use a more flexible selector that works across browsers
        const menuButton = page.locator(
          'button[aria-label="Open menu"], button:has(svg[data-lucide="Menu"])'
        );

        // Wait for the button to be visible and stable with longer timeout for Firefox
        const buttonTimeout = browserName === 'firefox' ? 10000 : 5000;
        await expect(menuButton).toBeVisible({ timeout: buttonTimeout });

        // Add extra wait for Firefox
        if (browserName === 'firefox') {
          await page.waitForTimeout(1000);
        }

        await waitForElementStable(menuButton);

        // Force click for Firefox which can have issues with regular clicks
        if (browserName === 'firefox') {
          await menuButton.click({ force: true });
        } else {
          await menuButton.click();
        }

        // Add a small delay after clicking to ensure the menu has time to start opening
        await page.waitForTimeout(500);

        // Based on the implementation, the mobile navigation is rendered when isMenuOpen is true
        // Use a more flexible selector that works across browsers
        const mobileNav = page.locator('nav.fixed.inset-0.z-50, nav[style*="top: 98px"]');

        // Wait for the mobile navigation to be visible with longer timeout for Firefox
        const navTimeout = browserName === 'firefox' ? 10000 : 5000;
        await expect(mobileNav).toBeVisible({ timeout: navTimeout });

        // Take a screenshot with the menu open for debugging
        await page.screenshot({ path: `screenshots/mobile-menu-open-${browserName}.png` });

        // Now look for the close button with a more flexible selector
        const closeButton = page.locator(
          'button[aria-label="Close menu"], button:has(svg[data-lucide="X"])'
        );
        await expect(closeButton).toBeVisible({ timeout: navTimeout });

        // Add extra wait for Firefox
        if (browserName === 'firefox') {
          await page.waitForTimeout(1000);
        }

        // Force click for Firefox
        if (browserName === 'firefox') {
          await closeButton.click({ force: true });
        } else {
          await closeButton.click();
        }

        // Add a small delay after clicking to ensure the menu has time to start closing
        await page.waitForTimeout(500);

        // Verify the menu is closed by checking that the mobile navigation is no longer visible
        // Use a longer timeout for Firefox
        await expect(mobileNav).not.toBeVisible({ timeout: navTimeout });

        // Take a screenshot with the menu closed for debugging
        await page.screenshot({ path: `screenshots/mobile-menu-closed-${browserName}.png` });
      },
      {
        timeout: 45000, // Longer timeout for the entire retry operation
        interval: 2000, // Longer interval between retries
        errorMessage: 'Failed to test mobile menu toggle',
      }
    );
  });

  test('should search for services', async ({ page, browserName }) => {
    // Navigate to the homepage with longer timeout for Firefox
    await page.goto('/', { timeout: browserName === 'firefox' ? 60000 : 30000 });

    // Wait for the page to be fully loaded
    await waitForPageReady(page);

    // Use retry for more reliable testing
    await retry(
      async () => {
        // Look for a search input with more flexible selector
        const searchInput = page.locator(
          'input[type="search"], [data-testid="search-input"], input[placeholder*="Buscar"]'
        );

        // If a search input exists, test it
        if ((await searchInput.count()) > 0) {
          // Wait for the search input to be stable
          await waitForElementStable(searchInput);

          // Add extra wait for Firefox
          if (browserName === 'firefox') {
            await page.waitForTimeout(1000);
          }

          // Type a search query
          await searchInput.fill('assistência técnica');

          // Add extra wait for Firefox
          if (browserName === 'firefox') {
            await page.waitForTimeout(1000);
          }

          // Press Enter to submit the search
          await searchInput.press('Enter');

          // Wait for search results to appear with longer timeout for Firefox
          await page.waitForSelector('[data-testid="search-results"], .search-results', {
            timeout: browserName === 'firefox' ? 30000 : 15000,
          });

          // Take a screenshot of search results
          await page.screenshot({ path: `screenshots/search-results-${browserName}.png` });
        } else {
          console.warn('No search input found to test');
        }
      },
      {
        timeout: browserName === 'firefox' ? 40000 : 20000,
        interval: browserName === 'firefox' ? 2000 : 1000,
        errorMessage: 'Failed to search for services',
      }
    );
  });
});
