# End-to-End Tests with <PERSON><PERSON>

This directory contains end-to-end tests for the GetNinjas E-commerce Frontend project using Playwright.

## Test Structure

- `home.spec.ts`: Tests for the homepage functionality
- `api.spec.ts`: Tests for API integration
- `navigation.spec.ts`: Tests for navigation and user interactions
- `utils/`: Utility functions for tests.
  - `env.ts`: Functions to read environment variables.

## Running Tests

You can run the tests using the following commands:

```bash
# Run all e2e tests
pnpm test:e2e

# Run tests with UI mode (for debugging)
pnpm test:e2e:ui

# Run tests in debug mode
pnpm test:e2e:debug

# Show the HTML report after tests have run
pnpm test:e2e:report
```

## Configuration

The tests are configured in `playwright.config.ts` in the project root. Key configuration options:

- Tests run against the URL specified in `NEXT_PUBLIC_BASE_URL` environment variable (defaults to `http://localhost:3000`)
- API requests use the URL from `NEXT_PRIVATE_API_BASE_URL` environment variable
- Tests run in multiple browsers: Chrome, Firefox, Safari, and mobile viewports
- Screenshots are captured on test failures
- A local development server is started automatically before tests run

## Writing New Tests

To add new tests:

1. Create a new `.spec.ts` file in the `e2e` directory
2. Import the necessary Playwright utilities:
   ```typescript
   import { test, expect } from '@playwright/test';
   ```
3. Write your tests using the Playwright API
4. Run the tests to verify they work as expected

## Best Practices

- Keep tests independent of each other
- Use descriptive test names
- Group related tests using `test.describe()`
- Use page objects for complex pages
- Avoid hard-coding selectors when possible
- Use data attributes like `data-testid` for more reliable selectors

## Debugging

If tests are failing, you can:

1. Run tests in UI mode: `pnpm test:e2e:ui`
2. Run tests in debug mode: `pnpm test:e2e:debug`
3. Check the screenshots in the `screenshots` directory
4. View the HTML report: `pnpm test:e2e:report`
