import { expect, test } from '@playwright/test';
import { getApiBaseUrl } from './utils/env';
import { retry, waitForPageReady, waitForRequest } from './utils/test-helpers';

test.describe('API Integration', () => {
  // Initialize API base URL (used for reference in comments)
  getApiBaseUrl();

  test('should be able to fetch health status', async ({ request }) => {
    // Make a request to the health endpoint
    const response = await request.get('/api/health');

    // Check if the response is successful
    expect(response.status()).toBe(200);

    // Check if the response contains expected data
    const data = await response.json();
    expect(data.status).toBe('ok');
  });

  test('should be able to fetch service types', async ({ request, page, browserName }) => {
    // Navigate to the homepage first to establish cookies/session if needed
    // Use a longer timeout for Firefox which can be slower
    await page.goto('/', { timeout: browserName === 'firefox' ? 60000 : 30000 });

    // Wait for the page to be fully loaded
    await waitForPageReady(page);

    // Use retry for more reliable testing
    await retry(
      async () => {
        // Make a request to the service-types API with a missing slug parameter
        const response = await request.get('/api/service-types');

        // Check if the response is successful
        expect(response.status()).toBe(400); // Expecting 400 because slug parameter is required

        // Check if the response contains expected error message
        const data = await response.json();
        expect(data.message).toContain('Slug parameter is required');
      },
      {
        timeout: 20000,
        interval: 1000,
        errorMessage: 'Failed to fetch service types API',
      }
    );
  });

  test('should display service data fetched from API', async ({ page, browserName }) => {
    // Set test timeout for this specific test (Firefox needs more time)
    test.setTimeout(browserName === 'firefox' ? 90000 : 60000);

    // Navigate to the homepage with longer timeout for Firefox
    await page.goto('/', { timeout: browserName === 'firefox' ? 60000 : 30000 });

    // Wait for the page to be fully loaded using our helper
    await waitForPageReady(page);

    // Wait for API requests to complete with longer timeout for Firefox
    await waitForRequest(page, /service-type\/list/, {
      timeout: browserName === 'firefox' ? 45000 : 15000,
    });

    // Use retry for more reliable testing
    await retry(
      async () => {
        // Wait for service content to be visible
        await page.waitForSelector('h2:has-text("Disponível para agendar agora")', {
          state: 'visible',
          timeout: browserName === 'firefox' ? 10000 : 5000,
        });

        // Verify service cards are loaded
        const serviceCards = page.locator('.overflow-x-auto > div').first();
        await expect(serviceCards).toBeVisible({
          timeout: browserName === 'firefox' ? 10000 : 5000,
        });
      },
      {
        timeout: browserName === 'firefox' ? 40000 : 20000,
        interval: browserName === 'firefox' ? 2000 : 1000,
        errorMessage: 'Failed to find service content',
      }
    );

    // Verify page has loaded content from API
    const pageContent = await page.content();
    expect(pageContent).toContain('GetNinjas');

    // Take a screenshot for debugging purposes
    await page.screenshot({ path: `screenshots/services-loaded-${browserName}.png` });
  });
});
