import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables from .env file
dotenv.config();

/**
 * Utility function to get environment variables
 * Falls back to default values if not found
 */
export function getEnv(key: string, defaultValue: string = ''): string {
  return process.env[key] || defaultValue;
}

/**
 * Get the API base URL from environment variables
 */
export function getApiBaseUrl(): string {
  return getEnv('NEXT_PRIVATE_API_BASE_URL');
}

/**
 * Get the base URL for the application
 */
export function getBaseUrl(): string {
  return getEnv('http://localhost:3000');
}

/**
 * Read all environment variables from .env file
 * This is useful for debugging
 */
export function readEnvFile(): Record<string, string> {
  try {
    const envPath = path.resolve(process.cwd(), '.env');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars: Record<string, string> = {};

    envContent.split('\n').forEach((line) => {
      // Skip empty lines and comments
      if (!line || line.startsWith('#')) return;

      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim();
        envVars[key.trim()] = value;
      }
    });

    return envVars;
  } catch (error) {
    console.error('Error reading .env file:', error);
    return {};
  }
}
