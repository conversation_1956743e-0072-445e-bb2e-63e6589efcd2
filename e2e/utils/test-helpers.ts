import { Page, Locator } from '@playwright/test';

/**
 * Wait for a network request to complete
 * @param page Playwright page object
 * @param urlPattern URL pattern to match
 * @param options Options for the wait
 */
export async function waitForRequest(
  page: Page,
  urlPattern: string | RegExp,
  options: { timeout?: number } = {}
): Promise<void> {
  const timeout = options.timeout || 30000;
  try {
    await page.waitForResponse(
      (response) => {
        return response.url().match(urlPattern) !== null;
      },
      { timeout }
    );
  } catch (_error) {
    console.warn(`Timeout waiting for request matching ${urlPattern}`);
  }
}

/**
 * Wait for an element to be stable (not moving)
 * @param locator Playwright locator
 * @param options Options for the wait
 */
export async function waitForElementStable(
  locator: Locator,
  options: { timeout?: number; checkInterval?: number } = {}
): Promise<void> {
  const timeout = options.timeout || 10000;
  const checkInterval = options.checkInterval || 100;
  const startTime = Date.now();

  let lastRect: { x: number; y: number } | null = null;

  while (Date.now() - startTime < timeout) {
    const isVisible = await locator.isVisible();
    if (!isVisible) {
      await new Promise((resolve) => setTimeout(resolve, checkInterval));
      continue;
    }

    const boundingBox = await locator.boundingBox();
    if (!boundingBox) {
      await new Promise((resolve) => setTimeout(resolve, checkInterval));
      continue;
    }

    const currentRect = { x: boundingBox.x, y: boundingBox.y };

    if (
      lastRect &&
      Math.abs(currentRect.x - lastRect.x) < 1 &&
      Math.abs(currentRect.y - lastRect.y) < 1
    ) {
      // Element is stable
      return;
    }

    lastRect = currentRect;
    await new Promise((resolve) => setTimeout(resolve, checkInterval));
  }

  throw new Error(`Element not stable after ${timeout}ms`);
}

/**
 * Retry an action until it succeeds or times out
 * @param action Function to retry
 * @param options Options for the retry
 */
export async function retry<T>(
  action: () => Promise<T>,
  options: {
    timeout?: number;
    interval?: number;
    errorMessage?: string;
  } = {}
): Promise<T> {
  const timeout = options.timeout || 30000;
  const interval = options.interval || 1000;
  const errorMessage = options.errorMessage || 'Action failed after retries';

  const startTime = Date.now();
  let lastError: Error | null = null;

  while (Date.now() - startTime < timeout) {
    try {
      return await action();
    } catch (_error) {
      lastError = _error instanceof Error ? _error : new Error(String(_error));
      await new Promise((resolve) => setTimeout(resolve, interval));
    }
  }

  throw new Error(`${errorMessage}: ${lastError?.message || 'Unknown error'}`);
}

/**
 * Wait for page to be in a ready state
 * @param page Playwright page object
 */
export async function waitForPageReady(page: Page): Promise<void> {
  // Wait for page to be loaded
  await page.waitForLoadState('domcontentloaded');

  // Wait for network to be idle
  await page.waitForLoadState('networkidle');

  // Wait a bit more to ensure JavaScript has executed
  await page.waitForTimeout(500);
}
