import { expect, test } from '@playwright/test';
import { retry, waitForPageReady } from './utils/test-helpers';

test.describe('Homepage', () => {
  test('should load the homepage successfully', async ({ page, browserName }) => {
    // Navigate to the homepage with longer timeout for Firefox
    await page.goto('/', { timeout: browserName === 'firefox' ? 60000 : 30000 });

    // Wait for the page to be fully loaded
    await waitForPageReady(page);

    // Check if the page title contains the expected text
    await retry(
      async () => {
        await expect(page).toHaveTitle(/GetNinjas/);
      },
      {
        timeout: browserName === 'firefox' ? 30000 : 15000,
        interval: browserName === 'firefox' ? 2000 : 1000,
        errorMessage: 'Page title did not contain expected text',
      }
    );

    // Take a screenshot for visual verification
    await page.screenshot({ path: `screenshots/homepage-${browserName}.png` });
  });

  test('should have main navigation elements', async ({ page, browserName }) => {
    // Navigate to the homepage with longer timeout for Firefox
    await page.goto('/', { timeout: browserName === 'firefox' ? 60000 : 30000 });

    // Wait for the page to be fully loaded
    await waitForPageReady(page);

    // Use retry for more reliable testing
    await retry(
      async () => {
        // Check for header elements
        const header = page.locator('header');
        await expect(header).toBeVisible({
          timeout: browserName === 'firefox' ? 10000 : 5000,
        });

        // Check for logo - using a more specific selector
        // More flexible selector that matches any GetNinjas logo
        const logo = page.locator('header img[alt*="GetNinjas"], header img[alt*="Logo"]').first();
        await expect(logo).toBeVisible({
          timeout: browserName === 'firefox' ? 10000 : 5000,
        });

        // Check for navigation links with more flexible selectors
        // This will match any links in the header or navigation areas
        const navLinks = page.locator('header a, nav a, [role="navigation"] a');
        const count = await navLinks.count();
        expect(count).toBeGreaterThan(0);
      },
      {
        timeout: browserName === 'firefox' ? 40000 : 20000,
        interval: browserName === 'firefox' ? 2000 : 1000,
        errorMessage: 'Failed to find navigation elements',
      }
    );
  });

  test('should have footer with expected elements', async ({ page, browserName }) => {
    // Navigate to the homepage with longer timeout for Firefox
    await page.goto('/', { timeout: browserName === 'firefox' ? 60000 : 30000 });

    // Wait for the page to be fully loaded
    await waitForPageReady(page);

    // Use retry for more reliable testing
    await retry(
      async () => {
        // Check for footer with more flexible selector
        const footer = page.locator('footer, [role="contentinfo"]');
        await expect(footer).toBeVisible({
          timeout: browserName === 'firefox' ? 10000 : 5000,
        });

        // Check for copyright text with more flexible selector
        // This will match any text containing copyright symbols or text
        const copyright = page
          .locator('footer, [role="contentinfo"]')
          .getByText(/copyright|©|todos os direitos|reserved/i);
        await expect(copyright).toBeVisible({
          timeout: browserName === 'firefox' ? 10000 : 5000,
        });
      },
      {
        timeout: browserName === 'firefox' ? 40000 : 20000,
        interval: browserName === 'firefox' ? 2000 : 1000,
        errorMessage: 'Failed to find footer elements',
      }
    );
  });
});
