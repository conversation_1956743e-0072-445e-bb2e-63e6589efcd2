# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.pnpm-store/

# testing
/coverage
get-coverage.js
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
/e2e-results/
/test-downloads/
/test-uploads/
/artifacts/
/screenshots/

# next.js
/.next/
/out/

# production
/build
/dist

# misc
.DS_Store
*.pem
Thumbs.db

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
*.log

# local env files
.env*.local
.env
.env.development
.env.test
.env.production
# Keep example env file
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE specific files
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.vs/
*.sublime-workspace
*.sublime-project
.cursor/
.windsurfrules

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.swp
*.swo
*~
.temp/
.tmp/

# Generated files
/public/sitemap*.xml
/public/robots.txt
/public/sw.js
/public/workbox-*.js
/public/worker-*.js
/public/fallback-*.js
/public/precache-*.js
/public/service-worker.js

# Storybook
/storybook-static

# Bundle analyzer
/analyze/
copilot-instructions.md
.windsurf

# backup
/backup_images
