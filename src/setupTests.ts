// Setup file for Jest tests
import '@testing-library/jest-dom';

// Global Window interface is defined in global.d.ts

// Add a conditional fetch polyfill for tests
// @ts-ignore - TypeScript doesn't like our fetch polyfill
global.fetch = async (url: RequestInfo | URL, options?: RequestInit) => {
  const urlString = url.toString();

  // Allow real API calls for integration tests
  // Add all API endpoints that should be allowed for real API calls
  const allowedEndpoints = [
    'service-type/list',
    'service-type/by-slug',
    'order',
    'checkout',
    'payment',
  ];

  if (
    allowedEndpoints.some((endpoint) => urlString.includes(endpoint)) &&
    process.env.USE_REAL_API === 'true'
  ) {
    // Using real API call for integration tests
    try {
      // Use Node.js http/https module directly to bypass CORS completely
      // This is a more reliable approach in Jest environment
      const https = require('https');
      const url = new URL(urlString);

      // Define response type for TypeScript
      interface HttpResponse {
        status: number;
        statusText: string;
        data: Record<string, unknown>;
        headers: Record<string, string | string[]>;
      }

      // Create a promise to handle the async HTTP request
      const response = await new Promise<HttpResponse>((resolve, reject) => {
        const req = https.get(
          {
            hostname: url.hostname,
            path: url.pathname + url.search,
            headers: {
              ...(options?.headers as Record<string, string>),
              'service-provider': 'EUR',
              Accept: 'application/json',
            },
          },
          (res: {
            statusCode?: number;
            statusMessage?: string;
            headers: Record<string, string | string[]>;
            on: (event: string, callback: (chunk?: Buffer) => void) => void;
          }) => {
            let data = '';
            res.on('data', (chunk?: Buffer) => {
              if (chunk) {
                data += chunk.toString();
              }
            });
            res.on('end', () => {
              resolve({
                status: res.statusCode || 200,
                statusText: res.statusMessage || 'OK',
                data: JSON.parse(data),
                headers: res.headers,
              });
            });
          }
        );

        req.on('error', (error: Error) => {
          reject(error);
        });

        req.end();
      });

      const axiosResponse: HttpResponse = response;

      // Convert axios response to fetch response format
      return {
        ok: axiosResponse.status >= 200 && axiosResponse.status < 300,
        status: axiosResponse.status,
        statusText: axiosResponse.statusText,
        json: async () => axiosResponse.data,
        text: async () => JSON.stringify(axiosResponse.data),
        headers: new Headers(axiosResponse.headers as Record<string, string>),
      } as Response;
    } catch (error) {
      // Error with real API call - falling back to mock
      // Fall back to mock if real API call fails
    }
  }

  // Using polyfilled fetch for mock responses

  // Return a mock response for all other endpoints
  return {
    ok: true,
    status: 200,
    json: async () => {
      // Mock fetch called for testing

      // Return structured mock data that matches the API format
      if (urlString.includes('service-type/list')) {
        return {
          categories: [
            {
              id: 1,
              name: 'Test Category',
              slug: 'test-category',
              subcategories: [
                {
                  id: 101,
                  name: 'Test Subcategory',
                  slug: 'test-subcategory',
                  services: [
                    {
                      id: 1,
                      slug: 'test-service',
                      name: 'Test Service',
                      description: 'Description for Test Service',
                      imageUrl: '/images/test-service.jpg',
                      status: 'active',
                      price: {
                        priceId: 1,
                        originalPrice: 150,
                        discountPrice: 30,
                        finalPrice: 120,
                      },
                      provider: {
                        id: 1,
                        name: 'Test Provider',
                        imageUrl: '/provider-image.jpg',
                        providerUrl: '/provider/1',
                        description: 'Provider description',
                      },
                      availableIn: ['São Paulo', 'Rio de Janeiro'],
                      details: ['Detail 1-1', 'Detail 1-2'],
                      serviceLimits: 'Service limits information',
                      keywords: ['test', 'service'],
                    },
                  ],
                },
              ],
            },
          ],
        };
      }

      // Return empty data for other endpoints
      return {};
    },
  } as Response;
};

// Mock window.gtag for analytics testing
Object.defineProperty(window, 'gtag', {
  writable: true,
  value: jest.fn(),
});

// Mock window.dataLayer for analytics testing
Object.defineProperty(window, 'dataLayer', {
  writable: true,
  value: [],
});

// Mock window.tagManagerDataLayer for analytics testing
Object.defineProperty(window, 'tagManagerDataLayer', {
  writable: true,
  value: [],
});

// Mock window.fbq for Meta Pixel testing
Object.defineProperty(window, 'fbq', {
  writable: true,
  value: jest.fn(),
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Reset mocks between tests
beforeEach(() => {
  jest.clearAllMocks();
  window.dataLayer = [];
  window.tagManagerDataLayer = window.dataLayer;
});
