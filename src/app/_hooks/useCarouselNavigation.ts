'use client';

import { useEffect, useRef, useState } from 'react';

interface UseCarouselNavigationProps {
  dependencies?: unknown[];
}

export function useCarouselNavigation({ dependencies = [] }: UseCarouselNavigationProps = {}) {
  const carouselRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [showArrows, setShowArrows] = useState(true);

  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeftStart, setScrollLeftStart] = useState(0);

  // Atualiza os botões de scroll
  const updateScrollButtons = () => {
    if (!carouselRef.current) return;
    const { scrollLeft, scrollWidth, clientWidth } = carouselRef.current;
    const canScrollLeftValue = scrollLeft > 0;
    const canScrollRightValue = scrollLeft + clientWidth < scrollWidth;

    setCanScrollLeft(canScrollLeftValue);
    setCanScrollRight(canScrollRightValue);
    setShowArrows(canScrollLeftValue || canScrollRightValue);
  };

  const handleNext = () => {
    if (!carouselRef.current) return;

    // Calculate the scroll amount based on the card width + gap
    const scrollAmount = 232 + 16; // card width + gap

    // Use requestAnimationFrame for smoother scrolling
    requestAnimationFrame(() => {
      if (carouselRef.current) {
        carouselRef.current.scrollLeft += scrollAmount;
        updateScrollButtons();
      }
    });
  };

  const handlePrev = () => {
    if (!carouselRef.current) return;

    // Calculate the scroll amount based on the card width + gap
    const scrollAmount = 232 + 16; // card width + gap

    // Use requestAnimationFrame for smoother scrolling
    requestAnimationFrame(() => {
      if (carouselRef.current) {
        carouselRef.current.scrollLeft -= scrollAmount;
        updateScrollButtons();
      }
    });
  };

  // Toque inicial
  const handleTouchStart = (e: React.TouchEvent) => {
    if (!carouselRef.current) return;
    setIsDragging(true);
    setStartX(e.touches[0].clientX);
    setScrollLeftStart(carouselRef.current.scrollLeft);
  };

  // Movimento do toque com otimização de performance
  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging || !carouselRef.current) return;
    // Removed preventDefault() to avoid error with passive listeners
    const x = e.touches[0].clientX;
    const walk = startX - x;

    // Use requestAnimationFrame for smoother scrolling
    requestAnimationFrame(() => {
      if (carouselRef.current) {
        carouselRef.current.scrollLeft = scrollLeftStart + walk;
      }
    });
  };

  // Final do toque
  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    const carousel = carouselRef.current;
    if (!carousel) return;

    // Throttle the scroll event handler for better performance
    let scrollTimeout: NodeJS.Timeout;
    const throttledUpdateScrollButtons = () => {
      if (scrollTimeout) clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(updateScrollButtons, 100);
    };

    // Initial update
    updateScrollButtons();

    // Use passive event listeners for better performance
    carousel.addEventListener('scroll', throttledUpdateScrollButtons, { passive: true });
    window.addEventListener('resize', throttledUpdateScrollButtons, { passive: true });

    return () => {
      if (scrollTimeout) clearTimeout(scrollTimeout);
      carousel.removeEventListener('scroll', throttledUpdateScrollButtons);
      window.removeEventListener('resize', throttledUpdateScrollButtons);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencies);

  return {
    carouselRef,
    canScrollLeft,
    canScrollRight,
    showArrows,
    handleNext,
    handlePrev,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
  };
}
