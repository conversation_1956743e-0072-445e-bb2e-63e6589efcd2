'use client';

import { useCallback, useRef, useState } from 'react';

interface UseFormFocusReturn {
  focusedBlock: string | null;
  setFocusedBlock: (block: string | null) => void;
  getFocusProps: (blockId: string) => {
    onFocus: () => void;
    onBlur: () => void;
  };
  isFocused: (blockId: string) => boolean;
}

/**
 * Hook to manage form focus state across multiple form blocks
 *
 * This implementation is designed to be safe and handle edge cases
 */
export function useFormFocus(initialFocusedBlock: string | null = null): UseFormFocusReturn {
  const [focusedBlock, setFocusedBlockInternal] = useState<string | null>(initialFocusedBlock);

  // Use a ref to track the last update timestamp to prevent rapid updates
  const lastUpdateRef = useRef<number>(0);
  // Use a ref to track the last focused block for debugging
  const lastBlockRef = useRef<string | null>(null);

  // Helper function to handle focus block updates
  const updateFocusBlock = (block: string | null): void => {
    // Prevent processing if there's no change
    if (block === lastBlockRef.current) {
      return;
    }

    // Update last block ref
    lastBlockRef.current = block;

    // Rate limiting - only process state updates with sufficient time between
    const now = Date.now();
    if (now - lastUpdateRef.current < 50) {
      return; // Skip if updates are too rapid (less than 50ms apart)
    }

    // Update timestamp
    lastUpdateRef.current = now;

    // Update state
    setFocusedBlockInternal(block);
  };

  // Debounced setter to prevent rapid state changes
  const setFocusedBlock = useCallback((block: string | null) => {
    try {
      updateFocusBlock(block);
    } catch (_error) {
      // Silent error handling
    }
  }, []);

  // Helper function to handle focus events
  const handleFocus = useCallback(
    (blockId: string) => {
      try {
        setFocusedBlock(blockId);
      } catch (_error) {
        // Silent error handling
      }
    },
    [setFocusedBlock]
  );

  // Helper function to handle blur events
  const handleBlur = useCallback(() => {
    try {
      // Add delay to prevent quick focus/blur cycles
      setTimeout(() => {
        setFocusedBlock(null);
      }, 50);
    } catch (_error) {
      // Silent error handling
    }
  }, [setFocusedBlock]);

  // Safe focus props generator
  const getFocusProps = useCallback(
    (blockId: string) => ({
      onFocus: () => handleFocus(blockId),
      onBlur: () => handleBlur(),
    }),
    [handleFocus, handleBlur]
  );

  // Helper to check if a block is focused
  const isFocused = useCallback(
    (blockId: string): boolean => focusedBlock === blockId,
    [focusedBlock]
  );

  return {
    focusedBlock,
    setFocusedBlock,
    getFocusProps,
    isFocused,
  };
}
