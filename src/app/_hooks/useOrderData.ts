'use client';

import { ApiOrderResponse, UseOrderDataProps, UseOrderDataReturn } from '@/src/app/_interfaces';
import { OrderService } from '@/src/app/_services/order';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

export function orderQueryFn(orderId: string | null | undefined): Promise<ApiOrderResponse> {
  if (!orderId) {
    throw new Error('ID do pedido não encontrado nos parâmetros da URL');
  }
  return OrderService.getOrderByUuid(orderId);
}

export function useOrderData({
  orderId: initialOrderId,
  maxRetries = 5,
  retryDelay = 1000,
  initialDelay = 500,
}: UseOrderDataProps): UseOrderDataReturn {
  const [orderId, setOrderId] = useState<string | null>(initialOrderId);

  // Try to fetch from sessionStorage if not provided
  // This is a legitimate use case for sessionStorage as it allows the application
  // to remember the last order ID across page refreshes or navigation
  useEffect(() => {
    if (!initialOrderId) {
      try {
        const storedOrderId = sessionStorage.getItem('lastOrderId');
        if (storedOrderId) {
          setOrderId(storedOrderId);
        }
      } catch (error) {
        // Silently handle sessionStorage errors
        console.error('Error accessing sessionStorage:', error);
      }
    }
  }, [initialOrderId]);

  const {
    data: orderData,
    isLoading,
    error: queryError,
    failureCount,
  } = useQuery<ApiOrderResponse, Error>({
    queryKey: ['order', orderId],
    queryFn: async () => {
      return orderQueryFn(orderId);
    },
    enabled: !!orderId,
    retry: maxRetries,
    retryDelay: (attemptIndex) => {
      // First attempt uses initialDelay, subsequent attempts use retryDelay
      return attemptIndex === 0 ? initialDelay : retryDelay;
    },
    gcTime: 5 * 60 * 1000, // 5 minutes
    staleTime: 30 * 1000, // 30 seconds
  });

  // Determine error message based on query error and failure count
  const error = queryError
    ? failureCount >= maxRetries
      ? 'Não foi possível carregar os detalhes do pedido. Por favor, tente novamente mais tarde.'
      : queryError.message
    : null;

  return { orderData: orderData || null, isLoading, error };
}
