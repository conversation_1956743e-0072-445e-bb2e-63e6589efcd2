'use client';

import { RefObject, useCallback, useEffect, useState } from 'react';
import { UseSubmenuReturn } from '@/src/app/_interfaces';

export const useSubmenu = (submenuRef: RefObject<HTMLDivElement>): UseSubmenuReturn => {
  const [isSubmenuOpen, setIsSubmenuOpen] = useState<boolean>(false);

  const toggleSubmenu = () => setIsSubmenuOpen(!isSubmenuOpen);
  const handleMouseEnter = () => setIsSubmenuOpen(true);
  const handleMouseLeave = () => setIsSubmenuOpen(false);

  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      const servicesButton = document.querySelector('[data-trigger="Serviços"]');
      if (
        submenuRef.current &&
        !submenuRef.current.contains(event.target as Node) &&
        servicesButton &&
        !servicesButton.contains(event.target as Node)
      ) {
        setIsSubmenuOpen(false);
      }
    },
    [submenuRef]
  );

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [handleClickOutside]);

  return {
    isSubmenuOpen,
    toggleSubmenu,
    handleMouseEnter,
    handleMouseLeave,
    handleClickOutside,
  };
};
