'use client';

import { useState } from 'react';

interface ExpandableSection {
  id: string;
  isExpanded: boolean;
  toggle: () => void;
}

type ExpandableSectionsConfig = Record<string, boolean>;

/**
 * Custom hook for managing multiple expandable sections
 * @param initialState Object with section IDs as keys and initial expanded state as values
 * @returns Object with section IDs as keys and their state + toggle function
 */
export function useExpandableSections(
  initialState: ExpandableSectionsConfig
): Record<string, ExpandableSection> {
  // Initialize state from the provided configuration
  const [expandedState, setExpandedState] = useState<ExpandableSectionsConfig>(initialState);

  // Create a result object with each section's state and toggle function
  const sections = Object.keys(initialState).reduce(
    (acc, sectionId) => {
      const toggle = () => {
        setExpandedState((prev) => ({
          ...prev,
          [sectionId]: !prev[sectionId],
        }));
      };

      acc[sectionId] = {
        id: sectionId,
        isExpanded: expandedState[sectionId],
        toggle,
      };

      return acc;
    },
    {} as Record<string, ExpandableSection>
  );

  return sections;
}
