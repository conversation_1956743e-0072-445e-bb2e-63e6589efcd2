'use client';

import { UseMenuReturn } from '@/src/app/_interfaces';
import { useEffect, useState } from 'react';

export const useMenu = (): UseMenuReturn => {
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  useEffect(() => {
    if (isMenuOpen) {
      document.body.classList.add('menu-open');
    } else {
      document.body.classList.remove('menu-open');
    }

    return () => {
      document.body.classList.remove('menu-open');
    };
  }, [isMenuOpen]);

  return {
    isMenuOpen,
    toggleMenu,
  };
};
