'use client';

import { useCallback } from 'react';
import { UseInputFormatReturn } from '@/src/app/_interfaces';

/**
 * Hook for common input formatting patterns in forms
 */
export function useInputFormat(): UseInputFormatReturn {
  /**
   * Formats a CEP (Brazilian postal code) string (e.g., 12345-678)
   */
  const formatCep = useCallback((value: string): string => {
    const digitsOnly = value.replace(/\D/g, '');

    if (digitsOnly.length <= 5) {
      return digitsOnly;
    }

    return `${digitsOnly.slice(0, 5)}-${digitsOnly.slice(5, 8)}`;
  }, []);

  /**
   * Strips non-digit characters from a string
   */
  const formatNumbersOnly = useCallback((value: string): string => {
    return value.replace(/\D/g, '');
  }, []);

  /**
   * Capitalizes words in a string
   */
  const capitalizeWords = (str: string): string => {
    if (!str) return str;

    return str
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  /**
   * Formats a CPF (Brazilian tax ID) string (e.g., 123.456.789-00)
   */
  const formatCpf = useCallback((value: string): string => {
    const digitsOnly = value.replace(/\D/g, '');

    if (digitsOnly.length <= 3) {
      return digitsOnly;
    }

    if (digitsOnly.length <= 6) {
      return `${digitsOnly.slice(0, 3)}.${digitsOnly.slice(3)}`;
    }

    if (digitsOnly.length <= 9) {
      return `${digitsOnly.slice(0, 3)}.${digitsOnly.slice(3, 6)}.${digitsOnly.slice(6)}`;
    }

    return `${digitsOnly.slice(0, 3)}.${digitsOnly.slice(
      3,
      6
    )}.${digitsOnly.slice(6, 9)}-${digitsOnly.slice(9, 11)}`;
  }, []);

  return {
    formatCep,
    formatNumbersOnly,
    capitalizeWords,
    formatCpf,
  };
}
