'use client';

import { getUtmParams } from '@/src/app/_functions/analytics/common';
import { ServiceType } from '@/src/app/_interfaces';
import { trackMixpanelEvent } from '@/src/app/_lib/initMixpanel';

// Helper function to create item data object
function createItemData(service: ServiceType) {
  try {
    return {
      item_id: service.id || 0,
      item_name: service.name || '',
      price: service.price?.finalPrice || 0,
      quantity: 1,
    };
  } catch (error) {
    console.error('Error creating item data:', error);
    return {
      item_id: 0,
      item_name: '',
      price: 0,
      quantity: 1,
    };
  }
}

import { hasAnalyticsConsent } from '@/src/app/_lib/hasAnalyticsConsent';

export const useHandleAddToCart = () => {
  const handleAddToCart = (service: ServiceType) => {
    try {
      if (typeof window === 'undefined') return;
      if (!service) return;

      const utmData = getUtmParams();

      // Initialize dataLayer and tagManagerDataLayer
      window.dataLayer = window.dataLayer || [];
      window.tagManagerDataLayer = window.tagManagerDataLayer || window.dataLayer;

      // Create item data
      const item = createItemData(service);
      const price = service.price?.finalPrice || 0;

      // Google Analytics (GTM)
      try {
        const eventData = {
          event: 'add_to_cart',
          ecommerce: {
            currency: 'BRL',
            value: price,
            items: [item],
          },
          utm_data: utmData,
        };

        // Push to dataLayer
        window.dataLayer.push(eventData);

        // Push to tagManagerDataLayer if it's different from dataLayer
        if (window.tagManagerDataLayer !== window.dataLayer) {
          window.tagManagerDataLayer.push(eventData);
        }
      } catch (error) {
        // Silently handle dataLayer errors
        console.error('Error pushing to dataLayer:', error);
      }

      // Google Analytics 4
      if (typeof window.gtag === 'function') {
        try {
          window.gtag('event', 'add_to_cart', {
            currency: 'BRL',
            value: price,
            items: [item],
          });
        } catch (error) {
          // Silently handle gtag errors
          console.error('Error sending event to gtag:', error);
        }
      }

      // Meta Pixel
      if (typeof window.fbq === 'function' && hasAnalyticsConsent()) {
        try {
          window.fbq('track', 'AddToCart', {
            content_ids: [service.id || 0],
            content_name: service.name || '',
            content_type: 'product',
            value: price,
            currency: 'BRL',
            ...utmData,
          });
        } catch (error) {
          // Silently handle fbq errors
          console.error('Error sending event to fbq:', error);
        }
      }

      // Mixpanel - using the safe tracking function
      if (hasAnalyticsConsent()) {
        try {
          trackMixpanelEvent('Add To Cart', {
            ...item,
            ...utmData,
          });
        } catch (error) {
          console.error('Error tracking event in Mixpanel:', error);
        }
      }
    } catch (error) {
      // Catch any unexpected errors
      console.error('Unexpected error in handleAddToCart:', error);
    }
  };

  return { handleAddToCart };
};
