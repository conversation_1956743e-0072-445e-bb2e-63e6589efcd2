'use client';

/**
 * Custom hook for sending analytics events to Google Analytics and dataLayer
 * @returns Object containing the sendEvent function
 */
export function useAnalyticsEventGeneric() {
  /**
   * Sends an analytics event to dataLayer and Google Analytics
   * @param eventName - The name of the event to track
   * @param payload - Optional payload data to include with the event
   */
  const sendEvent = (eventName: string, payload: Record<string, unknown> = {}) => {
    // Skip if running on server
    if (typeof window === 'undefined') {
      return;
    }

    // Skip if eventName is not provided
    if (!eventName) {
      console.warn('Event name is required for analytics tracking');
      return;
    }

    // Create the event object
    const eventObject = {
      event: eventName,
      ...payload,
    };

    // Handle dataLayer
    sendToDataLayer(eventObject);

    // Handle Google Analytics
    sendToGoogleAnalytics(eventName, payload);
  };

  /**
   * Sends event data to the dataLayer
   * @param eventObject - The event object to send to dataLayer
   */
  const sendToDataLayer = (eventObject: Record<string, unknown>) => {
    try {
      // Initialize dataLayer as an array if it doesn't exist
      if (!window.dataLayer) {
        window.dataLayer = [];
      }

      // Initialize tagManagerDataLayer, pointing to dataLayer if not already defined
      window.tagManagerDataLayer = window.tagManagerDataLayer || window.dataLayer;

      // Push event to dataLayer if it's an array with a push method
      if (Array.isArray(window.dataLayer) && typeof window.dataLayer.push === 'function') {
        window.dataLayer.push(eventObject);

        // Push to tagManagerDataLayer if it's different from dataLayer
        if (
          window.tagManagerDataLayer !== window.dataLayer &&
          Array.isArray(window.tagManagerDataLayer) &&
          typeof window.tagManagerDataLayer.push === 'function'
        ) {
          window.tagManagerDataLayer.push(eventObject);
        }
      } else {
        // If dataLayer is not an array or push is not a function, initialize dataLayer properly
        window.dataLayer = [eventObject];
        window.tagManagerDataLayer = window.dataLayer;
      }
    } catch (error) {
      // Silently handle any errors to prevent breaking the application
      console.error('Error sending analytics event:', error);
    }
  };

  /**
   * Sends event data to Google Analytics via gtag
   * @param eventName - The name of the event to track
   * @param payload - The payload data to include with the event
   */
  const sendToGoogleAnalytics = (eventName: string, payload: Record<string, unknown>) => {
    try {
      if (typeof window.gtag === 'function') {
        window.gtag('event', eventName, payload);
      }
    } catch (error) {
      // Silently handle any errors to prevent breaking the application
      console.error('Error sending gtag event:', error);
    }
  };

  return { sendEvent };
}
