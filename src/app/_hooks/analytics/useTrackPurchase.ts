'use client';

import { ApiOrderResponse } from '@/src/app/_interfaces/order';
import { trackMixpanelEvent } from '@/src/app/_lib/initMixpanel';

import { hasAnalyticsConsent } from '@/src/app/_lib/hasAnalyticsConsent';

export function useTrackPurchaseEvent() {
  const trackPurchaseEvent = (orderData: ApiOrderResponse, orderId?: string) => {
    try {
      if (!orderData || typeof window === 'undefined') return;

      const service = orderData.service;
      const price = service?.price?.finalPrice;

      // Use the provided orderId as transaction_id to prevent duplicate events
      const transactionId = orderId || '';

      if (!service || !price) return;

      // Initialize dataLayer and tagManagerDataLayer as arrays if they don't exist or aren't arrays
      if (!window.dataLayer || !Array.isArray(window.dataLayer)) {
        window.dataLayer = [];
      }

      // Initialize tagManagerDataLayer, pointing to dataLayer if not already defined
      window.tagManagerDataLayer = window.tagManagerDataLayer || window.dataLayer;

      // Check for homol environment
      const analyticsEnvironment = process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT;
      const eventName = analyticsEnvironment === 'homol' ? 'purchase-homol' : 'purchase';

      // Google Tag Manager
      try {
        const eventData = {
          event: eventName,
          ecommerce: {
            transaction_id: transactionId,
            currency: 'BRL',
            value: price,
            items: [
              {
                item_id: service.id,
                item_name: service.name,
                price,
                quantity: 1,
              },
            ],
          },
        };

        // Push to dataLayer
        window.dataLayer.push(eventData);

        // Push to tagManagerDataLayer if it's different from dataLayer
        if (window.tagManagerDataLayer !== window.dataLayer) {
          window.tagManagerDataLayer.push(eventData);
        }
      } catch (error) {
        // Silently handle dataLayer errors
        console.error('Error pushing to dataLayer:', error);
      }

      // Google Analytics 4
      if (typeof window.gtag === 'function') {
        try {
          window.gtag('event', eventName, {
            transaction_id: transactionId,
            currency: 'BRL',
            value: price,
            items: [
              {
                item_id: service.id,
                item_name: service.name,
                price,
                quantity: 1,
              },
            ],
          });
        } catch (error) {
          // Silently handle gtag errors
          console.error('Error sending event to gtag:', error);
        }
      }

      // Meta Pixel (Facebook)
      if (typeof window.fbq === 'function' && hasAnalyticsConsent()) {
        try {
          window.fbq('track', 'Purchase', {
            content_ids: [service.id],
            content_name: service.name,
            content_type: 'product',
            value: price,
            currency: 'BRL',
            transaction_id: transactionId,
          });
        } catch (error) {
          // Silently handle fbq errors
          console.error('Error sending event to fbq:', error);
        }
      }

      // Mixpanel - using the safe tracking function
      // Add distinct_id (customer email) if available, per Mixpanel best practices
      const eventProps: Record<string, unknown> = {
        item_id: service.id,
        item_name: service.name,
        price,
        quantity: 1,
        currency: 'BRL',
        transaction_id: transactionId,
      };
      if (orderData.customer?.email) {
        eventProps.distinct_id = orderData.customer.email;
      }
      if (hasAnalyticsConsent()) {
        trackMixpanelEvent('Purchase', eventProps);
      }
    } catch (error) {
      // Catch any unexpected errors
      console.error('Unexpected error in trackPurchaseEvent:', error);
    }
  };

  return { trackPurchaseEvent };
}
