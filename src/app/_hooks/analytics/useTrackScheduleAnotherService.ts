'use client';

export function useTrackScheduleAnotherServiceClick() {
  const trackScheduleAnotherServiceClick = () => {
    try {
      if (typeof window === 'undefined') return;

      // Initialize dataLayer and tagManagerDataLayer as arrays if they don't exist or aren't arrays
      if (!window.dataLayer || !Array.isArray(window.dataLayer)) {
        window.dataLayer = [];
      }

      // Initialize tagManagerDataLayer, pointing to dataLayer if not already defined
      window.tagManagerDataLayer = window.tagManagerDataLayer || window.dataLayer;

      try {
        const eventData = {
          event: 'schedule_another_service_click',
          event_category: 'Success Page',
          event_label: 'Agendar outro serviço',
        };

        // Push to dataLayer
        window.dataLayer.push(eventData);

        // Push to tagManagerDataLayer if it's different from dataLayer
        if (window.tagManagerDataLayer !== window.dataLayer) {
          window.tagManagerDataLayer.push(eventData);
        }
      } catch (error) {
        // Silently handle dataLayer errors
        console.error('Error pushing to dataLayer:', error);
      }
    } catch (error) {
      // Catch any unexpected errors
      console.error('Unexpected error in trackScheduleAnotherServiceClick:', error);
    }
  };

  return { trackScheduleAnotherServiceClick };
}
