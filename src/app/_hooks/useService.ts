import type { ServiceType } from '@/src/app/_interfaces';
import { axiosInstance } from '@/src/app/_utils';
import { useQuery } from '@tanstack/react-query';

async function fetchService(id: string) {
  const response = await axiosInstance.get(`/service-type/${id}`);
  return response.data;
}

export function useService(id: string) {
  const {
    data: service,
    isLoading,
    error,
  } = useQuery<ServiceType, Error>({
    queryKey: ['service', id],
    queryFn: () => fetchService(id),
    enabled: !!id,
  });

  return {
    service: service ?? null,
    isLoading,
    error: error?.message ?? null,
  };
}
