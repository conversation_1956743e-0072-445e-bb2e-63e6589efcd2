'use client';

import { debounce } from '@/src/app/_utils/debounce';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import { checkoutFormSchema, type CheckoutFormSchema } from '../_utils/formValidation';

export function useCheckoutForm() {
  const [focusedBlock, setFocusedBlock] = useState<string | null>(null);

  const form = useForm<CheckoutFormSchema>({
    resolver: zodResolver(checkoutFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      countryCode: '+55',
      phone: '',
      cpf: '',
      email: '',
      cep: '',
      street: '',
      streetNumber: '',
      complement: '',
      neighborhood: '',
      city: '',
      state: '',
      date: undefined,
      period: undefined,
      terms: false,
    },
  });

  const debouncedValidation = useCallback(() => {
    return debounce((fieldName: keyof CheckoutFormSchema) => {
      form.trigger(fieldName);
    }, 300);
  }, [form]);

  const isFormValid = useCallback(() => {
    const {
      firstName,
      lastName,
      date,
      period,
      cep,
      street,
      streetNumber,
      neighborhood,
      city,
      state,
      phone,
      cpf,
      terms,
    } = form.getValues();
    const allFieldsFilled =
      firstName &&
      lastName &&
      date &&
      period &&
      cep &&
      street &&
      streetNumber &&
      neighborhood &&
      city &&
      state &&
      phone &&
      cpf &&
      terms;
    return allFieldsFilled && form.formState.isValid;
  }, [form]);

  return {
    form,
    focusedBlock,
    setFocusedBlock,
    debouncedValidation,
    isFormValid,
  };
}
