'use client';

import type { ScheduleFormProps } from '@/src/app/_interfaces';
import type { CheckoutFormSchema } from '@/src/app/_utils';
import { isAfternoonPeriodAvailable, isMorningPeriodAvailable } from '@/src/app/_utils/dateUtils';
import { useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';

/**
 * Custom hook to manage scheduling logic and state
 * @param props - Optional props to initialize the hook state
 */
export function useScheduling(props?: Partial<ScheduleFormProps>) {
  const form = useFormContext<CheckoutFormSchema>();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(form.getValues('date'));
  const [isMorningAvailable, setIsMorningAvailable] = useState<boolean>(false);
  const [isAfternoonAvailable, setIsAfternoonAvailable] = useState<boolean>(false);
  const [isDatePickerVisible, setIsDatePickerVisible] = useState(
    props?.isDatePickerVisible || false
  );
  const [focusedBlock, setFocusedBlock] = useState<string | null>(props?.focusedBlock || null);

  // Sync with external props when they change
  useEffect(() => {
    if (props?.isDatePickerVisible !== undefined) {
      setIsDatePickerVisible(props.isDatePickerVisible);
    }
  }, [props?.isDatePickerVisible]);

  useEffect(() => {
    if (props?.focusedBlock !== undefined) {
      setFocusedBlock(props.focusedBlock);
    }
  }, [props?.focusedBlock]);

  // Update period availability when date changes
  useEffect(() => {
    if (!selectedDate) {
      setIsMorningAvailable(false);
      setIsAfternoonAvailable(false);
      return;
    }

    const morningAvailable = isMorningPeriodAvailable(selectedDate);
    const afternoonAvailable = isAfternoonPeriodAvailable(selectedDate);

    setIsMorningAvailable(morningAvailable);
    setIsAfternoonAvailable(afternoonAvailable);

    // If current period is no longer available, clear the selection
    const currentPeriod = form.getValues('period');

    if (currentPeriod === 'morning' && !morningAvailable) {
      // Clear the selection but don't reset to undefined to avoid type error
      if (afternoonAvailable) {
        form.setValue('period', 'afternoon');
      } else {
        // If neither option is available, we'll need to select one by default
        // and disable both in the UI
        form.setValue('period', 'morning');
      }
    } else if (currentPeriod === 'afternoon' && !afternoonAvailable) {
      if (morningAvailable) {
        form.setValue('period', 'morning');
      } else {
        form.setValue('period', 'afternoon');
      }
    }

    // If no period is selected yet but one is available, auto-select it
    if (!currentPeriod) {
      if (morningAvailable) {
        form.setValue('period', 'morning');
      } else if (afternoonAvailable) {
        form.setValue('period', 'afternoon');
      } else {
        // If neither is available, default to morning (UI will show disabled)
        form.setValue('period', 'morning');
      }
    }
  }, [selectedDate, form]);

  const handleFocus = () => {
    setFocusedBlock('schedule');

    // Sync with external callback if provided
    if (props?.setFocusedBlock) {
      props.setFocusedBlock('schedule');
    }
  };

  const handleBlur = () => {
    setFocusedBlock(null);

    // Sync with external callback if provided
    if (props?.setFocusedBlock) {
      props.setFocusedBlock(null);
    }
  };

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      form.setValue('date', date);
      setSelectedDate(date);
      form.trigger('date');
    }
  };

  const handlePeriodChange = (value: string) => {
    form.setValue('period', value as 'morning' | 'afternoon');
    form.trigger('period');
  };

  // Update external state via callback
  useEffect(() => {
    if (props?.setIsDatePickerVisible && isDatePickerVisible !== props.isDatePickerVisible) {
      props.setIsDatePickerVisible(isDatePickerVisible);
    }
  }, [isDatePickerVisible, props?.isDatePickerVisible, props?.setIsDatePickerVisible, props]);

  return {
    // State
    selectedDate,
    isMorningAvailable,
    isAfternoonAvailable,
    isDatePickerVisible,
    setIsDatePickerVisible,
    focusedBlock,
    setFocusedBlock,

    // Event handlers
    handleFocus,
    handleBlur,
    handleDateChange,
    handlePeriodChange,
  };
}
