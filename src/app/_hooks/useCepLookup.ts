'use client';

import { CepResponse } from '@/src/app/_interfaces';
import { axiosInstance } from '@/src/app/_utils';
import type { CheckoutFormSchema } from '@/src/app/_utils/formValidation';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useCallback, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';

// Helper function to format error messages
const formatErrorMessage = (error: unknown): string | null => {
  // If no error, return null
  if (!error) return null;

  // Handle Axios errors
  if (error instanceof AxiosError) {
    // Handle status code 400
    if (error.response?.status === 400) {
      // Get error message from response
      const errorMessage = error.response.data?.message || '';

      // Check if service is not available for the region
      if (errorMessage.includes('O serviço não está disponível para esta região')) {
        return 'Esse serviço não está disponível para este CEP.';
      }

      // Return the error message from the response or a default message
      return errorMessage || 'CEP inválido. Verifique os números digitados.';
    }

    // For other status codes, return the error message
    return error.message;
  }

  // Handle standard Error objects
  if (error instanceof Error) {
    return error.message;
  }

  // Default error message for unknown error types
  return 'Erro ao buscar CEP';
};

// Function to fetch address data by CEP
const fetchAddressByCep = async (cep: string) => {
  // Remove any non-digit characters from CEP
  const cleanCep = cep ? cep.replace(/\D/g, '') : '';

  // Validate CEP format
  if (!cleanCep || cleanCep.length !== 8) {
    throw new Error('CEP inválido. O CEP deve conter 8 dígitos.');
  }

  try {
    // Fetch address data
    const response = await axiosInstance.get<CepResponse>(`/api/address/${cleanCep}`);
    const data = response.data;

    // Validate response data has required fields
    const hasRequiredFields =
      Boolean(data.street) &&
      Boolean(data.cityName) &&
      Boolean(data.neighborhood) &&
      Boolean(data.uf);

    if (!hasRequiredFields) {
      throw new Error('Não foi possível obter os dados completos do endereço para este CEP.');
    }

    return data;
  } catch (error) {
    // Rethrow the error to be handled by the caller
    throw error;
  }
};

export function useCepLookup(options?: { onSuccessOverride?: (data: unknown) => void }) {
  const form = useFormContext<CheckoutFormSchema>();
  const [fieldsFilledByCep, setFieldsFilledByCep] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Use TanStack Query mutation for CEP lookup
  const {
    mutate: mutateCep,
    isPending: isLoading,
    error: mutationError,
    reset: resetMutation,
  } = useMutation({
    mutationFn: fetchAddressByCep,
    onSuccess:
      options?.onSuccessOverride ||
      ((data) => {
        // Ensure we have valid data
        if (!data) {
          return;
        }

        // Update form fields with the response data
        form.setValue('street', data.street, { shouldValidate: true });
        form.setValue('city', data.cityName, { shouldValidate: true });
        form.setValue('neighborhood', data.neighborhood, { shouldValidate: true });
        form.setValue('state', data.uf, { shouldValidate: true });

        // Set fields as filled by CEP
        setFieldsFilledByCep(true);

        // Focus on the street number field after auto-fill
        setTimeout(() => {
          const streetNumberInput = document.querySelector('input[name="streetNumber"]');
          if (streetNumberInput instanceof HTMLInputElement) {
            streetNumberInput.focus();
          }
        }, 100);
      }),
    onError: (error) => {
      console.error('Erro ao buscar CEP:', error);
      setFieldsFilledByCep(false);
    },
  });

  // Format error message
  const error = formatErrorMessage(mutationError);

  // Wrapper function to handle CEP lookup
  const lookupCep = useCallback(
    (cep: string) => {
      // Skip if CEP is empty or invalid
      const cleanCep = cep ? cep.replace(/\D/g, '') : '';
      const isValidCep = cleanCep.length === 8;

      if (!isValidCep) {
        setFieldsFilledByCep(false);
        return;
      }

      // Cancel any ongoing requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }

      // Reset previous errors
      resetMutation();

      // Create a new abort controller for this request
      abortControllerRef.current = new AbortController();

      // Execute the mutation
      mutateCep(cleanCep);
    },
    [mutateCep, resetMutation]
  );

  const resetCepFillStatus = useCallback(() => {
    setFieldsFilledByCep(false);
    resetMutation();
  }, [resetMutation]);

  return { lookupCep, isLoading, error, fieldsFilledByCep, resetCepFillStatus };
}
