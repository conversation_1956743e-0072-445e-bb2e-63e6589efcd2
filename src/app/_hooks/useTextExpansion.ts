'use client';

import { useState, useCallback } from 'react';
import { UseTextExpansionOptions, UseTextExpansionReturn } from '@/src/app/_interfaces';

export function useTextExpansion({
  truncateThreshold = 250,
  charsPerLine = 18,
}: UseTextExpansionOptions = {}): UseTextExpansionReturn {
  const [showFullText, setShowFullText] = useState(false);

  const toggleTextExpansion = useCallback(() => {
    setShowFullText((prev) => !prev);
  }, []);

  // Text truncation helper
  const truncateText = useCallback(
    (text: string | undefined, maxLines = 5) => {
      if (!text) return '';

      const maxChars = maxLines * charsPerLine;
      if (text.length <= maxChars) return text;
      return text.substring(0, maxChars) + '...';
    },
    [charsPerLine]
  );

  // Helper to format text from either string or string[]
  const formatText = useCallback(
    (text: string | string[] | undefined, defaultText: string): string => {
      if (!text) return defaultText;
      return Array.isArray(text) ? text.join('\n') : text;
    },
    []
  );

  // Helper to check if text needs an expand button
  const shouldShowExpandButton = useCallback(
    (text: string | string[] | undefined): boolean => {
      if (!text) return false;
      const formattedText = formatText(text, '');
      return formattedText.length > truncateThreshold;
    },
    [formatText, truncateThreshold]
  );

  return {
    showFullText,
    toggleTextExpansion,
    truncateText,
    formatText,
    shouldShowExpandButton,
  };
}
