'use client';

import { useServiceContext } from '@/src/app/_context/ServiceContext';
import type { Provider, ServiceType } from '@/src/app/_interfaces';
import { ServiceApiService } from '@/src/app/_services/serviceApi';
import { useEffect, useRef, useState } from 'react';

export interface UseServiceBySlugResult {
  service: ServiceType | null;
  provider: Provider | null;
  isLoading: boolean;
  error: string | null;
  isError: boolean;
}

interface ServiceData {
  service: ServiceType;
  provider: Provider;
}

export function useServiceBySlug(slug: string, initialData?: ServiceData): UseServiceBySlugResult {
  const { services } = useServiceContext();
  const initialDataProcessed = useRef(false);
  const slugRef = useRef(slug);
  const serviceProcessed = useRef(false);

  // Initialize state with initialData if available
  const [result, setResult] = useState<UseServiceBySlugResult>({
    service: initialData?.service || null,
    provider: initialData?.provider || null,
    isLoading: !initialData,
    error: null,
    isError: false,
  });

  // Effect to handle initialData - runs only once
  useEffect(() => {
    if (initialData && !initialDataProcessed.current) {
      initialDataProcessed.current = true;
      setResult({
        service: initialData.service,
        provider: initialData.provider,
        isLoading: false,
        error: null,
        isError: false,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Effect to handle slug changes
  useEffect(() => {
    // Skip if we've already processed this slug
    if (slug === slugRef.current && serviceProcessed.current) {
      return;
    }

    // Update the slug ref and reset the processed flag
    if (slugRef.current !== slug) {
      slugRef.current = slug;
      serviceProcessed.current = false;
    }

    // If no slug, return early
    if (!slug) {
      setResult({
        service: null,
        provider: null,
        isLoading: false,
        error: null,
        isError: false,
      });
      return;
    }

    // Set loading state
    setResult((prev) => ({ ...prev, isLoading: true }));

    // Try to find the service in the context
    if (services && Array.isArray(services)) {
      let foundService = null;
      let foundCategory = null;
      let foundSubcategory = null;

      // Search through all categories and subcategories
      for (const category of services) {
        for (const subcategory of category.subcategories) {
          const service = subcategory.services.find((s) => s.slug === slug);
          if (service) {
            foundService = service;
            foundCategory = category;
            foundSubcategory = subcategory;
            break;
          }
        }
        if (foundService) break;
      }

      if (foundService && foundCategory && foundSubcategory) {
        // Convert to ServiceType
        const serviceType = ServiceApiService.convertServiceToServiceType({
          ...foundService,
          categoryName: foundCategory.name,
          categorySlug: foundCategory.slug,
          subcategoryName: foundSubcategory.name,
          subcategorySlug: foundSubcategory.slug,
        });

        // Create provider object
        const provider: Provider = {
          id: foundService.provider.id.toString(),
          name: foundService.provider.name,
          description: foundService.provider.description || '',
          imageUrl: foundService.provider.imageUrl || '',
          testimonials: [],
          providerUrl: foundService.provider.providerUrl,
        };

        setResult({
          service: serviceType,
          provider,
          isLoading: false,
          error: null,
          isError: false,
        });

        // Mark as processed
        serviceProcessed.current = true;
      } else {
        // If service not found in context, show error
        setResult({
          service: null,
          provider: null,
          isLoading: false,
          error: `Service with slug "${slug}" not found`,
          isError: true,
        });

        // Mark as processed even though we didn't find the service
        serviceProcessed.current = true;
      }
    }
  }, [slug, services]);

  return result;
}
