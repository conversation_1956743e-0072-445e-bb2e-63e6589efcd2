'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/src/app/_components';
import { AlertOctagonIcon } from 'lucide-react';
import React from 'react';

interface CheckoutErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRetry: () => void;
  errorType?: 'generic' | 'payment' | 'validation' | 'server';
  errorMessage?: string;
}

export function CheckoutErrorModal({
  isOpen,
  onClose,
  onRetry,
  errorType = 'generic',
  errorMessage = '',
}: CheckoutErrorModalProps) {
  const analyticsEnvironment = process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT; // Verifica o ambiente de analytics

  // Função para rastrear o clique de Tentar Novamente
  const handleRetryClick = () => {
    const eventName = analyticsEnvironment === 'homol' ? 'retry_click-homol' : 'retry_click';

    window.dataLayer = window.dataLayer || [];
    window.tagManagerDataLayer = window.tagManagerDataLayer || window.dataLayer;

    const eventData = {
      event: eventName,
      action: 'Tentar Novamente',
      timestamp_micros: performance.now() * 1000 + performance.timeOrigin * 1000,
    };

    // Push to dataLayer
    window.dataLayer.push(eventData);

    // Push to tagManagerDataLayer if it's different from dataLayer
    if (window.tagManagerDataLayer !== window.dataLayer) {
      window.tagManagerDataLayer.push(eventData);
    }

    // Chama a função de retry que foi passada como prop
    onRetry();
  };

  // Função para rastrear o clique de Voltar ao agendamento
  const handleBackToSchedulingClick = () => {
    const eventName =
      analyticsEnvironment === 'homol'
        ? 'back_to_scheduling_click-homol'
        : 'back_to_scheduling_click';

    window.dataLayer = window.dataLayer || [];
    window.tagManagerDataLayer = window.tagManagerDataLayer || window.dataLayer;

    const eventData = {
      event: eventName,
      action: 'Voltar ao agendamento',
      timestamp_micros: performance.now() * 1000 + performance.timeOrigin * 1000,
    };

    // Push to dataLayer
    window.dataLayer.push(eventData);

    // Push to tagManagerDataLayer if it's different from dataLayer
    if (window.tagManagerDataLayer !== window.dataLayer) {
      window.tagManagerDataLayer.push(eventData);
    }

    // Chama a função de fechar que foi passada como prop
    onClose();
  };

  // Get the title based on error type
  const getErrorTitle = () => {
    switch (errorType) {
      case 'payment':
        return 'Erro no pagamento';
      case 'validation':
        return 'Erro de validação';
      case 'server':
        return 'Erro no servidor';
      case 'generic':
        return 'Ocorreu um erro';
      default:
        return 'Agendamento não realizado.';
    }
  };

  // Get the message based on error type and provided message
  const getErrorMessage = () => {
    if (errorMessage) {
      return errorMessage;
    }

    return 'Há uma instabilidade temporária em nossos serviços no momento.\nTente novamente em alguns instantes.';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-4/5 rounded-lg border-2 border-red-500 sm:max-w-md">
        <DialogHeader className="flex flex-col items-start text-left sm:flex-row">
          <div className="mb-4 flex h-10 w-10 items-center justify-start rounded-full">
            <AlertOctagonIcon className="h-8 w-8 text-red-500" />
          </div>
          <DialogTitle className="text-left text-xl font-semibold">{getErrorTitle()}</DialogTitle>
        </DialogHeader>
        <div className="text-left text-sm text-muted-foreground">
          <p>
            {getErrorMessage()
              .split('\n')
              .map((line, i) => (
                <React.Fragment key={i}>
                  {line}
                  {i < getErrorMessage().split('\n').length - 1 && <br />}
                </React.Fragment>
              ))}
          </p>
        </div>
        <DialogFooter className="mt-4 flex flex-col gap-2 px-4 sm:flex-row sm:justify-center sm:px-2">
          <Button
            onClick={handleRetryClick} // Rastreia o clique do "Tentar novamente"
            className="order-1 w-full bg-primary text-white hover:bg-primary/90 sm:order-2 sm:w-auto"
          >
            Tentar novamente
          </Button>
          <Button
            variant="outline"
            onClick={handleBackToSchedulingClick} // Rastreia o clique do "Voltar ao agendamento"
            className="order-2 w-full sm:order-1 sm:w-auto"
          >
            Voltar ao agendamento
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
