'use client';

import { MapPinIcon as MapPinFilled } from 'lucide-react';
import { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/src/app/_components';

import { useCepLookup, useInputFormat } from '@/src/app/_hooks';
import { cn } from '@/src/app/_utils/utils';
import { Loader2 } from 'lucide-react';

import { states } from '@/src/app/_data/states';
import type { AddressFormProps } from '@/src/app/_interfaces/checkout';
import type { CheckoutFormSchema } from '@/src/app/_utils/formValidation';

export function AddressForm({
  service,
  isStateSelectVisible,
  setIsStateSelectOpen,
  focusedBlock,
  setFocusedBlock,
}: AddressFormProps) {
  const form = useFormContext<CheckoutFormSchema>();
  const { lookupCep, isLoading, error, fieldsFilledByCep, resetCepFillStatus } = useCepLookup();
  const { formatCep, formatNumbersOnly } = useInputFormat();

  // UseEffect para tirar o blur dos inputs ao realizar o scroll
  useEffect(() => {
    const handleScroll = () => {
      if (document.activeElement instanceof HTMLElement) {
        document.activeElement.blur(); // Remove o foco do input ativo
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const handleFocus = () => {
    if (typeof setFocusedBlock === 'function') {
      setFocusedBlock('address');
    }
  };

  const handleBlur = () => {
    if (typeof setFocusedBlock === 'function') {
      setFocusedBlock(null);
    }
  };

  return (
    <Card
      className={cn(
        'transition-shadow duration-300',
        (focusedBlock === 'address' || isStateSelectVisible) &&
          'shadow-lg ring-2 ring-primary ring-opacity-50'
      )}
    >
      <CardHeader className="px-8">
        <div className="flex flex-col gap-2 md:flex-row md:items-center">
          <MapPinFilled className="h-5 w-5 flex-shrink-0 text-primary" strokeWidth={3} />
          <CardTitle className="text-lg font-bold leading-tight">
            Onde o serviço será prestado?
          </CardTitle>
        </div>
        <p className="mt-2 text-sm text-muted-foreground">
          {service.name} está disponível apenas para: &nbsp;
          <b>São Paulo, Rio de Janeiro e Minas Gerais</b>
        </p>
      </CardHeader>
      <CardContent className="space-y-4 px-8 pb-8">
        <FormField
          control={form.control}
          name="cep"
          render={({ field }) => (
            <FormItem>
              <FormLabel>CEP</FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    placeholder="00000-000"
                    {...field}
                    required
                    value={field.value || ''}
                    onFocus={handleFocus}
                    onBlur={async (e) => {
                      field.onChange(e.target.value);
                      handleBlur();
                      await form.trigger('cep');
                      if (!form.formState.errors.cep) {
                        await lookupCep(field.value || '');
                      }
                    }}
                    onChange={(e) => {
                      const value = formatCep(e.target.value);
                      field.onChange(value);

                      // Reset CEP fill status if CEP is cleared or changed significantly
                      if (value.length <= 5) {
                        resetCepFillStatus();
                      }

                      form.trigger('cep');
                    }}
                    className={cn(form.formState.errors.cep && 'input-error', isLoading && 'pr-10')}
                    aria-invalid={!!form.formState.errors.cep}
                    aria-describedby={
                      form.formState.errors.cep ? `cep-error-${field.name}` : undefined
                    }
                    disabled={isLoading}
                  />
                </FormControl>
                {isLoading && (
                  <div className="absolute right-3 top-2">
                    <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
                  </div>
                )}
              </div>
              <FormMessage>
                {error &&
                  !form.formState.errors.cep &&
                  (() => {
                    if (typeof window !== 'undefined') {
                      // Initialize dataLayer and tagManagerDataLayer
                      window.dataLayer = window.dataLayer || [];
                      window.tagManagerDataLayer = window.tagManagerDataLayer || window.dataLayer;

                      const eventData = {
                        event: 'cep_error', // Nome do evento personalizado
                        error_name: 'Invalid CEP', // Nome do erro
                        error_message: error, // Mensagem de erro retornada
                        form_field: 'cep', // Campo onde o erro ocorreu (opcional)
                      };

                      // Push to dataLayer
                      window.dataLayer.push(eventData);

                      // Push to tagManagerDataLayer if it's different from dataLayer
                      if (window.tagManagerDataLayer !== window.dataLayer) {
                        window.tagManagerDataLayer.push(eventData);
                      }
                    }

                    return <span className="text-sm text-destructive">{error}</span>;
                  })()}
              </FormMessage>
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <FormField
            control={form.control}
            name="street"
            render={({ field }) => (
              <FormItem className="md:col-span-2">
                <FormLabel>Endereço</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Rua, Avenida, Alameda, etc."
                    {...field}
                    value={field.value || ''}
                    required
                    onFocus={handleFocus}
                    onBlur={(e) => {
                      field.onChange(e.target.value);
                      handleBlur();
                      form.trigger('street');
                    }}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      form.trigger('street');
                    }}
                    className={cn(
                      form.formState.errors.street && 'input-error',
                      fieldsFilledByCep && 'bg-muted'
                    )}
                    aria-invalid={!!form.formState.errors.street}
                    aria-describedby={
                      form.formState.errors.street ? `street-error-${field.name}` : undefined
                    }
                    disabled={fieldsFilledByCep}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="streetNumber"
            render={({ field }) => (
              <FormItem className="md:col-span-1">
                <FormLabel>Número</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Ex: 123"
                    {...field}
                    value={field.value || ''}
                    required
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    onFocus={handleFocus}
                    onBlur={(e) => {
                      field.onChange(e.target.value);
                      handleBlur();
                      form.trigger('streetNumber');
                    }}
                    onChange={(e) => {
                      const value = formatNumbersOnly(e.target.value);
                      field.onChange(value);
                      form.trigger('streetNumber');
                    }}
                    className={cn(form.formState.errors.streetNumber && 'input-error')}
                    aria-invalid={!!form.formState.errors.streetNumber}
                    aria-describedby={
                      form.formState.errors.streetNumber
                        ? `streetNumber-error-${field.name}`
                        : undefined
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="complement"
            render={({ field }) => (
              <FormItem className="md:col-span-1">
                <FormLabel>Complemento</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Apto, Bloco, etc. (opcional)"
                    {...field}
                    value={field.value || ''}
                    onFocus={handleFocus}
                    onBlur={(e) => {
                      field.onChange(e.target.value);
                      handleBlur();
                      form.trigger('complement');
                    }}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      form.trigger('complement');
                    }}
                    className={cn(form.formState.errors.complement && 'input-error')}
                    aria-invalid={!!form.formState.errors.complement}
                    aria-describedby={
                      form.formState.errors.complement
                        ? `complement-error-${field.name}`
                        : undefined
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <FormField
            control={form.control}
            name="neighborhood"
            render={({ field }) => (
              <FormItem className="md:col-span-2">
                <FormLabel>Bairro</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Ex: Jardim Paulista"
                    {...field}
                    value={field.value || ''}
                    required
                    onFocus={handleFocus}
                    onBlur={(e) => {
                      field.onChange(e.target.value);
                      handleBlur();
                      form.trigger('neighborhood');
                    }}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      form.trigger('neighborhood');
                    }}
                    className={cn(
                      form.formState.errors.neighborhood && 'input-error',
                      fieldsFilledByCep && 'bg-muted'
                    )}
                    aria-invalid={!!form.formState.errors.neighborhood}
                    aria-describedby={
                      form.formState.errors.neighborhood
                        ? `neighborhood-error-${field.name}`
                        : undefined
                    }
                    disabled={fieldsFilledByCep}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="city"
            render={({ field }) => (
              <FormItem className="md:col-span-1">
                <FormLabel>Cidade</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Ex: São Paulo"
                    {...field}
                    value={field.value || ''}
                    required
                    onFocus={handleFocus}
                    onBlur={(e) => {
                      field.onChange(e.target.value);
                      handleBlur();
                      form.trigger('city');
                    }}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      form.trigger('city');
                    }}
                    className={cn(
                      form.formState.errors.city && 'input-error',
                      fieldsFilledByCep && 'bg-muted'
                    )}
                    aria-invalid={!!form.formState.errors.city}
                    aria-describedby={
                      form.formState.errors.city ? `city-error-${field.name}` : undefined
                    }
                    disabled={fieldsFilledByCep}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="state"
            render={({ field }) => (
              <FormItem className="md:col-span-1">
                <FormLabel>Estado</FormLabel>
                <FormControl>
                  {service && (
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        form.trigger('state');
                      }}
                      value={field.value || undefined}
                      required
                      onOpenChange={setIsStateSelectOpen}
                      disabled={fieldsFilledByCep}
                    >
                      <SelectTrigger
                        className={cn(
                          form.formState.errors.state && 'input-error',
                          fieldsFilledByCep && 'bg-muted'
                        )}
                      >
                        <SelectValue placeholder="Selecione" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(states).map(([key, value]) => (
                          <SelectItem key={key} value={key}>
                            {value}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
}
