'use client';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { CalendarDaysIcon as CalendarDaysFilled, CalendarIcon } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

import {
  Button,
  Calendar,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Popover,
  PopoverContent,
  PopoverTrigger,
  RadioGroup,
  RadioGroupItem,
} from '@/src/app/_components';

import { useScheduling } from '@/src/app/_hooks/useScheduling';
import { TIME_PERIODS, getDisabledDates } from '@/src/app/_utils/dateUtils';
import { cn } from '@/src/app/_utils/utils';

import type { ScheduleFormProps } from '@/src/app/_interfaces';
import type { CheckoutFormSchema } from '@/src/app/_utils/formValidation';

export function ScheduleForm(props: ScheduleFormProps) {
  const form = useFormContext<CheckoutFormSchema>();

  // Use the extracted scheduling hook with props
  const {
    selectedDate,
    isMorningAvailable,
    isAfternoonAvailable,
    isDatePickerVisible,
    setIsDatePickerVisible,
    focusedBlock,
    handleFocus,
    handleBlur,
    handleDateChange,
    handlePeriodChange,
  } = useScheduling(props);

  return (
    <Card
      className={cn(
        'transition-shadow duration-300',
        (focusedBlock === 'schedule' || isDatePickerVisible) &&
          'shadow-lg ring-2 ring-primary ring-opacity-50'
      )}
    >
      <CardHeader className="px-8">
        <div className="flex flex-col gap-2 md:flex-row md:items-center">
          <CalendarDaysFilled className="h-5 w-5 flex-shrink-0 text-primary" strokeWidth={3} />
          <CardTitle className="text-lg font-bold leading-tight">
            Quando você deseja realizar o serviço?
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 px-8 pb-8">
        <FormField
          control={form.control}
          name="date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Data do serviço</FormLabel>
              <Popover open={isDatePickerVisible} onOpenChange={setIsDatePickerVisible}>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={'outline'}
                      onFocus={handleFocus}
                      onClick={() => {
                        handleFocus();
                        setIsDatePickerVisible(true);
                      }}
                      className={cn(
                        'w-[240px] pl-3 text-left font-normal',
                        !field.value && 'text-muted-foreground'
                      )}
                    >
                      {field.value ? (
                        format(field.value, 'PPP', { locale: ptBR })
                      ) : (
                        <span>Escolha uma data</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={(date) => {
                      if (date) {
                        field.onChange(date);
                        handleDateChange(date);
                        handleBlur();
                        setIsDatePickerVisible(false);
                      }
                    }}
                    disabled={getDisabledDates}
                    initialFocus
                    required
                    locale={ptBR}
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="period"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>Período</FormLabel>
              <FormControl>
                <RadioGroup
                  onFocus={handleFocus}
                  onBlur={handleBlur}
                  onValueChange={(value) => {
                    field.onChange(value);
                    handlePeriodChange(value);
                  }}
                  value={field.value}
                  className="flex flex-col space-y-1"
                  required
                >
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem
                        value="morning"
                        disabled={!isMorningAvailable || !selectedDate}
                      />
                    </FormControl>
                    <div>
                      <FormLabel
                        className={cn(
                          'font-normal',
                          (!isMorningAvailable || !selectedDate) && 'text-muted-foreground'
                        )}
                      >
                        Manhã ({TIME_PERIODS.MORNING.displayTime})
                      </FormLabel>
                      {selectedDate && !isMorningAvailable && (
                        <p className="text-xs text-muted-foreground">Indisponível</p>
                      )}
                    </div>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem
                        value="afternoon"
                        disabled={!isAfternoonAvailable || !selectedDate}
                      />
                    </FormControl>
                    <div>
                      <FormLabel
                        className={cn(
                          'font-normal',
                          (!isAfternoonAvailable || !selectedDate) && 'text-muted-foreground'
                        )}
                      >
                        Tarde ({TIME_PERIODS.AFTERNOON.displayTime})
                      </FormLabel>
                      {selectedDate && !isAfternoonAvailable && (
                        <p className="text-xs text-muted-foreground">Indisponível</p>
                      )}
                    </div>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              {selectedDate && !isMorningAvailable && !isAfternoonAvailable && (
                <p className="text-sm text-destructive">
                  Não há horários disponíveis para a data selecionada. Por favor, escolha outra
                  data.
                </p>
              )}
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}
