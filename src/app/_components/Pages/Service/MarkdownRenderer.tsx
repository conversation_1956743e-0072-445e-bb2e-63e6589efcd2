interface MarkdownRendererProps {
  markdown: string | string[] | undefined;
  className?: string;
}

export function MarkdownRenderer({ markdown = '', className = '' }: MarkdownRendererProps) {
  // Convert array to string if needed
  const markdownText = Array.isArray(markdown) ? markdown.join('\n\n') : markdown;
  // Basic parsing for common markdown elements
  const parseMarkdown = (text: string) => {
    // Handle headers (# Heading)
    text = text.replace(/^###\s+(.*?)$/gm, '<h3>$1</h3>');
    text = text.replace(/^##\s+(.*?)$/gm, '<h2>$1</h2>');
    text = text.replace(/^#\s+(.*?)$/gm, '<h1>$1</h1>');

    // Handle bold text
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Handle italic text
    text = text.replace(/\_(.*?)\_/g, '<em>$1</em>');

    // Handle links ([text](url))
    text = text.replace(
      /\[(.*?)\]\((.*?)\)/g,
      '<a href="$2" target="_blank" class="text-primary hover:underline">$1</a>'
    );

    // Handle inline code (`code`)
    text = text.replace(/`([^`]+)`/g, '<code class="px-1 py-0.5 rounded bg-slate-100">$1</code>');

    // Handle lists
    // First, identify list sections
    const listSections = text.split(/\n(?!\s*-\s+)/g);
    let processedText = '';

    for (let section of listSections) {
      // Check if this section contains list items
      if (section.trim().match(/^\s*-\s+/m)) {
        // Process list items
        const listItems = section
          .split('\n')
          .filter((line) => line.trim().match(/^\s*-\s+/))
          .map((line) => `<li>${line.replace(/^\s*-\s+/, '')}</li>`)
          .join('');

        // Add the list to the processed text
        processedText += `<ul>${listItems}</ul>`;
      } else {
        // Not a list section, just add it as is
        processedText += section;
      }
    }

    text = processedText || text;

    // Handle horizontal rules
    text = text.replace(/^\s*---\s*$/gm, '<hr class="my-4 border-t border-gray-200" />');

    // Handle paragraphs (but don't wrap already-wrapped content)
    text = text.replace(/^([^<].*?)$/gm, '<p>$1</p>');

    // Clean up empty paragraphs
    text = text.replace(/<p>\s*<\/p>/g, '');

    return text;
  };

  return (
    <div
      className={`markdown-content ${className}`}
      dangerouslySetInnerHTML={{ __html: parseMarkdown(markdownText || '') }}
    />
  );
}
