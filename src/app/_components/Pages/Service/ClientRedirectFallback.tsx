'use client';

import { useServiceContext } from '@/src/app/_context/ServiceContext';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { TestimonialsSkeleton } from '../../Common/Testimonial/TestimonialsSkeleton';
import { ServicePageSkeleton } from '../Home/ServicePageSkeleton';

export function ClientRedirectFallback() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Get services from context
  const { services } = useServiceContext();

  useEffect(() => {
    const hasSlugParam = searchParams.has('slug');
    if (hasSlugParam) return;

    const pathParts = pathname?.split('/').filter(Boolean);
    const subcategorySlug = pathParts?.[1]; // Ex: ['servicos', 'chuveiro'] => 'chuveiro'

    if (!subcategorySlug || !services || !Array.isArray(services)) return;

    try {
      // Find the subcategory in the context
      let firstServiceSlug = null;

      // Search through all categories and subcategories
      for (const category of services) {
        for (const subcategory of category.subcategories) {
          if (subcategory.slug === subcategorySlug && subcategory.services.length > 0) {
            firstServiceSlug = subcategory.services[0].slug;
            break;
          }
        }
        if (firstServiceSlug) break;
      }

      if (firstServiceSlug) {
        const newUrl = `${pathname}?slug=${firstServiceSlug}`;
        window.location.replace(newUrl);
      }
    } catch (e) {
      console.error('Failed to redirect from context:', e);
    }
  }, [pathname, searchParams, router, services]);

  return (
    <>
      <ServicePageSkeleton />
      <TestimonialsSkeleton />
    </>
  );
}
