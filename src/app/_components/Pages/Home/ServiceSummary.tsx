import { ServiceType } from '@/src/app/_interfaces';
import { ServiceSummaryCard } from './ServiceSummaryCard';

async function getServiceData(slug: string): Promise<ServiceType | null> {
  try {
    const API_URL = process.env.NEXT_PRIVATE_API_BASE_URL;
    const response = await fetch(`${API_URL}/service-type/${slug}`, {
      headers: {
        'service-provider': 'EUR',
      },
      cache: 'no-store', // Garante que os dados são sempre atualizados
    });

    if (!response.ok) {
      throw new Error(`Erro ao buscar serviço: ${response.statusText}`);
    }

    return response.json();
  } catch (error) {
    console.error('Erro ao buscar serviço:', error);
    return null;
  }
}

interface ServiceSummaryProps {
  slug: string;
}

export default async function ServiceSummary({ slug }: ServiceSummaryProps) {
  const service = await getServiceData(slug);

  return <ServiceSummaryCard service={service} />;
}
