import { Card, CardContent } from '@/src/app/_components';

export function ServiceCarouselSkeleton() {
  const skeletonCards = Array.from({ length: 4 });

  return (
    <div className="bg-blue relative">
      <h2 className="mb-4 px-4 text-xl font-extrabold md:px-6 lg:mb-6 lg:text-3xl">
        Disponível para agendar agora
      </h2>

      <div className="relative -mx-4 md:-mx-6">
        <div className="no-scrollbar flex gap-4 overflow-x-auto py-4 pl-4 pr-4 will-change-transform md:pl-6 md:pr-6">
          {skeletonCards.map((_, index) => (
            <Card
              key={index}
              className="w-[232px] flex-shrink-0 transform-gpu overflow-hidden border-none shadow-none"
            >
              <CardContent className="animate-pulse space-y-4 p-4">
                {/* Imagem simulada */}
                <div className="h-40 w-full rounded-xl bg-gray-200" />

                {/* Título */}
                <div className="h-5 w-2/3 rounded bg-gray-300" />

                {/* Preço */}
                <div className="space-y-1 pt-4">
                  <div className="h-4 w-1/3 rounded bg-gray-200" />
                  <div className="h-6 w-1/2 rounded bg-gray-300" />
                </div>

                {/* Subcategorias */}
                <div className="space-y-3 pt-6">
                  <div className="h-4 w-4/5 rounded bg-gray-200" />
                  <div className="h-4 w-3/5 rounded bg-gray-200" />
                  <div className="h-4 w-2/3 rounded bg-gray-200" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
