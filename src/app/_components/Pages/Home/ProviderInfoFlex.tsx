'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useAnalyticsEventGeneric } from '@/src/app/_hooks';

export const ProviderInfoFlex = () => {
  const { sendEvent } = useAnalyticsEventGeneric();

  return (
    <div className="flex flex-col items-center gap-10 md:flex-row">
      {/* Coluna 1 */}
      <div className="flex flex-col gap-6 md:w-1/2">
        <p className="font-md text-xl text-gray-500">
          Agora você pode contratar serviços essenciais para sua casa de forma rápida e segura, com
          a qualidade do <strong>GetNinjas</strong> e a garantia da{' '}
          <strong> Europ Assistance</strong>.
        </p>
        <p className="font-md text-xl text-gray-500">
          Com o <strong className="text-yellow-400">GetNinjas + Europ</strong>, você agenda um
          serviço confiável, com preço fechado e garantia de qualidade.
        </p>
      </div>

      {/* Coluna 2 */}
      <div className="flex flex-col gap-6 md:w-1/2">
        <div className="border-l-2 p-8">
          <Link
            href="https://www.europ-assistance.com.br/"
            target="_blank"
            onClick={() => sendEvent('home_click_https://www.europ-assistance.com.br/')}
            className="flex flex-col gap-8"
          >
            <Image src="/images/Europ_Logo.svg" width={120} height={120} alt="Europ logo" />
            <p className="font-md text-lg text-gray-500">
              Profissionais especializados e mais de 60 anos de experiência.
            </p>
          </Link>
        </div>
        <div className="border-l-2 p-8">
          <Link
            href="https://www.getninjas.com.br/"
            target="_blank"
            onClick={() => sendEvent('home_click_https://www.getninjas.com.br/')}
            className="flex flex-col gap-8"
          >
            <Image src="/images/GetNinjas_logo.svg" width={150} height={150} alt="GetNinjas logo" />
            <p className="font-md text-lg text-gray-500">
              Mais de 13 anos os encontrando melhores profissionais para resolver o seu problema.
            </p>
          </Link>
        </div>
      </div>
    </div>
  );
};
