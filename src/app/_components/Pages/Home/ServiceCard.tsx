'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/src/app/_components';

import { ServiceType } from '@/src/app/_interfaces';

import Image from 'next/image';
import Link from 'next/link';

import { trackViewItem } from '@/src/app/_functions/analytics/common/trackViewItem';
import { capitalizeFirstLetter } from '@/src/app/_utils/stringUtils';
import { Calendar } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface ServiceCardProps {
  service: ServiceType;
  maxDetailItems?: number;
  renderButton?: () => React.ReactNode;
}

export function ServiceCard({ service, renderButton }: ServiceCardProps) {
  const router = useRouter();
  const slug = service.slug;

  // Convert service name to sentence case
  const sentenceCaseName = capitalizeFirstLetter(service.name.toLowerCase());

  return (
    <Card
      className="flex h-full cursor-pointer flex-col justify-between transition-shadow hover:shadow-lg"
      onClick={() => router.push(`/service/${slug}`)}
      aria-label={`Detalhes do serviço: ${service.name}`}
    >
      {/* Imagem do serviço */}
      <div className="relative aspect-video">
        <Image
          src={service.imageUrl}
          alt={`${service.name} - ${service.description}`}
          layout="fill"
          className="rounded-t-lg object-cover"
        />
        {service.provider && (
          <div className="absolute bottom-2 right-2 h-12 w-12 overflow-hidden rounded-full border border-gray-200 shadow-sm">
            <Image
              src={service.provider.imageUrl}
              alt={service.provider.name}
              layout="fill"
              className="object-cover"
            />
          </div>
        )}
      </div>

      {/* Título e palavras-chave */}
      <CardHeader>
        <CardTitle className="text-lg md:text-xl">{sentenceCaseName}</CardTitle>
      </CardHeader>

      {/* Detalhes */}
      <CardContent className="flex flex-grow flex-col">
        <p className="mb-4 text-gray-600">{service.description}</p>

        {/* Preço */}
        <div className="mt-auto flex flex-col border-t pt-4">
          <div className="mb-4">
            {service.price.discountPrice > 0 && (
              <span className="ml-2 text-sm text-gray-500 line-through">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                }).format(service.price.originalPrice)}
              </span>
            )}
            <span className="block text-lg font-semibold text-primary md:text-xl">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL',
              }).format(service.price.finalPrice)}
            </span>
          </div>

          {/* Botões de ação */}
          {renderButton ? (
            renderButton()
          ) : (
            <>
              <Button asChild size="sm" className="mb-2 w-full">
                <Link
                  href={`/service/${slug}/checkout`}
                  className="flex items-center justify-center"
                  onClick={(e) => {
                    // Stop event propagation to prevent the parent Card's onClick from firing
                    e.stopPropagation();
                    trackViewItem({
                      id: service.id,
                      name: service.name,
                      price: service.price.finalPrice,
                    });
                  }}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  Agendar agora
                </Link>
              </Button>
              <Button asChild size="sm" variant="outline" className="w-full">
                <Link
                  href={`/service/${slug}`}
                  onClick={(e) => {
                    // Also stop propagation for the Details button to be consistent
                    e.stopPropagation();
                  }}
                >
                  Detalhes
                </Link>
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
