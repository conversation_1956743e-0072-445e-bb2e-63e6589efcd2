import { <PERSON>, Card<PERSON>ontent, CardHeader, Skeleton } from '@/src/app/_components';

export function ServiceCardSkeleton() {
  return (
    <Card className="flex h-full flex-col">
      {/* Service Image */}
      <div className="relative aspect-video">
        <Skeleton className="h-full w-full rounded-t-lg" />
      </div>
      <CardHeader>
        {/* Service Title */}
        <Skeleton className="mb-2 h-6 w-3/4" />
        {/* Service Categories */}
        <div className="mt-2 flex flex-wrap gap-2">
          <Skeleton className="h-5 w-16" />
          <Skeleton className="h-5 w-20" />
          <Skeleton className="h-5 w-24" />
        </div>
      </CardHeader>
      <CardContent className="flex flex-grow flex-col">
        {/* Service Details */}
        <div className="mb-4 space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-4/6" />
        </div>
        {/* Service Availability */}
        <Skeleton className="mb-2 h-4 w-3/4" />
        <div className="mt-4 flex flex-col border-t pt-4">
          {/* Service Price */}
          <Skeleton className="mb-1 h-5 w-1/3" />
          <Skeleton className="mb-4 h-6 w-1/2" />
          {/* Action Buttons */}
          <Skeleton className="mb-2 h-9 w-full" />
          <Skeleton className="h-9 w-full" />
        </div>
      </CardContent>
    </Card>
  );
}
