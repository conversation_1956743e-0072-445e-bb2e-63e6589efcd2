'use client';

import { ServiceCarousel } from './ServiceCarousel';
import { ServiceCarouselProps } from '@/src/app/_interfaces';

/**
 * This wrapper component ensures that the ServiceCarousel component
 * is properly loaded as a client component. This is necessary because
 * the ServiceCarousel component uses refs, which cannot be used in
 * Server Components.
 */
export function ServiceCarouselWrapper(props: ServiceCarouselProps) {
  return <ServiceCarousel {...props} />;
}
