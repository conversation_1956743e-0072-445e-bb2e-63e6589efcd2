'use client';

import { ChevronRight } from 'lucide-react';

export const Advantages = () => {
  const advantages = [
    {
      title: '90 dias de garantia',
      description: 'Garantia de 90 dias em todos os serviços realizados',
    },
    {
      title: 'Agendamento imediato',
      description: 'Agende seu serviço para o mesmo dia ou dia seguinte',
    },
    {
      title: 'Preço fechado',
      description: 'Sem surpresas, você sabe exatamente quanto vai pagar',
    },
  ];

  return (
    <div className="z-1 relative">
      <div className="mx-auto max-w-7xl">
        <div className="flex flex-col justify-between gap-10 md:flex-row">
          {advantages.map((advantage, index) => (
            <div key={index} className="flex-1">
              <div className="flex items-start">
                <div className="mr-2 flex h-8 flex-shrink-0 items-center justify-center rounded-full">
                  <ChevronRight className="h-5 w-5 text-[#FCC800]" strokeWidth={4} />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-slate-900">{advantage.title}</h3>
                  <p className="mt-2 text-base text-slate-600">{advantage.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
