import { Skeleton, Card, CardContent } from '@/src/app/_components';

export function ServicePageSkeleton() {
  return (
    <div className="container mx-auto px-4 pt-4">
      {/* Breadcrumb */}
      <Skeleton className="mb-4 h-8 w-48" />

      {/* Hero Section */}
      <section className="mb-8 mt-4 overflow-hidden rounded-md bg-white text-gray-900 shadow-lg sm:mb-12 md:mb-16">
        <div className="flex flex-col items-stretch lg:flex-row">
          {/* Service Information */}
          <div className="mb-4 p-4 sm:p-6 md:p-8 lg:mb-0 lg:w-1/2 lg:p-12">
            <Skeleton className="mb-4 h-8 w-3/4 sm:h-10 md:h-12" />
            <Skeleton className="mb-6 h-4 w-full sm:h-5 md:h-6" />
            <div className="mb-6 flex flex-wrap gap-2">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-6 w-20" />
              ))}
            </div>
            <div className="mb-6 rounded-lg bg-gray-50 p-4 shadow-sm">
              <Skeleton className="mb-2 h-4 w-1/2" />
              <Skeleton className="mb-2 h-6 w-2/3" />
              <Skeleton className="h-4 w-full" />
            </div>
            <Skeleton className="h-10 w-full sm:h-12 sm:w-48" />
          </div>
          {/* Service Image */}
          <div className="relative h-48 sm:h-64 md:h-80 lg:h-auto lg:w-1/2">
            <Skeleton className="h-full w-full" />
          </div>
        </div>
      </section>

      {/* Service Details Section */}
      <section className="mb-12">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Service Details */}
          <div className="space-y-6 lg:col-span-2">
            <Skeleton className="mb-6 h-8 w-48" />
            <div className="space-y-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="grid grid-cols-1 items-start gap-4 sm:grid-cols-4">
                  <div className="sm:col-span-1">
                    <Skeleton className="h-6 w-24" />
                  </div>
                  <div className="sm:col-span-3">
                    <Skeleton className="mb-2 h-4 w-full" />
                    <Skeleton className="h-4 w-5/6" />
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div>
            {/* Service Summary Card */}
            <Card className="border-none bg-card shadow-lg">
              <CardContent className="p-4 sm:p-6">
                <Skeleton className="mb-4 h-6 w-48" />
                <div className="mb-6 space-y-4">
                  {[1, 2].map((i) => (
                    <div key={i} className="flex items-start">
                      <Skeleton className="mr-3 mt-1 h-5 w-5 flex-shrink-0" />
                      <div className="flex-grow">
                        <Skeleton className="mb-2 h-4 w-3/4" />
                        <Skeleton className="h-3 w-full" />
                      </div>
                    </div>
                  ))}
                </div>
                <Skeleton className="mt-6 h-10 w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
}
