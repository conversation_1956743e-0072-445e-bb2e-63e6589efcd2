'use client';

import { Calendar, HardHat, Pointer, Receipt } from 'lucide-react';

export const HowItWorks = () => {
  const steps = [
    {
      number: 1,
      icon: Pointer,
      title: '<PERSON><PERSON>cione o serviço',
    },
    {
      number: 2,
      icon: Calendar,
      title: '<PERSON><PERSON><PERSON>ha a melhor data',
    },
    {
      number: 3,
      icon: HardHat,
      title: '<PERSON><PERSON><PERSON> o profissional',
    },
    {
      number: 4,
      icon: Receipt,
      title: 'Aprove o pagamento',
    },
  ];

  return (
    <div className="mx-auto">
      <div className="mb-8">
        <h2 className="text-xl font-bold text-muted-foreground">Como funciona?</h2>
      </div>

      <div className="flex flex-col justify-center gap-8 md:flex-row md:flex-wrap">
        {steps.map((step) => (
          <div key={step.number} className="flex flex-1 gap-4 md:min-w-[200px] md:max-w-[250px]">
            <div className="relative">
              <div className="flex h-10 w-10 items-center justify-center rounded-full border border-slate-200 bg-slate-50 text-slate-600">
                <span className="text-xl font-black">{step.number}</span>
              </div>
            </div>

            <div className="flex-1 border-l border-slate-200 pl-6">
              <div className="mb-4">
                <step.icon className="h-10 w-10 text-muted-foreground" strokeWidth={2} />
              </div>
              <h3 className="mb-2 text-base font-semibold text-slate-900">{step.title}</h3>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
