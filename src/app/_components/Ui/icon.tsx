'use client';

import * as LucideIcons from 'lucide-react';
import { LucideProps } from 'lucide-react';
import * as React from 'react';

export type IconName = keyof typeof LucideIcons;

export interface IconProps extends Omit<LucideProps, 'ref'> {
  name: IconName;
}

export const Icon = ({
  name,
  size = 24,
  strokeWidth = 2,
  color = 'currentColor',
  className = '',
  ...props
}: IconProps) => {
  if (!(name in LucideIcons)) {
    // Silent fail for missing icon
    return null;
  }

  const LucideIcon = LucideIcons[name] as React.FC<LucideProps>;

  return (
    <LucideIcon
      size={size}
      strokeWidth={strokeWidth}
      color={color}
      className={className}
      {...props}
    />
  );
};
