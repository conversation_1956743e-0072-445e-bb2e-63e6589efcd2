interface ErrorFallbackProps {
  error: Error;
}

export function ErrorFallback({ error }: ErrorFallbackProps) {
  return (
    <div role="alert" className="rounded-md border border-red-400 bg-red-100 p-4">
      <h2 className="mb-2 text-lg font-semibold text-red-800">Aconteceu alguma coisa errada:</h2>
      <pre className="whitespace-pre-wrap text-sm text-red-600">{error.message}</pre>
    </div>
  );
}
