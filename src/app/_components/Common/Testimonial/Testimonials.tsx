import { Star } from 'lucide-react';
import Image from 'next/image';

interface Provider {
  name: string;
  imageUrl: string;
}

interface TestimonialsProps {
  provider: Provider;
}

export const Testimonials = ({ provider }: TestimonialsProps) => {
  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      rating: 5,
      comment: 'Excelente serviço! Profissionais muito competentes e atenciosos.',
      state: 'Rio de Janeiro',
    },
    {
      id: 2,
      name: '<PERSON>',
      rating: 5,
      comment: 'Muito satisfeito com o atendimento e a qualidade do serviço.',
      state: 'São Paulo',
    },
    {
      id: 3,
      name: '<PERSON>',
      rating: 4,
      comment: 'Bom serviço, recomendo! O tempo para chegar e consertar foi bem pontual.',
      state: 'Minas Gerais',
    },
  ];

  return (
    <section className="py-16" role="complementary">
      <div className="container mx-auto px-4">
        {/* Cabeçalho com logo e título */}
        <div className="mb-8 flex flex-col items-center">
          <div className="mb-4 h-20 w-20 overflow-hidden rounded-full border-2 border-gray-200 p-1 shadow-lg">
            <Image
              src={provider.imageUrl}
              alt={`${provider.name} logo`}
              width={80}
              height={80}
              className="object-cover"
            />
          </div>
          <div className="mb-2 flex items-center justify-center">
            <div className="mr-4 h-px w-12 bg-gray-200" data-testid="decorative-line" />
            <h2 className="text-center text-sm font-medium uppercase tracking-wider text-gray-400 md:text-start">
              O que nossos clientes dizem sobre
            </h2>
            <div className="ml-4 h-px w-12 bg-gray-200" data-testid="decorative-line" />
          </div>
          <p className="mb-12 text-center text-4xl font-bold text-primary">
            {provider.name
              .split(' ')
              .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
              .join(' ')}
          </p>
        </div>

        {/* Cartões de depoimentos */}
        <div className="flex flex-col gap-2 space-y-8 md:flex-row md:space-x-8 md:space-y-0">
          {testimonials.map((testimonial) => (
            <div key={testimonial.id} className="relative flex flex-1 flex-col">
              <div className="th items-between flex h-full flex-col justify-between rounded-lg bg-white p-6">
                <div>
                  <div className="flex items-center justify-center gap-2 md:justify-start">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star key={i} className={`h-5 w-5 ${'fill-current text-yellow-400'}`} />
                    ))}
                  </div>
                </div>

                <p className="my-4 text-center text-gray-600 md:text-start">
                  {testimonial.comment}
                </p>

                <div>
                  <h3 className="text-center text-lg font-bold text-muted-foreground md:text-start">
                    {testimonial.name}
                  </h3>
                  <p className="text-center text-lg text-gray-400 md:text-start">
                    {testimonial.state}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
