import { Skeleton } from '@/src/app/_components';

export function TestimonialsSkeleton() {
  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="mb-8 flex flex-col items-center">
          <Skeleton className="mb-4 h-20 w-20 rounded-full" />
          <div className="mb-2 flex items-center justify-center">
            <div className="mr-4 h-px w-12 bg-gray-100"></div>
            <Skeleton className="h-4 w-48" />
            <div className="ml-4 h-px w-12 bg-gray-100"></div>
          </div>
          <Skeleton className="mb-12 h-8 w-64" />
        </div>
        <div className="flex flex-col space-y-8 md:flex-row md:space-x-8 md:space-y-0">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex-1">
              <div className="flex h-full flex-col">
                <Skeleton className="mb-4 h-4 w-24" />
                <Skeleton className="mb-4 h-20 w-full" />
                <div className="mt-auto flex items-center">
                  <Skeleton className="mr-3 h-10 w-10 rounded-full" />
                  <div>
                    <Skeleton className="mb-1 h-4 w-24" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
