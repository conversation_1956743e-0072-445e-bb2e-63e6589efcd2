import type { Testimonial } from '@/src/app/_interfaces';
import { Star } from 'lucide-react';

interface TestimonialCardProps {
  testimonial: Testimonial;
}

export function TestimonialCard({ testimonial }: TestimonialCardProps) {
  return (
    <div className="flex h-full flex-col">
      <div className="mb-4">
        <div className="flex">
          {[...Array(5)].map((_, i) => (
            <Star key={i} className="mr-1 h-5 w-5 fill-current text-gray-400" />
          ))}
        </div>
      </div>
      <p className="flex-grow text-gray-600">{testimonial.content}</p>
      <div className="mt-4">
        <p className="text-sm font-semibold text-gray-900">{testimonial.author}</p>
        <p className="text-sm text-gray-500">{testimonial.location}</p>
      </div>
    </div>
  );
}
