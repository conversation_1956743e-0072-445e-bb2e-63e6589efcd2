'use client';

import { But<PERSON>, Icon } from '@/src/app/_components';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { RefObject } from 'react';

import { useAnalyticsEventGeneric, useMenu, useSubmenu } from '@/src/app/_hooks';

// Importação dinâmica para DesktopNavigation
const DesktopNavigation = dynamic(() => import('./DesktopNavigation'));

// Importação dinâmica para MobileNavigation
const MobileNavigation = dynamic(() => import('./MobileNavigation'));

interface HeaderProps {
  logoPath: string;
  headerRef: RefObject<HTMLElement>;
  submenuRef: RefObject<HTMLDivElement>;
}

export default function Header({
  logoPath,
  headerRef,
  submenuRef,
}: HeaderProps): React.ReactElement {
  const pathname = usePathname();

  const { sendEvent } = useAnalyticsEventGeneric();
  const { isMenuOpen, toggleMenu } = useMenu();
  const { isSubmenuOpen, handleMouseEnter, handleMouseLeave } = useSubmenu(submenuRef);

  return (
    <header
      ref={headerRef}
      className="fixed top-0 z-[60] w-full border-b border-gray-200 bg-white shadow-[0px_1px_4px_0px_rgba(0,0,0,0.08)]"
    >
      <div className="flex items-center justify-between px-4 py-6 md:px-8 2xl:px-40">
        <Link href="/" className="flex items-center">
          <Image
            src={logoPath || '/images/getninjas_europ_logo.svg'}
            alt="GetNinjas Logo"
            width={120}
            height={40}
            className="h-10 w-auto object-contain"
            quality={85}
            priority
            onError={(e) => {
              // Fallback to a default image if the logo fails to load
              const target = e.target as HTMLImageElement;
              target.src = '/images/getninjas_europ_logo.svg';
            }}
          />
        </Link>

        {/* Desktop Navigation */}
        <DesktopNavigation
          submenuRef={submenuRef as RefObject<HTMLDivElement>}
          isSubmenuOpen={isSubmenuOpen}
          handleMouseEnter={handleMouseEnter}
          handleMouseLeave={handleMouseLeave}
          pathname={pathname}
        />

        {/* Mobile Menu Button - Following Figma design */}
        {isMenuOpen ? (
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-slate-100 md:hidden">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 p-0"
              onClick={() => toggleMenu()}
              aria-label="Close menu"
            >
              <Icon name="X" className="h-5 w-5" strokeWidth={2} />
            </Button>
          </div>
        ) : (
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => {
              sendEvent('menu_click_open');
              toggleMenu();
            }}
            aria-label="Open menu"
          >
            <Icon name="Menu" className="h-6 w-6" />
          </Button>
        )}
      </div>

      {/* Mobile Navigation - Added on top of header with proper z-index */}
      <MobileNavigation isMenuOpen={isMenuOpen} toggleMenu={toggleMenu} />
    </header>
  );
}
