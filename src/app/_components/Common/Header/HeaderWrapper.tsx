'use client';

import { RefObject, useRef } from 'react';
import Header from './Header';

interface HeaderWrapperProps {
  logoPath: string;
}

/**
 * This wrapper component is responsible for creating and managing refs
 * that will be passed to the Header component. This ensures that all refs
 * are created and used within a client component context.
 */
export default function HeaderWrapper({ logoPath }: HeaderWrapperProps) {
  // Create refs here in the client component
  const headerRef = useRef<HTMLElement>(null);
  const submenuRef = useRef<HTMLDivElement>(null);

  return (
    <Header
      logoPath={logoPath}
      headerRef={headerRef as RefObject<HTMLElement>}
      submenuRef={submenuRef as RefObject<HTMLDivElement>}
    />
  );
}
