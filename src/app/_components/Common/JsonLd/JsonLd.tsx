// Este componente renderiza dados estruturados JSON-LD para melhorar o SEO da página.
// Ele ajuda os mecanismos de busca a entender melhor o conteúdo do site.
import type React from 'react';

interface JsonLdProps {
  data: Record<string, unknown>;
}

export const JsonLd: React.FC<JsonLdProps> = ({ data }) => {
  // Safely serialize the data, filtering out any potential router state objects
  const safeData = JSON.stringify(data, (key, value) => {
    // Avoid serializing objects that might be router state
    if (
      typeof value === 'object' &&
      value !== null &&
      (key === 'children' || key === 'params' || key === '__PAGE__')
    ) {
      return undefined; // Skip these properties
    }
    return value;
  });

  return <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: safeData }} />;
};
