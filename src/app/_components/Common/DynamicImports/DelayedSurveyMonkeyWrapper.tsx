'use client';

import { DelayedSurveyMonkeyScript } from '@/src/app/_components';
import dynamic from 'next/dynamic';

/**
 * This wrapper component ensures that the DelayedSurveyMonkeyScript component
 * is properly loaded as a client component. This is necessary because
 * the DelayedSurveyMonkeyScript component uses refs, which cannot be used in
 * Server Components.
 */
export const DynamicDelayedSurveyMonkey = dynamic(
  () => Promise.resolve(DelayedSurveyMonkeyScript),
  {
    ssr: false,
  }
);
