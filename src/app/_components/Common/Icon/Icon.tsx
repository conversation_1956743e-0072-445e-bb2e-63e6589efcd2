import { cn } from '@/src/app/_utils';
import * as LucideIcons from 'lucide-react';
import { LucideProps } from 'lucide-react';

export type IconName = keyof typeof LucideIcons;
type IconComponent = React.ForwardRefExoticComponent<
  Omit<LucideProps, 'ref'> & React.RefAttributes<SVGSVGElement>
>;

interface IconProps extends Omit<LucideProps, 'ref'> {
  className?: string;
  name: IconName;
}

export function Icon({ className, name, ...props }: IconProps) {
  const IconComponent = LucideIcons[name] as IconComponent;

  if (!IconComponent) {
    // Silent fail for missing icon
    return null;
  }

  return <IconComponent className={cn('h-4 w-4', className)} strokeWidth={1.5} {...props} />;
}

export default Icon;
