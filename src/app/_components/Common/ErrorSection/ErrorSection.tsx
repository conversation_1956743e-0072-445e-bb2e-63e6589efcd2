'use client';

import { Button } from '@/src/app/_components';

export const ErrorSection = () => {
  return (
    <div className="flex min-h-[50vh] flex-col items-center justify-center p-4">
      <h2 className="mb-4 text-xl font-semibold text-red-600">Erro ao carregar serviços</h2>
      <p className="mb-4 text-gray-600">
        Ocorreu um erro ao carregar a lista de serviços. Por favor, tente novamente mais tarde.
      </p>
      <Button onClick={() => window.location.reload()}>Tentar novamente</Button>
    </div>
  );
};
