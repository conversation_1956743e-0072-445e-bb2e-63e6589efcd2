import heroImage from '@/public/images/hero_image.webp';
import Image from 'next/image';

export function Hero() {
  return (
    <section className="flex min-h-[70vh] items-center py-12 sm:py-16 lg:py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-between gap-12 lg:flex-row lg:gap-12">
          <div className="flex w-full flex-col justify-center lg:w-1/2">
            <h1 className="mb-4 text-3xl font-bold leading-tight text-gray-900 sm:text-4xl md:text-5xl lg:text-6xl">
              Seu problema resolvido com garantia e confiança
            </h1>
            <p className="mb-6 text-base text-gray-600 sm:text-lg md:text-xl">
              O GetNinjas ajuda você a encontrar o serviço que você precisa com segurança e
              agilidade, em parceria com a Europ Assistance.
            </p>
          </div>
          <div className="relative order-first flex h-[300px] w-full items-center justify-center pb-8 sm:h-[350px] md:h-[400px] lg:order-last lg:h-[450px] lg:w-1/2 lg:pb-0">
            <Image
              src={heroImage}
              alt="Mosaico de fotos de quatro profissionais diferentes prestando diversos serviços como troca de fechadura, conserto de pia, dedetização e troca de lustre."
              placeholder="blur"
              blurDataURL="/hero_image.webp"
              width={400}
              height={450}
              className="max-h-60 max-w-80 scale-110 md:max-h-none md:max-w-none"
              priority
              fetchPriority="high"
              quality={75}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              loading="eager"
              decoding="async"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
