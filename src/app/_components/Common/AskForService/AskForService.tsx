'use client';

import { Button } from '@/src/app/_components';
import { cn } from '@/src/app/_utils/utils';
import Image from 'next/image';
import Link from 'next/link';

interface AskForServiceProps {
  variant?: 'default' | 'compact' | 'custom';
  className?: string;
  containerClassName?: string;
  titleClassName?: string;
  descriptionClassName?: string;
  buttonClassName?: string;
  showIcon?: boolean;
}

export function AskForService({
  variant = 'default',
  className,
  containerClassName,
  titleClassName,
  descriptionClassName,
  buttonClassName,
  showIcon = false,
}: AskForServiceProps) {
  if (variant === 'custom') {
    return (
      <section
        className={cn('relative w-full overflow-hidden rounded-3xl', className)}
        style={{ background: 'linear-gradient(26deg, #EDA909 -40.7%, #FDE63E 95.2%)' }}
      >
        <div className="bg-grid-gray-300/[0.2] absolute inset-0 overflow-hidden bg-[size:20px_20px]"></div>
        <div
          className={cn(
            'relative z-10 flex flex-col items-start justify-between gap-10 p-10 lg:flex-row lg:items-center',
            containerClassName
          )}
        >
          {showIcon && (
            <Image
              src="/images/GetNinjas_Icon.svg"
              alt="GetNinjas Europe"
              width={64}
              height={64}
              className="h-16 w-auto md:h-24"
            />
          )}
          <div>
            <h2 className={cn('text-xl font-bold leading-tight md:text-2xl', titleClassName)}>
              Não encontrou o serviço que precisa?
            </h2>
            <p className={cn('mt-2 max-w-2xl text-base', descriptionClassName)}>
              Faça um pedido personalizado no GetNinjas e encontre o profissional ideal para sua
              necessidade.
            </p>
          </div>

          <div className="w-full lg:w-auto">
            <Button
              asChild
              size="lg"
              className={cn(
                'h-12 w-full rounded-xl bg-white px-6 text-base font-bold text-black transition duration-300 hover:bg-gray-100',
                buttonClassName
              )}
            >
              <Link
                href="https://www.getninjas.com.br"
                target="_blank"
                rel="noopener noreferrer"
                className="w-full"
              >
                <span className="inline-block w-full text-center">
                  Pedir serviço
                  <span className="max-[408px]:block"> personalizado</span>
                </span>
              </Link>
            </Button>
          </div>
        </div>
      </section>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn('rounded-lg bg-gray-900 p-4 text-center', className)}>
        <h3 className={cn('mb-2 text-lg font-semibold text-white', titleClassName)}>
          Não encontrou o serviço que precisa?
        </h3>
        <p className={cn('mb-4 text-sm text-gray-300', descriptionClassName)}>
          Faça um pedido personalizado no GetNinjas e encontre o profissional ideal para sua
          necessidade.
        </p>
        <Link
          href="https://www.getninjas.com.br"
          target="_blank"
          rel="noopener noreferrer"
          className={cn(
            'inline-block w-full rounded-md bg-white px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-50',
            buttonClassName
          )}
        >
          <span className="inline-block w-full text-center">
            Pedir serviço
            <span className="max-[408px]:block"> personalizado</span>
          </span>
        </Link>
      </div>
    );
  }

  return (
    <section
      className={cn(
        'relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 py-12 sm:py-16 lg:py-20',
        className
      )}
    >
      <div
        className="bg-grid-gray-300/[0.2] absolute inset-0 overflow-hidden bg-[size:20px_20px]"
        style={{
          maskImage: 'linear-gradient(to bottom, transparent, black)',
          WebkitMaskImage: 'linear-gradient(to bottom, transparent, black)',
        }}
      ></div>
      <div
        className={cn(
          'container relative z-10 mx-auto px-4 text-center sm:px-6 lg:px-8',
          containerClassName
        )}
      >
        {showIcon && (
          <Image
            src="/images/GetNinjas_Icon.svg"
            alt="GetNinjas Europe"
            width={64}
            height={64}
            className="h-16 w-auto md:h-24"
          />
        )}
        <h2
          className={cn(
            'mb-4 text-2xl font-bold leading-tight text-gray-800 sm:text-3xl md:text-4xl',
            titleClassName
          )}
        >
          Não encontrou o serviço que procura?
        </h2>
        <p
          className={cn(
            'mx-auto mb-6 max-w-2xl text-base text-gray-600 sm:text-lg md:text-xl',
            descriptionClassName
          )}
        >
          Faça um pedido personalizado no GetNinjas e encontre o profissional ideal para sua
          necessidade.
        </p>
        <div className="w-full">
          <Button
            asChild
            size="lg"
            className={cn(
              'w-full rounded-lg bg-gray-800 px-4 py-2 text-sm font-medium text-white transition duration-300 hover:bg-gray-700 sm:px-6 sm:py-3 sm:text-base',
              buttonClassName
            )}
          >
            <Link
              href="https://www.getninjas.com.br"
              target="_blank"
              rel="noopener noreferrer"
              className="w-full"
            >
              <span className="inline-block w-full text-center">
                Pedir serviço
                <span className="max-[408px]:block"> personalizado</span>
              </span>
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
