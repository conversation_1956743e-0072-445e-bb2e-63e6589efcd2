'use client';

import { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
}

/**
 * A client component that catches hydration errors and provides a fallback UI
 */
export class HydrationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_: Error): State {
    return { hasError: true };
  }

  componentDidCatch(_error: Error, _errorInfo: ErrorInfo) {
    // Silent error handling - errors are handled by showing fallback UI
  }

  render() {
    if (this.state.hasError) {
      // If there's a hydration error, try to recover by forcing a client-side render
      if (typeof window !== 'undefined') {
        // Reset the error state after a short delay to attempt recovery
        setTimeout(() => {
          this.setState({ hasError: false });
        }, 100);
      }

      // Show fallback UI
      return (
        this.props.fallback || (
          <div className="rounded-md border border-yellow-200 bg-yellow-50 p-4">
            <p className="text-yellow-800">
              Ocorreu um erro de carregamento. Tentando recuperar...
            </p>
          </div>
        )
      );
    }

    return this.props.children;
  }
}

export default HydrationErrorBoundary;
