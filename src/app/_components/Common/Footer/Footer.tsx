'use client';

import { trackClickEvent } from '@/src/app/_functions/analytics/common/trackClickEvent';
import { Instagram, Lock, Youtube } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { CancellationInfo } from '../../Pages/Home/CancellationInfo';

export default function Footer() {
  return (
    <footer>
      {/* Cancellation Info Section */}
      <CancellationInfo />

      {/* Main Footer */}
      <div className="border-t border-border bg-background 2xl:px-40">
        <div className="mx-auto px-4 py-8">
          <div className="flex flex-col space-y-10 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
            {/* Right side group */}
            <div className="flex flex-col items-center space-y-10 lg:order-1 lg:flex-row lg:items-center lg:justify-end lg:space-x-12 lg:space-y-0">
              {/* Site Seguro */}
              <div className="order-1 flex items-center space-x-2 text-muted-foreground lg:order-1">
                <Lock className="h-5 w-5" aria-hidden="true" />
                <span className="text-sm font-medium">Site seguro</span>
              </div>

              {/* Reclame Aqui */}
              <Link
                href={
                  process.env.NEXT_PUBLIC_RECLAME_AQUI_URL ||
                  'https://www.reclameaqui.com.br/empresa/getninjas/'
                }
                target="_blank"
                rel="noopener noreferrer"
                className="order-2 flex items-center gap-2 transition-opacity hover:opacity-80 lg:order-2"
                aria-label="GetNinjas no ReclameAqui"
                onClick={() => trackClickEvent('ReclameAqui')}
              >
                <Image
                  src={process.env.NEXT_PUBLIC_FOOTER_RATING_IMAGE_URL as string}
                  alt="Selo ReclameAqui"
                  width={100}
                  height={45}
                  className="h-auto w-auto"
                />
              </Link>

              {/* Social Icons */}
              <nav className="order-3 flex items-center space-x-4 lg:order-3">
                <Link
                  href={
                    process.env.NEXT_PUBLIC_INSTAGRAM_URL || 'https://www.instagram.com/getninjas'
                  }
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground transition-colors hover:text-primary"
                  aria-label="Siga o GetNinjas no Instagram"
                  onClick={() => trackClickEvent('Instagram')}
                >
                  <Instagram className="h-6 w-6" />
                </Link>

                <Link
                  href={
                    process.env.NEXT_PUBLIC_YOUTUBE_URL ||
                    'https://www.youtube.com/channel/UCWgyIuQWHB3u88SY1hr5zZg'
                  }
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground transition-colors hover:text-primary"
                  aria-label="Siga o GetNinjas no YouTube"
                  onClick={() => trackClickEvent('Youtube')}
                >
                  <Youtube className="h-6 w-6" />
                </Link>

                <Link
                  href={process.env.NEXT_PUBLIC_TIKTOK_URL || 'https://www.tiktok.com/@getninjas'}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground transition-colors hover:text-primary"
                  aria-label="Siga o GetNinjas no TikTok"
                  onClick={() => trackClickEvent('TikTok')}
                >
                  <Image
                    src="/images/tiktok.svg"
                    alt="TikTok"
                    width={24}
                    height={24}
                    className="h-6 w-6"
                  />
                </Link>
              </nav>
            </div>

            {/* Copyright - Left side */}
            <div className="order-4 text-center lg:order-2 lg:text-left">
              <p className="text-sm text-muted-foreground">
                © {new Date().getFullYear()}{' '}
                <Link
                  target="_blank"
                  href={process.env.NEXT_PUBLIC_GETNINJAS_URL || 'https://www.getninjas.com.br'}
                  className="hover:text-primary"
                  onClick={() => trackClickEvent('GetNinjas - Footer')}
                >
                  GetNinjas
                </Link>
                . Todos os direitos reservados.
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
