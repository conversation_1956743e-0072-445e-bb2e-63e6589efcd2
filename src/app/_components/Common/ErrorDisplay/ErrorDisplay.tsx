'use client';

import { Button } from '@/src/app/_components';

interface ErrorDisplayProps {
  error?: Error;
  message?: string;
  fullPage?: boolean;
  onRetry?: () => void;
}

export function ErrorDisplay({
  error,
  message = 'Ocorreu um erro. Por favor, tente novamente mais tarde.',
  fullPage = false,
  onRetry = () => window.location.reload(),
}: ErrorDisplayProps) {
  const containerClasses = fullPage
    ? 'flex min-h-[50vh] flex-col items-center justify-center p-4'
    : 'rounded-md border border-red-400 bg-red-100 p-4';

  return (
    <div className={containerClasses} role="alert">
      {error ? (
        <>
          <h2 className="mb-2 text-lg font-semibold text-red-800">
            Aconteceu alguma coisa errada:
          </h2>
          <pre className="whitespace-pre-wrap text-sm text-red-600">{error.message}</pre>
        </>
      ) : (
        <p className="mb-4 text-gray-600">{message}</p>
      )}
      {fullPage && (
        <Button onClick={onRetry} className="mt-4">
          Tentar novamente
        </Button>
      )}
    </div>
  );
}

export default ErrorDisplay;
