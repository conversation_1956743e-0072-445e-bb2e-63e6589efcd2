'use client';

import { useEffect, useRef } from 'react';

export function DelayedSurveyMonkeyScript() {
  const scriptLoaded = useRef(false);

  useEffect(() => {
    function handleScroll() {
      const scrollPosition = window.innerHeight + window.scrollY;
      const threshold = document.body.offsetHeight - 200; // 200px antes do fim

      if (scrollPosition >= threshold && !scriptLoaded.current) {
        scriptLoaded.current = true;

        const script = document.createElement('script');
        script.src =
          'https://widget.surveymonkey.com/collect/website/js/tRaiETqnLgj758hTBazgd94d4_2F5337WmKyXyyFb0ZZ1yx8d9n5Oj95R2dUpN6OKp.js';
        script.async = true;
        document.body.appendChild(script);

        // Remove listener após o carregamento
        window.removeEventListener('scroll', handleScroll);
      }
    }

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return null;
}
