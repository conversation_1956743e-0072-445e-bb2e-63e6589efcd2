'use client';

import { countries } from '@/src/app/_utils/countries';
import { cn } from '@/src/app/_utils/utils';
import { isValidBrazilianDDD } from '@/src/app/_utils/validDdds';
import type { CountryCode } from 'libphonenumber-js';
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import React, { useEffect, useState } from 'react';

interface PhoneInputProps {
  value: string | undefined;
  countryCode: string;
  onChange: (value: string) => void;
  placeholder?: string;
  onValidationError?: (error: string | null) => void;
}

export const PhoneInput: React.FC<PhoneInputProps> = ({
  value = '',
  countryCode,
  onChange,
  placeholder,
  onValidationError,
}) => {
  const [error, setError] = useState<string | null>(null);
  const [hasInteracted, setHasInteracted] = useState(false);

  // Validate the phone number whenever the value changes
  useEffect(() => {
    if (hasInteracted) {
      validatePhoneNumber(value);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, hasInteracted]);

  const validatePhoneNumber = (inputValue: string) => {
    let newError: string | null = null;

    if (!inputValue.trim()) {
      newError = 'Insira um número de telefone válido com DDD.';
    } else {
      const country = countries.find((c) => c.code === countryCode);
      if (!country) {
        newError = 'Código do país inválido.';
      } else {
        if (countryCode === '+55') {
          const digits = inputValue.replace(/\D/g, '');
          if (digits.length !== 11) {
            newError = 'Insira um número de telefone válido com DDD.';
          } else {
            // Validar se o DDD é válido no Brasil
            const ddd = digits.substring(0, 2);
            if (!isValidBrazilianDDD(ddd)) {
              newError = 'O DDD informado não é válido no Brasil.';
            }
          }
        }

        if (!newError) {
          try {
            const phoneNumber = parsePhoneNumberFromString(inputValue, country.iso as CountryCode);
            if (!phoneNumber || !phoneNumber.isValid()) {
              newError = 'Insira um número de telefone válido com DDD.';
            }
          } catch (_error) {
            // Silent error handling - just set the validation error message
            newError = 'Insira um número de telefone válido com DDD.';
          }
        }
      }
    }

    if (newError !== error) {
      setError(newError);
      if (onValidationError) onValidationError(newError);
    }

    return !newError;
  };

  const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    setHasInteracted(true);
    validatePhoneNumber(event.target.value);
  };

  return (
    <div className="flex flex-col">
      <input
        type="tel"
        placeholder={placeholder}
        value={value || ''}
        onChange={(e) => {
          onChange(e.target.value);
          setHasInteracted(true);
        }}
        onBlur={handleBlur}
        className={cn(
          'h-10 w-full rounded-md border border-input bg-background p-2 px-3 py-2 text-base ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
          error ? 'focus:ring-black-500 border-red-500' : 'border-gray-200 focus:ring-blue-500'
        )}
        aria-label="Seu número de telefone"
      />
      {error && <span className="mt-1 text-sm font-medium text-red-500">{error}</span>}
    </div>
  );
};
