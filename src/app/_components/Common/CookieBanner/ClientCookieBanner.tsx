'use client';

import dynamic from 'next/dynamic';
import { Suspense } from 'react';

// Dynamically import the CookieBanner component with SSR disabled
// This helps with performance by not blocking the initial page load
const DynamicCookieBanner = dynamic(
  () => import('./CookieBanner').then((mod) => mod.CookieBanner),
  {
    ssr: false,
    loading: () => null, // No loading state to prevent layout shift
  }
);

export function ClientCookieBanner() {
  return (
    <Suspense fallback={null}>
      <DynamicCookieBanner />
    </Suspense>
  );
}
