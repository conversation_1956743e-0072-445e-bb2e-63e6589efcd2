'use client';

import { But<PERSON> } from '@/src/app/_components/Ui/button';
import {
  COOKIE_CONSENT_EXPIRY,
  COOKIE_CONSENT_KEY,
  SECURE_COOKIE_OPTIONS,
} from '@/src/app/_constants/cookies';
import { trackClickEvent } from '@/src/app/_functions/analytics/common/trackClickEvent';
import Cookies from 'js-cookie';
import Link from 'next/link';
import { useEffect, useState } from 'react';

export function CookieBanner() {
  // Start with hidden state to prevent flash of content if cookie exists
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    try {
      // Check if user has already given consent
      const hasConsent = Cookies.get(COOKIE_CONSENT_KEY);
      if (hasConsent) {
        // Sync to window property for analytics gating
        if (typeof window !== 'undefined') {
          window.cookieConsent = 'granted';
          window.analyticsConsent = true;
        }
      } else {
        setIsVisible(true);
      }
    } catch (error) {
      // Silent error handling for cookie access issues
      console.error('Error checking cookie consent:', error);
    }
  }, []);

  const handleAccept = () => {
    try {
      // Set cookie with configured expiry
      Cookies.set(COOKIE_CONSENT_KEY, 'true', {
        expires: COOKIE_CONSENT_EXPIRY,
        ...SECURE_COOKIE_OPTIONS,
      });
      // Sync to window property for analytics gating
      if (typeof window !== 'undefined') {
        window.cookieConsent = 'granted';
        window.analyticsConsent = true;
      }
      setIsVisible(false);

      // Track the event
      trackClickEvent('cookie_consent_accepted');
    } catch (error) {
      console.error('Error setting cookie consent:', error);
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div
      id="cookie-banner"
      role="alert"
      aria-live="polite"
      className="fixed bottom-0 left-0 z-[100] w-full bg-black px-5 py-3 shadow-md"
    >
      <div className="mx-auto flex max-w-7xl items-center justify-between gap-4">
        <div className="text-xs font-medium text-white">
          <p>
            Este site usa cookies para garantir que você obtenha a melhor experiência em nosso site.{' '}
            <Link
              href="https://www.getninjas.com.br/termos-de-uso"
              target="_blank"
              rel="noopener noreferrer"
              className="text-white underline"
              onClick={() => trackClickEvent('cookie_consent_learn_more')}
              aria-label="Saiba mais sobre nossa política de cookies"
            >
              Saiba mais
            </Link>
          </p>
        </div>
        <Button
          onClick={handleAccept}
          className="rounded-xl px-4 py-2 text-sm font-medium"
          variant="outline"
          aria-label="Permitir o uso de cookies"
        >
          Permitir
        </Button>
      </div>
    </div>
  );
}
