import { trackClickEvent } from '@/src/app/_functions/analytics/common/trackClickEvent';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import Cookies from 'js-cookie';
import { CookieBanner } from './CookieBanner';
jest.mock('@/src/app/_functions/analytics/common/trackClickEvent', () => ({
  trackClickEvent: jest.fn(),
}));

jest.mock('js-cookie');

describe('CookieBanner', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset window properties
    delete window.cookieConsent;
    delete window.analyticsConsent;
  });

  it('shows banner if cookie is not set', () => {
    (Cookies.get as jest.Mock).mockReturnValue(undefined);
    render(<CookieBanner />);
    expect(screen.getByRole('alert')).toBeInTheDocument();
    expect(screen.getByText(/este site usa cookies/i)).toBeInTheDocument();
  });

  it('does not show banner if cookie is set', () => {
    (Cookies.get as jest.Mock).mockReturnValue('true');
    render(<CookieBanner />);
    expect(screen.queryByRole('alert')).not.toBeInTheDocument();
  });

  // Increase timeout to 10 seconds to prevent timeout failures
  it('sets cookie and window properties on accept', async () => {
    jest.setTimeout(10000);
    (Cookies.get as jest.Mock).mockReturnValue(undefined);
    const setMock = Cookies.set as jest.Mock;
    render(<CookieBanner />);
    const button = screen.getByRole('button', { name: /permitir/i });
    fireEvent.click(button);
    await waitFor(() => {
      expect(setMock).toHaveBeenCalledWith(
        expect.any(String),
        'true',
        expect.objectContaining({ expires: expect.any(Number) })
      );
      expect(window.cookieConsent).toBe('granted');
      expect(window.analyticsConsent).toBe(true);
      expect(trackClickEvent).toHaveBeenCalledWith('cookie_consent_accepted');
      expect(screen.queryByRole('alert')).not.toBeInTheDocument();
    });
  });

  it('syncs window properties if cookie is already set', () => {
    (Cookies.get as jest.Mock).mockReturnValue('true');
    render(<CookieBanner />);
    expect(window.cookieConsent).toBe('granted');
    expect(window.analyticsConsent).toBe(true);
  });

  it('handles errors gracefully when checking cookie', () => {
    (Cookies.get as jest.Mock).mockImplementation(() => {
      throw new Error('fail');
    });
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    render(<CookieBanner />);
    expect(errorSpy).toHaveBeenCalledWith('Error checking cookie consent:', expect.any(Error));
    errorSpy.mockRestore();
  });

  it('handles errors gracefully when setting cookie', () => {
    (Cookies.get as jest.Mock).mockReturnValue(undefined);
    (Cookies.set as jest.Mock).mockImplementation(() => {
      throw new Error('fail');
    });
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    render(<CookieBanner />);
    const button = screen.getByRole('button', { name: /permitir/i });
    fireEvent.click(button);
    expect(errorSpy).toHaveBeenCalledWith('Error setting cookie consent:', expect.any(Error));
    errorSpy.mockRestore();
  });

  it('tracks "Saiba mais" link click', () => {
    (Cookies.get as jest.Mock).mockReturnValue(undefined);
    render(<CookieBanner />);
    const saibaMais = screen.getByRole('link', { name: /saiba mais/i });
    fireEvent.click(saibaMais);
    expect(trackClickEvent).toHaveBeenCalledWith('cookie_consent_learn_more');
  });
});
