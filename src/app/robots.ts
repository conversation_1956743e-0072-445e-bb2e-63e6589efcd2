import { MetadataRoute } from 'next';

/**
 * Dynamically generates robots.txt content
 * This function is automatically called by Next.js when /robots.txt is requested
 */
export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://europ.getninjas.com.br';

  return {
    rules: [
      {
        userAgent: '*',
        allow: ['/', '/servicos/*'],
        disallow: [
          '/api/',
          '/_next/',
          '/static/debug/',
          '/checkout/',
          '/order/*/payment',
          '*/search?*europ=europa*',
          '*/search?*europe=europ*',
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: ['/', '/servicos/*'],
        disallow: ['/api/', '*/search?*europ=europa*', '*/search?*europe=europ*'],
      },
      {
        userAgent: 'Googlebot-Image',
        allow: ['/*.jpg$', '/*.jpeg$', '/*.png$', '/*.webp$', '/*.svg$', '/images/'],
      },
      {
        userAgent: 'Bingbot',
        allow: ['/', '/servicos/*'],
        disallow: ['/api/'],
      },
      {
        userAgent: 'Yandexbot',
        allow: ['/', '/servicos/*'],
        disallow: ['/api/'],
      },
      {
        userAgent: 'DuckDuckBot',
        allow: ['/', '/servicos/*'],
        disallow: ['/api/'],
      },
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  };
}
