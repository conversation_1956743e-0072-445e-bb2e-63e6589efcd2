import mixpanel from 'mixpanel-browser';
import { hasAnalyticsConsent } from './hasAnalyticsConsent';

const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN;

// Track whether Mixpanel has been initialized
let isMixpanelInitialized = false;
export const initMixpanel = () => {
  // Check if token is missing or empty
  if (!MIXPANEL_TOKEN) {
    console.warn('Mixpanel token is missing! Check your .env file.');
    return false;
  }

  try {
    mixpanel.init(MIXPANEL_TOKEN, {
      track_pageview: true,
      debug: process.env.NODE_ENV === 'development',
      persistence: 'cookie',
    });
    isMixpanelInitialized = true;

    // --- Mixpanel Replay (Session Recording) ---
    // Only start session recording if in browser, consent is granted, and not opted out
    // Use bracket notation for type safety and correct method name per Mixpanel SDK
    if (
      typeof window !== 'undefined' &&
      typeof (mixpanel as unknown as Record<string, () => void>)['start_session_recording'] ===
        'function' &&
      !window.MixpanelReplayOptOut &&
      hasAnalyticsConsent()
    ) {
      (mixpanel as unknown as Record<string, () => void>)['start_session_recording']();
    }
    // --- End Mixpanel Replay ---
    return true;
  } catch (error: unknown) {
    console.error('Failed to initialize Mixpanel:', error);
    return false;
  }
};

// Helper function to safely track events with Mixpanel
export const trackMixpanelEvent = (eventName: string, properties?: Record<string, unknown>) => {
  if (!isMixpanelInitialized || !hasAnalyticsConsent()) {
    // Silently fail if Mixpanel isn't initialized or consent not granted
    return;
  }

  try {
    mixpanel.track(eventName, properties);
  } catch (error: unknown) {
    console.error(`Failed to track Mixpanel event "${eventName}":`, error);
  }
};
