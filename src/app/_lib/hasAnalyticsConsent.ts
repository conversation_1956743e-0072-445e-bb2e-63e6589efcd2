// Centralized utility for checking analytics/cookie consent
export function hasAnalyticsConsent(): boolean {
  // First check if window is defined
  if (typeof window === 'undefined') return false;

  // Check each consent type separately for better testability
  const hasCookieConsent = window.cookieConsent === 'granted';
  const hasAnalytics = window.analyticsConsent === true;

  // Return true if either condition is met
  return hasCookieConsent || hasAnalytics;
}
