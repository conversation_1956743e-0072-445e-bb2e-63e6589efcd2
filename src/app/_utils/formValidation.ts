import { addDays } from 'date-fns';
import { CountryCode, parsePhoneNumberFromString } from 'libphonenumber-js';
import * as z from 'zod';
import { countries } from './countries';
import { isAfternoonPeriodAvailable, isMorningPeriodAvailable } from './dateUtils';
import { isValidBrazilianDDD } from './validDdds';

// CPF validation function
export const isValidBrazilianCPF = (cpf: string) => {
  // Defensive: return false if input is not a string
  if (typeof cpf !== 'string' || !cpf) {
    return false;
  }

  // Remove non-digit characters
  const cleanCpf = cpf.replace(/[^\d]+/g, '');

  // Check if CPF has 11 digits and is not all the same digit
  if (cleanCpf.length !== 11 || /^(\d)\1+$/.test(cleanCpf)) {
    return false;
  }

  // Convert to array of digits
  const digits = cleanCpf.split('').map((x) => parseInt(x, 10));

  // Validate first check digit
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += digits[i] * (10 - i);
  }
  let checkDigit1 = 11 - (sum % 11);
  if (checkDigit1 === 10 || checkDigit1 === 11) {
    checkDigit1 = 0;
  }
  if (checkDigit1 !== digits[9]) {
    return false;
  }

  // Validate second check digit
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += digits[i] * (11 - i);
  }
  let checkDigit2 = 11 - (sum % 11);
  if (checkDigit2 === 10 || checkDigit2 === 11) {
    checkDigit2 = 0;
  }
  if (checkDigit2 !== digits[10]) {
    return false;
  }

  return true;
};

// CEP validation function
export const isValidBrazilianPostalCode = (cep: string) => {
  // Defensive: return false if input is not a string
  if (typeof cep !== 'string' || !cep) {
    return false;
  }

  // Check if CEP has the correct format (5 digits, hyphen, 3 digits)
  const cepRegex = /^[0-9]{5}-[0-9]{3}$/;
  return cepRegex.test(cep);
};

export const checkoutFormSchema = z
  .object({
    firstName: z
      .string()
      .nonempty('Insira seu nome.')
      .min(2, 'O nome deve ter pelo menos 2 caracteres.')
      .transform((value) => value.trim()),
    lastName: z
      .string()
      .nonempty('Insira seu sobrenome.')
      .min(2, 'O sobrenome deve ter pelo menos 2 caracteres.')
      .transform((value) => value.trim()),
    countryCode: z
      .string({
        required_error: 'Informe o código do país',
      })
      .nonempty('Informe o código do país'),

    phone: z.string().nonempty('Insira um número de telefone válido com DDD.'),
    cpf: z
      .string()
      .max(14, 'CPF inválido. Verifique os números digitados.')
      .refine(isValidBrazilianCPF, 'CPF inválido. Verifique os números digitados.'),
    cep: z
      .string()
      .refine(
        isValidBrazilianPostalCode,
        'CEP inválido. Digite apenas números no formato 00000-000.'
      )
      .transform((value) => value.trim()),
    street: z
      .string()
      .nonempty('Insira o nome da rua, avenida, alameda, etc.')
      .transform((value) => value.trim()),
    streetNumber: z
      .string()
      .nonempty('Insira o número do endereço.')
      .transform((value) => value.trim()),
    complement: z
      .string()
      .optional()
      .transform((value) => (value ? value.trim() : value)),
    city: z
      .string()
      .min(2, 'Insira o nome completo da cidade.')
      .transform((value) => value.trim()),
    state: z.string().length(2, 'Selecione um estado válido da lista.'),
    neighborhood: z
      .string()
      .nonempty('Insira o nome do bairro.')
      .transform((value) => value.trim()),
    email: z
      .string()
      .email('Insira um endereço de e-mail válido.')
      .transform((value) => value.trim()),
    date: z.date().refine((date) => {
      const now = new Date();
      const tomorrow = addDays(now, 1);
      tomorrow.setHours(0, 0, 0, 0);
      return date >= tomorrow;
    }),
    period: z.enum(['morning', 'afternoon']),
    terms: z.boolean().refine((value) => value === true, {
      message: 'É necessário aceitar as condições gerais para prosseguir com o agendamento.',
    }),
  })
  .superRefine((data, ctx) => {
    const { phone } = data;
    // Use Brazil for phone validation
    const brazilCountry = countries.find((c) => c.code === '+55');
    if (!brazilCountry) {
      // This should never happen, but we'll handle it just in case
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Configuração de país inválida.',
        path: ['phone'],
      });
      return;
    }

    // For Brazilian numbers, validate that it has exactly 11 digits
    const digits = phone.replace(/\D/g, '');
    if (digits.length !== 11) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'O número de telefone deve ter exatamente 11 dígitos, incluindo o DDD.',
        path: ['phone'],
      });
      return;
    }

    // Validate DDD
    const ddd = digits.substring(0, 2);
    if (!isValidBrazilianDDD(ddd)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'O DDD informado não é válido no Brasil.',
        path: ['phone'],
      });
      return;
    }

    try {
      const phoneNumber = parsePhoneNumberFromString(phone, brazilCountry.iso as CountryCode);
      if (!phoneNumber?.isValid()) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Insira um número de telefone válido com DDD.',
          path: ['phone'],
        });
      }
    } catch (_error) {
      // Silent error handling - just add validation issue
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Insira um número de telefone válido com DDD.',
        path: ['phone'],
      });
    }

    // Validate date and period selection according to 24-hour rule
    if (data.date && data.period) {
      if (data.period === 'morning' && !isMorningPeriodAvailable(data.date)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'O período da manhã não está disponível para a data selecionada.',
          path: ['period'],
        });
      }

      if (data.period === 'afternoon' && !isAfternoonPeriodAvailable(data.date)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'O período da tarde não está disponível para a data selecionada.',
          path: ['period'],
        });
      }
    }
  });

export type CheckoutFormSchema = z.infer<typeof checkoutFormSchema>;
