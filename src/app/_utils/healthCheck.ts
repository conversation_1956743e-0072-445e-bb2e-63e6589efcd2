/**
 * Utility functions for performing health checks
 */

import axios from 'axios';

/**
 * Performs a health check and returns a boolean indicating if the application is healthy
 */
export async function checkApplicationHealth(baseUrl?: string): Promise<boolean> {
  try {
    // Use provided baseUrl or default to a relative path (for server-side use)
    const url = baseUrl ? `${baseUrl}/api/health` : '/api/health';
    await axios.get(url, {
      headers: {
        'Cache-Control': 'no-cache',
      },
    });

    return true;
  } catch (_error) {
    // Silent error handling - just return false to indicate failure
    return false;
  }
}

/**
 * Performs a deep health check to verify all dependencies
 */
export async function checkDeepHealth(baseUrl?: string): Promise<{
  healthy: boolean;
  details?: Record<string, unknown>;
}> {
  try {
    // Use provided baseUrl or default to a relative path (for server-side use)
    const url = baseUrl ? `${baseUrl}/api/health/deep` : '/api/health/deep';
    const { data } = await axios.get(url, {
      headers: {
        'Cache-Control': 'no-cache',
      },
    });

    return {
      healthy: data.status === 'ok',
      details: data,
    };
  } catch (_error) {
    // Silent error handling - just return unhealthy status
    return { healthy: false };
  }
}

/**
 * Gets the current application status as a string
 */
export function getHealthStatusText(isHealthy: boolean): string {
  return isHealthy ? 'Operational' : 'Experiencing Issues';
}

/**
 * Returns a formatted timestamp string for the health check
 */
export function getHealthCheckTimestamp(): string {
  return new Date().toISOString();
}
