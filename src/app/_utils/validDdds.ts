// Lista de DDDs válidos do Brasil
export const validBrazilianDDDs = [
  '11',
  '12',
  '13',
  '14',
  '15',
  '16',
  '17',
  '18',
  '19', // São Paulo
  '21',
  '22',
  '24',
  '27',
  '28', // Rio de Janeiro e Espírito Santo
  '31',
  '32',
  '33',
  '34',
  '35',
  '37',
  '38', // Minas Gerais
  '41',
  '42',
  '43',
  '44',
  '45',
  '46',
  '47',
  '48',
  '49', // Paraná e Santa Catarina
  '51',
  '53',
  '54',
  '55', // Rio Grande do Sul
  '61',
  '62',
  '63',
  '64',
  '65',
  '66',
  '67',
  '68',
  '69', // Centro-Oeste e Tocantins
  '71',
  '73',
  '74',
  '75',
  '77',
  '79', // Bahia e Sergipe
  '81',
  '82',
  '83',
  '84',
  '85',
  '86',
  '87',
  '88',
  '89', // Nordeste
  '91',
  '92',
  '93',
  '94',
  '95',
  '96',
  '97',
  '98',
  '99', // Norte
];

/**
 * Verifica se um DDD é válido no Brasil
 * @param ddd O DDD a ser validado
 * @returns true se o DDD for válido, false caso contrário
 */
export function isValidBrazilianDDD(ddd: string): boolean {
  return validBrazilianDDDs.includes(ddd);
}

/**
 * Obtém um DDD válido ou retorna um padrão
 * @param ddd O DDD a ser validado
 * @param defaultDDD O DDD padrão a ser retornado se o DDD for inválido
 * @returns O DDD original se for válido, ou o DDD padrão caso contrário
 */
export function getValidBrazilianDDD(ddd: string, defaultDDD: string = '11'): string {
  return isValidBrazilianDDD(ddd) ? ddd : defaultDDD;
}
