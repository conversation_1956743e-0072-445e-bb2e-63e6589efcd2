/**
 * Capitalizes the first letter of a string after trimming whitespace
 * @param str - The string to capitalize
 * @returns The capitalized string
 */
export function capitalizeFirstLetter(str: string): string {
  if (!str || typeof str !== 'string') return str;

  // Trim the string to remove leading/trailing whitespace
  const trimmed = str.trim();
  if (trimmed.length === 0) return str;

  return trimmed.charAt(0).toUpperCase() + trimmed.slice(1);
}

/**
 * Capitalizes the first letter of each string in an array
 * @param items - Array of strings to capitalize
 * @returns Array with each string capitalized
 */
export function capitalizeArrayItems(items: string[]): string[] {
  if (!items || !Array.isArray(items)) return items;

  return items.map((item) => {
    // Handle leading whitespace
    if (typeof item !== 'string') return item;

    // Preserve original whitespace in the string
    const leadingWhitespace = item.match(/^\s*/)?.[0] || '';
    const trimmed = item.trim();

    if (trimmed.length === 0) return item;

    // Return with original leading whitespace + capitalized content
    return leadingWhitespace + trimmed.charAt(0).toUpperCase() + trimmed.slice(1);
  });
}

/**
 * Capitalizes the first letter of each sentence in a string
 * @param str - The string containing multiple sentences
 * @returns String with each sentence capitalized
 */
export function capitalizeSentences(str: string): string {
  if (!str || typeof str !== 'string') return str;

  // Split by sentence-ending punctuation followed by whitespace
  // This regex looks for .!? followed by whitespace and captures the punctuation and whitespace
  return str.replace(/(^|\.\s+|\!\s+|\?\s+)([a-z])/g, (match, p1, p2) => {
    return p1 + p2.toUpperCase();
  });
}

/**
 * Capitalizes all sentences in each array item, handling whitespace and multiple sentences per item
 * @param items - Array of strings to process
 * @returns Array with properly capitalized content
 */
export function capitalizeDetailedItems(items: string[]): string[] {
  if (!items || !Array.isArray(items)) return items;

  return items.map((item) => {
    if (typeof item !== 'string') return item;

    // First, handle multi-sentence items by capitalizing each sentence
    const sentencesCapitalized = capitalizeSentences(item);

    // Then ensure the first character is capitalized (even if it's not after a period)
    const trimmed = sentencesCapitalized.trim();
    if (trimmed.length === 0) return item;

    // Preserve original whitespace
    const leadingWhitespace = item.match(/^\s*/)?.[0] || '';

    return leadingWhitespace + trimmed.charAt(0).toUpperCase() + trimmed.slice(1);
  });
}
