import { Category, Service, Subcategory } from '@/src/app/_context/ServiceContext';
import type { ServiceType } from '@/src/app/_interfaces';
import {
  CarouselCategory,
  CarouselService,
  CarouselSubcategory,
} from '@/src/app/_interfaces/carousel';

/**
 * Converts a Service object to a CarouselService object
 * Ensures all required properties are present
 */
export function convertToCarouselService(service: Service): CarouselService {
  return {
    id: service.id,
    name: service.name,
    slug: service.slug,
    description: service.description,
    imageUrl: service.imageUrl || '',
    status: service.status,
    price: service.price,
    provider: {
      id: service.provider.id,
      name: service.provider.name,
      imageUrl: service.provider.imageUrl || '',
      providerUrl: service.provider.providerUrl,
      description: service.provider.description || '',
    },
  };
}

/**
 * Converts a Subcategory object to a CarouselSubcategory object
 */
export function convertToCarouselSubcategory(subcategory: Subcategory): CarouselSubcategory {
  return {
    id: subcategory.id,
    name: subcategory.name,
    slug: subcategory.slug,
    services: subcategory.services.map(convertToCarouselService),
  };
}

/**
 * Converts a Category object to a CarouselCategory object
 */
export function convertToCarouselCategory(category: Category): CarouselCategory {
  return {
    id: category.id,
    name: category.name,
    slug: category.slug,
    subcategories: category.subcategories.map(convertToCarouselSubcategory),
  };
}

/**
 * Converts an array of Category objects to an array of CarouselCategory objects
 * This ensures all required properties are present for the ServiceCarousel component
 */
export function convertToCarouselCategories(categories: Category[]): CarouselCategory[] {
  return categories.map(convertToCarouselCategory);
}

/**
 * Converts a Service object from the context to a ServiceType object
 * Ensures all required properties are present
 */
export function convertToServiceType(service: Service): ServiceType {
  return {
    id: service.id,
    name: service.name,
    slug: service.slug,
    description: service.description,
    imageUrl: service.imageUrl || '',
    status: service.status,
    price: {
      priceId: service.price.priceId,
      originalPrice: service.price.originalPrice,
      discountPrice: service.price.discountPrice,
      finalPrice: service.price.finalPrice,
    },
    provider: {
      id: service.provider.id,
      name: service.provider.name,
      imageUrl: service.provider.imageUrl || '',
      providerUrl: service.provider.providerUrl,
      description: service.provider.description || '',
    },
    availableIn: service.availableIn,
    details: service.details,
    serviceLimits: service.serviceLimits || '',
    keywords: service.keywords,
    termsConditionsUrl: service.termsConditionsUrl || '',
    preparations: service.preparations || '',
    categoryName: service.categoryName,
    categorySlug: service.categorySlug,
    subcategoryName: service.subcategoryName,
    subcategorySlug: service.subcategorySlug,
  };
}

/**
 * Converts an array of Service objects to an array of ServiceType objects
 * This ensures all required properties are present
 */
export function convertToServiceTypes(services: Service[]): ServiceType[] {
  return services.map(convertToServiceType);
}
