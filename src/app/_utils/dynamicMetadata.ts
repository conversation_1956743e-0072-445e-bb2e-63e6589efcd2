import { serviceType<PERSON>pi } from '@/src/app/_services/serviceTypeApi';
import { Metadata } from 'next';
import { getServiceDescription } from './metadataHelpers';

// Enhanced metadata type that includes jsonLd
export type EnhancedMetadata = Metadata & {
  jsonLd?: Record<string, unknown>;
};

// Export createFallbackMetadata for testing
export { createFallbackMetadata };

/**
 * Creates fallback metadata when service data is unavailable
 *
 * @param serviceName - Formatted service name
 * @param slug - Original service slug
 * @param subcategorySlug - Subcategory slug for URL construction
 * @param baseUrl - Base URL for canonical links
 * @returns Metadata object with SEO-friendly fallback values
 */
function createFallbackMetadata(
  serviceName: string,
  slug: string,
  subcategorySlug: string,
  baseUrl: string = process.env.NEXT_PUBLIC_BASE_URL || 'https://europ.getninjas.com.br'
): EnhancedMetadata {
  // Use default values if parameters are missing
  const name = serviceName || 'Serviço';
  const serviceSlug = slug || 'servico';
  const category = subcategorySlug || 'categoria';
  const url = baseUrl || 'https://europ.getninjas.com.br';

  // Create SEO-friendly fallback metadata using the service name
  return {
    title: `${name} | GetNinjas + Europ Assistance`,
    description: `Contrate ${name} com profissionais qualificados e garantia de serviço. Agende agora mesmo.`,
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-snippet': -1,
        'max-image-preview': 'large',
      },
    },
    openGraph: {
      title: `${name} | GetNinjas + Europ Assistance`,
      description: `Contrate ${name} com profissionais qualificados e garantia de serviço. Agende agora mesmo.`,
      url: `${url}/servicos/${category}?=${serviceSlug}`,
      siteName: 'GetNinjas + Europ Assistance',
      type: 'website',
      locale: 'pt-BR',
    },
    twitter: {
      card: 'summary',
      title: `${name} | GetNinjas + Europ Assistance`,
      description: `Contrate ${name} com profissionais qualificados e garantia de serviço.`,
    },
    keywords: serviceSlug
      .split('-')
      .concat(['europ', 'assistance', 'getninjas', 'serviço', 'profissional'])
      .join(', '),
    alternates: {
      canonical: `${url}/servicos/${category}?=${serviceSlug}`,
    },
  };
}

/**
 * Generates comprehensive SEO metadata for a service page
 *
 * @param slug - The service slug to fetch data for
 * @param subcategorySlug - The subcategory slug for canonical URL construction
 * @param baseUrl - The base URL for canonical links and absolute URLs
 * @returns Enhanced SEO metadata including structured data, social cards, and canonical URLs
 */
export async function generateDynamicMetadata(
  slug: string,
  subcategorySlug: string,
  baseUrl: string = process.env.NEXT_PUBLIC_BASE_URL || 'https://europ.getninjas.com.br'
): Promise<EnhancedMetadata> {
  if (!slug) {
    return {
      title: 'Carregando serviço... | GetNinjas',
      description: 'Encontre os melhores serviços no GetNinjas.',
    };
  }

  try {
    // Format the slug for use in fallback metadata if needed
    // Remove any provider suffix (like -europ-assistance) for cleaner display
    const cleanSlug = slug.replace(/-europ-assistance$/, '');

    const formattedServiceName = cleanSlug
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    // Fetch service data using the service layer
    const response = await serviceTypeApi.getServiceTypeBySlug(slug);

    // More robust validation of the response
    if (!response || typeof response !== 'object') {
      // Silent handling - use fallback metadata
      return createFallbackMetadata(formattedServiceName, slug, subcategorySlug, baseUrl);
    }

    const { service } = response;

    if (!service || typeof service !== 'object' || !service.name) {
      // This should rarely happen due to our improved fallback in the API service
      // Silent handling - use fallback metadata
      return createFallbackMetadata(formattedServiceName, slug, subcategorySlug, baseUrl);
    }

    // Additional validation for critical properties
    if (!service.description) {
      // Silent handling - use fallback description
      service.description = `${service.name} com profissionais qualificados e garantia de serviço. Agende agora mesmo.`;
    }

    // Create service URL
    const serviceUrl = `${baseUrl}/servicos/${subcategorySlug}?=${slug}`;

    // Prepare service image for metadata
    const serviceImage = service.imageUrl
      ? {
          url: service.imageUrl,
          width: 1200,
          height: 630,
          alt: `${service.name} - imagem`,
        }
      : null;

    // Default image if none provided
    const defaultImage = {
      url: `${baseUrl}/images/getninjas_europ_logo.svg`,
      width: 1200,
      height: 630,
      alt: service.name || 'Serviço profissional',
    };

    // Use service images if available, otherwise fallback to default
    const metadataImages = serviceImage ? [serviceImage] : [defaultImage];

    // Get current date for modified/published
    const currentDate = new Date().toISOString();

    // Extract keywords from service properties
    const keywords =
      service.keywords && Array.isArray(service.keywords)
        ? service.keywords.join(', ')
        : `serviços, ${service.name}, profissionais`;

    // Create JSON-LD structured data
    const jsonLd = {
      '@context': 'https://schema.org',
      '@type': 'Service',
      name: service.name,
      description: service.description,
      url: serviceUrl,
      provider: {
        '@type': 'Organization',
        name: service.provider.name,
        url: service.provider.providerUrl,
        logo: service.provider.imageUrl,
      },
      offers: {
        '@type': 'Offer',
        price: service.price.finalPrice,
        priceCurrency: 'BRL',
      },
      image: service.imageUrl,
    };

    // Helper functions are imported from metadataHelpers.ts

    // Get service name with fallback
    const serviceName = service.name ? service.name : 'Serviço';

    // Get service description
    const serviceDesc = getServiceDescription(service);

    // Create metadata object with properly typed properties
    const metadata: Metadata = {
      title: serviceName,
      description: serviceDesc,
      keywords: keywords,
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-snippet': -1,
          'max-image-preview': 'large',
        },
      },
      openGraph: {
        title: `${serviceName} | GetNinjas + Europ Assistance`,
        description: serviceDesc,
        url: serviceUrl,
        siteName: 'GetNinjas + Europ Assistance',
        images: metadataImages,
        locale: 'pt-BR',
        type: 'website',
      },
      twitter: {
        card: 'summary_large_image',
        title: serviceName,
        description: serviceDesc,
        images: metadataImages,
      },
      alternates: {
        canonical: serviceUrl,
        languages: {
          'pt-BR': serviceUrl,
        },
      },
      other: {
        'og:updated_time': currentDate,
      },
    };

    // Add JSON-LD to metadata
    const metadataWithJsonLd = {
      ...metadata,
      jsonLd,
    };

    return metadataWithJsonLd as Metadata;
  } catch (_error) {
    // Silent error handling - use fallback metadata

    // Format the slug for use in fallback metadata
    // Remove any provider suffix (like -europ-assistance) for cleaner display
    const cleanSlug = slug.replace(/-europ-assistance$/, '');

    const formattedServiceName = cleanSlug
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    // Use our helper function to create fallback metadata
    return createFallbackMetadata(formattedServiceName, slug, subcategorySlug, baseUrl);
  }
}
