import { CarouselService } from '@/src/app/_interfaces';
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

/**
 * Handles redirecting to a service page and tracking analytics events
 * @param router Next.js router instance
 * @param subcategorySlug The subcategory slug
 * @param services The services array
 * @param subcategoryName The subcategory name
 */
export function handleServiceRedirect(
  router: AppRouterInstance,
  subcategorySlug: string,
  services: CarouselService[],
  subcategoryName: string
): void {
  const firstService = services?.[0];

  // GA4 via dataLayer
  if (typeof window !== 'undefined' && window.dataLayer) {
    window.dataLayer.push({
      event: 'view_item',
      ecommerce: {
        items: [
          {
            item_name: subcategoryName,
            item_category: subcategorySlug,
            item_list_name: 'Carrossel de Serviços',
          },
        ],
      },
    });
  }

  // Redirect to the service page
  // No need to store in sessionStorage anymore, the service context is available throughout the app
  router.push(`/servicos/${subcategorySlug}?=${firstService?.slug}`);
}
