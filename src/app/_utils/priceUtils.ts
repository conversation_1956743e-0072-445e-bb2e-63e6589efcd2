import { CarouselCategory, CarouselService, CarouselSubcategory } from '@/src/app/_interfaces';

/**
 * Gets the lowest price from a category's subcategories and services
 * @param category The category to get the lowest price from
 * @returns The lowest price as a number, or null if no prices are found
 */
export function getLowestPrice(category: CarouselCategory): number | null {
  const prices = category.subcategories
    ?.flatMap((subcategory: CarouselSubcategory) =>
      subcategory.services?.map((service: CarouselService) => service.price?.finalPrice)
    )
    .filter((price: number | undefined) => price !== undefined) as number[];

  return prices.length > 0 ? Math.min(...prices) : null;
}
