import axios from 'axios';
import Cookies from 'js-cookie';

const axiosInstance = axios.create({
  headers: {
    'Content-Type': 'application/json',
    'service-provider': 'EUR',
  },
});

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // If the URL starts with /api, it's an internal route, otherwise use the external API base URL
    if (!config.url?.startsWith('/api')) {
      config.baseURL = process.env.NEXT_PRIVATE_API_BASE_URL;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      Cookies.remove('token');
      // You might want to redirect to login page here
    }
    return Promise.reject(error);
  }
);

export { axiosInstance };
