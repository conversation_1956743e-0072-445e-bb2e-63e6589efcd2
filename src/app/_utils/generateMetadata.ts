import { ServiceApiService } from '@/src/app/_services/serviceApi';
import { Metadata, ResolvingMetadata } from 'next';

/**
 * Generates comprehensive SEO metadata for a service page
 *
 * @param slug - The service slug to fetch data for
 * @param baseUrl - The base URL for canonical links and absolute URLs (default: process.env.NEXT_PUBLIC_BASE_URL)
 * @param parent - Parent metadata for inheritance
 * @returns Enhanced SEO metadata including structured data, social cards, and canonical URLs
 */
export async function generateMetadata(
  slug: string,
  baseUrl: string = process.env.NEXT_PUBLIC_BASE_URL || 'https://euro.getninjas.com.br',
  parent?: ResolvingMetadata
): Promise<Metadata> {
  try {
    const { service } = await ServiceApiService.getServiceBySlug(slug);

    if (!service) {
      return {
        title: 'Serviço não encontrado',
        description: 'O serviço solicitado não foi encontrado.',
        robots: {
          index: false,
          follow: false,
        },
      };
    }

    // Get parent metadata if available
    const previousImages = parent ? (await parent).openGraph?.images || [] : [];

    // Create service URL
    const serviceUrl = `${baseUrl}/service/${slug}`;

    // Prepare service image for metadata
    const serviceImage = service.imageUrl
      ? {
          url: service.imageUrl,
          width: 1200,
          height: 630,
          alt: `${service.name} - imagem`,
        }
      : null;

    // Combine with any parent images
    const allImages = serviceImage ? [serviceImage, ...previousImages] : [...previousImages];

    // Default image if none provided
    const defaultImage = {
      url: `${baseUrl}/images/getninjas-europ-logo.svg`,
      width: 1200,
      height: 630,
      alt: service.name || 'Serviço profissional',
    };

    // Use service images if available, otherwise fallback to default
    const metadataImages = allImages.length > 0 ? allImages : [defaultImage];

    // Get current date for modified/published
    const currentDate = new Date().toISOString();

    // Extract keywords from service properties
    const keywords =
      Array.isArray(service.keywords) && service.keywords.length
        ? service.keywords.join(', ')
        : `serviços, ${service.name}, profissionais`;

    // Helper function to extract URL from various image types
    function getImageUrl(
      img: unknown,
      baseUrl: string = process.env.NEXT_PUBLIC_BASE_URL || 'https://euro.getninjas.com.br'
    ): string {
      if (img === null || img === undefined) {
        return `${baseUrl}/images/getninjas-europ-logo.svg`;
      }
      if (typeof img === 'string') return img;
      if (img instanceof URL) return img.toString();
      if (typeof img === 'object') {
        if ('url' in img) {
          const urlValue = (img as { url: string | URL }).url;
          if (urlValue === null || urlValue === undefined) {
            return `${baseUrl}/images/getninjas-europ-logo.svg`;
          }
          return typeof urlValue === 'string' ? urlValue : urlValue.toString();
        }
        if ('src' in img) {
          const srcValue = (img as { src: string }).src;
          if (srcValue === null || srcValue === undefined) {
            return `${baseUrl}/images/getninjas-europ-logo.svg`;
          }
          return srcValue;
        }
      }
      return `${baseUrl}/images/getninjas-europ-logo.svg`;
    }

    // Create metadata object with properly typed properties
    const metadata: Metadata = {
      title: service.name || 'Serviço',
      description:
        service.description ||
        `Contrate serviços com profissionais qualificados e garantia de serviço.`,
      keywords: keywords,
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
        },
      },
      openGraph: {
        title: `${service.name || 'Serviço'} | Serviços Profissionais`,
        description:
          service.description ||
          `Contrate serviços com profissionais qualificados e garantia de serviço.`,
        url: serviceUrl,
        siteName: 'Serviços Profissionais',
        images: metadataImages, // keep as objects for OpenGraph
        locale: 'pt_BR',
        type: 'website',
      },
      twitter: {
        card: 'summary_large_image',
        title: service.name || 'Serviço',
        description:
          service.description ||
          `Contrate serviços com profissionais qualificados e garantia de serviço.`,
        images: metadataImages
          .map((img) => getImageUrl(img, baseUrl))
          .filter((url): url is string => typeof url === 'string' && url !== ''),
        site: '@YourTwitterHandle',
        creator: '@YourTwitterHandle',
      },
      authors: [{ name: 'Serviços Profissionais' }],
      publisher: 'Serviços Profissionais',
      formatDetection: {
        telephone: true,
        address: true,
        email: true,
      },
      alternates: {
        canonical: serviceUrl,
        languages: {
          'pt-BR': serviceUrl,
        },
      },
      other: {
        'og:updated_time': currentDate,
      },
    };

    // Create the JSON-LD structured data
    const jsonLd = {
      '@context': 'https://schema.org',
      '@type': 'Service',
      name: service.name,
      description: service.description,
      url: serviceUrl,
      provider: {
        '@type': 'Organization',
        name: service.provider?.name || 'Serviços Profissionais',
        url: service.provider?.providerUrl || baseUrl,
        image: service.provider?.imageUrl,
        description: service.provider?.description,
      },
      areaServed:
        service.availableIn && service.availableIn.length > 0
          ? service.availableIn.join(', ')
          : 'Brasil',
      serviceType:
        service.keywords && service.keywords.length > 0
          ? service.keywords[0]
          : 'Professional Service',
      image: metadataImages[0] ? getImageUrl(metadataImages[0]) : undefined,
      offers: service.price
        ? {
            '@type': 'Offer',
            price: service.price.finalPrice,
            priceCurrency: 'BRL',
            availability: 'https://schema.org/InStock',
          }
        : undefined,
      mainEntityOfPage: serviceUrl,
      datePublished: currentDate,
      dateModified: currentDate,
    };

    // Add JSON-LD to metadata using a more type-safe approach
    const metadataWithJsonLd = {
      ...metadata,
      // Using unknown as intermediate step for type safety
      jsonLd: jsonLd as unknown as Record<string, unknown>,
    };

    return metadataWithJsonLd as Metadata;
  } catch (error) {
    console.error('Error generating metadata:', error);

    // Create fallback metadata
    const fallbackMetadata: Metadata = {
      title: 'Serviços Profissionais',
      description: 'Encontre os melhores profissionais para seus serviços.',
      robots: {
        index: true,
        follow: true,
      },
      openGraph: {
        title: 'Serviços Profissionais',
        description: 'Encontre os melhores profissionais para seus serviços.',
        url: `${baseUrl}/service/${slug}`,
        siteName: 'Serviços Profissionais',
        type: 'website',
        locale: 'pt_BR',
        images: [
          {
            url: `${baseUrl}/images/getninjas-europ-logo.svg`,
            width: 1200,
            height: 630,
            alt: 'Serviços Profissionais - imagem',
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title: 'Serviços Profissionais',
        description: 'Encontre os melhores profissionais para seus serviços.',
        images: [`${baseUrl}/images/getninjas-europ-logo.svg`],
        site: '@YourTwitterHandle',
        creator: '@YourTwitterHandle',
      },
      alternates: {
        canonical: `${baseUrl}/service/${slug}`,
      },
    };

    return fallbackMetadata;
  }
}
