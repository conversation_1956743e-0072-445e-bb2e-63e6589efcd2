export type Country = {
  code: string;
  iso: string;
  name: string;
};

export const countries: Country[] = [
  { code: '+55', iso: 'BR', name: 'Brasil' },
  { code: '+1', iso: 'US', name: 'Estados Unidos' },
  { code: '+54', iso: 'AR', name: 'Argentina' },
  { code: '+56', iso: 'CL', name: 'Chile' },
  { code: '+57', iso: 'CO', name: 'Colômbia' },
  { code: '+52', iso: 'MX', name: 'México' },
  { code: '+51', iso: 'PE', name: 'Peru' },
  { code: '+598', iso: 'UY', name: 'Uruguai' },
  { code: '+33', iso: 'FR', name: 'Fran<PERSON>' },
  { code: '+49', iso: 'DE', name: 'Aleman<PERSON>' },
  { code: '+34', iso: 'ES', name: 'Espanha' },
  { code: '+39', iso: 'IT', name: 'Itália' },
  { code: '+351', iso: 'PT', name: 'Portugal' },
  // Add additional countries as needed
];
