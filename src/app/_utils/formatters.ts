/**
 * Formats a date string into a localized Brazilian date format
 */
export function formatDate(dateString: string): string {
  // Get year, month, and day directly from the date string
  // to avoid timezone issues in Date object creation
  const [year, month, day] = dateString.split('-').map(Number);

  // Create a formatted date string using the parts
  const formatter = new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: 'long',
    year: 'numeric',
  });

  // Use a fixed date constructor that won't be affected by timezone
  // Month is 0-indexed in JavaScript Date
  const date = new Date(year, month - 1, day);

  return formatter.format(date);
}

/**
 * Formats a period string (morning/afternoon) into a human-readable format
 */
export function formatPeriod(period: string): string {
  return period === 'morning' ? 'Manhã (8h - 12h)' : 'Tarde (13h - 18h)';
}

/**
 * Truncates text to a specified number of characters and adds ellipsis
 */
export function truncateText(text: string | string[] | undefined, maxChars: number = 90): string {
  if (!text) return '';

  const formattedText = Array.isArray(text) ? text.join(', ') : text;

  if (formattedText.length <= maxChars) return formattedText;
  return formattedText.slice(0, maxChars) + '...';
}

/**
 * Formats a CPF string into a formatted string with dots and dash
 */
export const formatCPF = (cpf: string): string => {
  if (!cpf) return '';

  // Remove any non-digit characters
  const digits = cpf.replace(/\D/g, '');

  // Format as XXX.XXX.XXX-XX
  return digits.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
};
