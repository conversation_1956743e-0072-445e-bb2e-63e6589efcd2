/**
 * Helper function to get image URL or pass through the object
 * @param img - The image to process
 * @returns The processed image URL or empty string if falsy
 */
export const getImageUrl = (img: unknown): string | unknown => {
  return img || '';
};

/**
 * Helper function to get service description with fallback
 * @param service - The service object
 * @returns A formatted description string
 */
interface ServiceWithDescription {
  name?: string;
  description?: string;
}

export const getServiceDescription = (service: unknown): string => {
  const typedService = service as ServiceWithDescription;
  // Default description for null/undefined service
  if (!service) {
    return 'Contrate serviços com profissionais qualificados e garantia de serviço.';
  }

  // Get service name with fallback
  const serviceName = typedService.name || 'Serviço';

  // Default description using service name
  const defaultDesc = `${serviceName} com profissionais qualificados e garantia de serviço. Agende agora mesmo.`;

  // Check if description exists and is a valid string
  const hasValidDescription =
    typedService.description &&
    typeof typedService.description === 'string' &&
    typedService.description.trim() !== '';

  // Use default if no valid description
  if (!hasValidDescription) {
    return defaultDesc;
  }

  // Truncate long descriptions
  // We can safely use non-null assertion (!) here because we've already checked
  // in hasValidDescription that description exists and is a valid string
  const desc = typedService.description!.trim();
  return desc.length > 157 ? desc.substring(0, 157) + '...' : desc;
};
