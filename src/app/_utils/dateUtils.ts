import { addDays, setHours, setMilliseconds, setMinutes, setSeconds } from 'date-fns';

/**
 * Time periods configuration
 */
export const TIME_PERIODS = {
  MORNING: {
    id: 'morning',
    label: 'Manhã',
    startHour: 8,
    endHour: 12,
    displayTime: '8h - 12h',
  },
  AFTERNOON: {
    id: 'afternoon',
    label: 'Tarde',
    startHour: 13,
    endHour: 18,
    displayTime: '13h - 18h',
  },
};

/**
 * Creates a date with specific time (hours, minutes, seconds, milliseconds)
 */
export function createDateWithTime(
  date: Date,
  hours: number,
  minutes = 0,
  seconds = 0,
  milliseconds = 0
): Date {
  return setMilliseconds(
    setSeconds(setMinutes(setHours(date, hours), minutes), seconds),
    milliseconds
  );
}

/**
 * Calculates a timestamp that is 24 hours from now
 */
export function getTwentyFourHoursLater(): number {
  return new Date().getTime() + 24 * 60 * 60 * 1000;
}

/**
 * Checks if a given date is at least 24 hours in the future from now
 */
export function isDateAtLeast24HoursAhead(date: Date): boolean {
  const now = new Date();
  const twentyFourHoursFromNow = now.getTime() + 24 * 60 * 60 * 1000;
  return date.getTime() >= twentyFourHoursFromNow;
}

/**
 * Determines the minimum allowed date for scheduling based on local time
 */
export function getMinSchedulingDate(): Date {
  const now = new Date();
  return addDays(now, 1);
}

/**
 * Checks if morning period is available based on the current local time and selected date
 */
export function isMorningPeriodAvailable(selectedDate: Date): boolean {
  const now = new Date();
  const selectedDateMidnight = createDateWithTime(selectedDate, 0, 0, 0, 0);
  const tomorrowMidnight = createDateWithTime(addDays(now, 1), 0, 0, 0, 0);

  // If selected date is more than 1 day ahead, morning is available
  if (selectedDateMidnight.getTime() > tomorrowMidnight.getTime()) {
    return true;
  }

  // For tomorrow, calculate if there's at least 24 hours between now and the END of the morning period
  const morningEndTime = createDateWithTime(selectedDate, TIME_PERIODS.MORNING.endHour, 0, 0, 0);
  const twentyFourHoursFromNow = getTwentyFourHoursLater();

  // We need to check if NOW + 24h is still BEFORE the end of the morning period tomorrow
  return twentyFourHoursFromNow <= morningEndTime.getTime();
}

/**
 * Checks if afternoon period is available based on the current local time and selected date
 */
export function isAfternoonPeriodAvailable(selectedDate: Date): boolean {
  const now = new Date();
  const selectedDateMidnight = createDateWithTime(selectedDate, 0, 0, 0, 0);
  const tomorrowMidnight = createDateWithTime(addDays(now, 1), 0, 0, 0, 0);

  // If selected date is more than 1 day ahead, afternoon is available
  if (selectedDateMidnight.getTime() > tomorrowMidnight.getTime()) {
    return true;
  }

  // For tomorrow, calculate if there's at least 24 hours between now and the END of the afternoon period
  const afternoonEndTime = createDateWithTime(
    selectedDate,
    TIME_PERIODS.AFTERNOON.endHour,
    0,
    0,
    0
  );
  const twentyFourHoursFromNow = getTwentyFourHoursLater();

  // We need to check if NOW + 24h is still BEFORE the end of the afternoon period tomorrow
  return twentyFourHoursFromNow <= afternoonEndTime.getTime();
}

/**
 * Gets disabled dates for the calendar based on the 24-hour scheduling rule
 */
export function getDisabledDates(
  date: Date,
  isMorningPeriodAvailableFn: (date: Date) => boolean = isMorningPeriodAvailable,
  isAfternoonPeriodAvailableFn: (date: Date) => boolean = isAfternoonPeriodAvailable
): boolean {
  const now = new Date();
  const tomorrow = addDays(now, 1);
  tomorrow.setHours(0, 0, 0, 0);

  // Reset input date to midnight for proper comparison
  const dateToCheck = new Date(date);
  dateToCheck.setHours(0, 0, 0, 0);

  // Disable dates in the past and today
  if (dateToCheck.getTime() < tomorrow.getTime()) {
    return true;
  }

  // If the date is tomorrow, check if both periods are unavailable
  if (dateToCheck.getTime() === tomorrow.getTime()) {
    const isMorningAvailable = isMorningPeriodAvailableFn(date);
    const isAfternoonAvailable = isAfternoonPeriodAvailableFn(date);

    // If both periods are unavailable, disable the date
    if (!isMorningAvailable && !isAfternoonAvailable) {
      return true;
    }
  }

  // Date is available
  return false;
}
