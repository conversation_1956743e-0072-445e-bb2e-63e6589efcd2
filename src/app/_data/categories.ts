import { IconName } from '../_components/Ui/icon';

export interface Category {
  id: string;
  name: string;
  icon: IconName;
  subcategories: string[];
}

export const categories: Category[] = [
  {
    id: 'assistencia-tecnica',
    name: 'Assistência técnica',
    icon: 'Wrench',
    subcategories: ['Eletrodomésticos'],
  },
  {
    id: 'chaveiro',
    name: '<PERSON><PERSON><PERSON>',
    icon: 'KeyRound',
    subcategories: ['Aberturas, instalações e trocas'],
  },
  {
    id: 'eletricista',
    name: 'Eletric<PERSON>',
    icon: 'Zap',
    subcategories: ['Chuve<PERSON><PERSON>', 'Iluminação'],
  },
  {
    id: 'encanador',
    name: '<PERSON><PERSON><PERSON>',
    icon: 'Droplet',
    subcategories: ['Desentupimento', 'Instalações', 'Vazamentos'],
  },
  {
    id: 'instalacao',
    name: 'Instala<PERSON>',
    icon: 'Drill',
    subcategories: ['<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', 'Eletrodoméstic<PERSON>'],
  },
  {
    id: 'limpeza',
    name: '<PERSON><PERSON><PERSON>',
    icon: '<PERSON>rk<PERSON>',
    subcategories: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON> d´á<PERSON>', '<PERSON>as', 'Placas solares', 'Sofás e estofados'],
  },
  {
    id: 'manutencao',
    name: 'Manutenção',
    icon: 'Settings',
    subcategories: ['Aquecedores', 'Vidraçaria'],
  },
];
