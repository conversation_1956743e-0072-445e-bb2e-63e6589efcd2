export interface ServiceType {
  id: number;
  slug: string;
  name: string;
  description: string;
  imageUrl: string;
  status: string;
  provider: {
    id: number;
    name: string;
    imageUrl: string;
    providerUrl: string;
    description: string;
  };
  price: {
    priceId: number;
    originalPrice: number;
    discountPrice: number;
    finalPrice: number;
  };
  availableIn: string[];
  details: string[] | string;
  serviceLimits: string;
  keywords: string[];
  termsConditionsUrl: string;
  preparations: string;
  categoryName?: string;
  categorySlug?: string;
  subcategoryName?: string;
  subcategorySlug?: string;
}
