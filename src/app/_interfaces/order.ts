export interface ApiOrderResponse {
  service: {
    id: number;
    slug: string;
    name: string;
    description: string;
    imageUrl: string;
    status: string;
    termsConditionsUrl: string;
    preparations: string;
    provider: {
      id: number;
      name: string;
      imageUrl: string;
      providerUrl: string;
      description: string;
    };
    price: {
      priceId: number;
      originalPrice: number;
      discountPrice: number;
      finalPrice: number;
    };
    availableIn: string[];
    details: string[];
    serviceLimits: string;
    keywords: string[];
  };
  appointment: {
    date: string;
    period: string;
  };
  payment: {
    method: string;
    totalPaid: number;
  };
  customer: {
    customerId: number;
    fullName: string;
    phone: string;
    email: string;
    document: string;
  };
  address: {
    numberAd: number;
    street: string;
    neighborhood: string;
    cityName: string;
    uf: string;
    zipCode: string;
    complement: string;
  };
}

export function validateOrderService(service: unknown): boolean {
  const typedService = service as Partial<ApiOrderResponse['service']>;
  return (
    typeof typedService?.id === 'number' &&
    typeof typedService?.slug === 'string' &&
    typeof typedService?.name === 'string' &&
    typeof typedService?.description === 'string' &&
    typeof typedService?.imageUrl === 'string' &&
    typeof typedService?.status === 'string' &&
    typeof typedService?.termsConditionsUrl === 'string' &&
    typeof typedService?.preparations === 'string' &&
    Array.isArray(typedService?.availableIn) &&
    Array.isArray(typedService?.details) &&
    typeof typedService?.serviceLimits === 'string' &&
    Array.isArray(typedService?.keywords) &&
    validateOrderServiceProvider(typedService?.provider) &&
    validateOrderServicePrice(typedService?.price)
  );
}

export function validateOrderServiceProvider(provider: unknown): boolean {
  const typedProvider = provider as Partial<ApiOrderResponse['service']['provider']>;
  return (
    typeof typedProvider?.id === 'number' &&
    typeof typedProvider?.name === 'string' &&
    typeof typedProvider?.imageUrl === 'string' &&
    typeof typedProvider?.providerUrl === 'string' &&
    typeof typedProvider?.description === 'string'
  );
}

export function validateOrderServicePrice(price: unknown): boolean {
  const typedPrice = price as Partial<ApiOrderResponse['service']['price']>;
  return (
    typeof typedPrice?.priceId === 'number' &&
    typeof typedPrice?.originalPrice === 'number' &&
    typeof typedPrice?.discountPrice === 'number' &&
    typeof typedPrice?.finalPrice === 'number'
  );
}

export function validateOrderAppointment(appointment: unknown): boolean {
  const typedAppointment = appointment as Partial<ApiOrderResponse['appointment']>;
  return typeof typedAppointment?.date === 'string' && typeof typedAppointment?.period === 'string';
}

export function validateOrderPayment(payment: unknown): boolean {
  const typedPayment = payment as Partial<ApiOrderResponse['payment']>;
  return typeof typedPayment?.method === 'string' && typeof typedPayment?.totalPaid === 'number';
}

export function validateOrderCustomer(customer: unknown): boolean {
  const typedCustomer = customer as Partial<ApiOrderResponse['customer']>;
  return (
    typeof typedCustomer?.customerId === 'number' &&
    typeof typedCustomer?.fullName === 'string' &&
    typeof typedCustomer?.phone === 'string' &&
    typeof typedCustomer?.email === 'string' &&
    typeof typedCustomer?.document === 'string'
  );
}

export function validateOrderAddress(address: unknown): boolean {
  const typedAddress = address as Partial<ApiOrderResponse['address']>;
  return (
    typeof typedAddress?.numberAd === 'number' &&
    typeof typedAddress?.street === 'string' &&
    typeof typedAddress?.neighborhood === 'string' &&
    typeof typedAddress?.cityName === 'string' &&
    typeof typedAddress?.uf === 'string' &&
    typeof typedAddress?.zipCode === 'string' &&
    typeof typedAddress?.complement === 'string'
  );
}

export function validateApiOrderResponse(order: unknown): order is ApiOrderResponse {
  if (!order || typeof order !== 'object') return false;

  const typedOrder = order as Partial<ApiOrderResponse>;
  return (
    validateOrderService(typedOrder.service) &&
    validateOrderAppointment(typedOrder.appointment) &&
    validateOrderPayment(typedOrder.payment) &&
    validateOrderCustomer(typedOrder.customer) &&
    validateOrderAddress(typedOrder.address)
  );
}
