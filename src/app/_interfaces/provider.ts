export interface Testimonial {
  id: string;
  author: string;
  location: string;
  content: string;
  rating: number;
  order: number;
}

export interface Provider {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  testimonials: Testimonial[];
  providerUrl: string;
}

export function validateTestimonial(testimonial: unknown): testimonial is Testimonial {
  const typedTestimonial = testimonial as Partial<Testimonial>;
  return (
    typeof typedTestimonial?.id === 'string' &&
    typeof typedTestimonial?.author === 'string' &&
    typeof typedTestimonial?.location === 'string' &&
    typeof typedTestimonial?.content === 'string' &&
    typeof typedTestimonial?.rating === 'number' &&
    typeof typedTestimonial?.order === 'number'
  );
}

export function validateProvider(provider: unknown): provider is Provider {
  const typedProvider = provider as Partial<Provider>;
  return (
    typeof typedProvider?.id === 'string' &&
    typeof typedProvider?.name === 'string' &&
    typeof typedProvider?.description === 'string' &&
    typeof typedProvider?.imageUrl === 'string' &&
    typeof typedProvider?.providerUrl === 'string' &&
    Array.isArray(typedProvider?.testimonials) &&
    typedProvider.testimonials?.every((testimonial: unknown) => validateTestimonial(testimonial))
  );
}
