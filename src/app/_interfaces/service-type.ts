// ServiceInterface.ts
export interface ServiceProvider {
  id: number;
  name: string;
  imageUrl: string;
  providerUrl: string;
  description: string;
}

export interface ServicePrice {
  priceId: number;
  originalPrice: number;
  discountPrice: number;
  finalPrice: number;
}

export interface Service {
  id: number;
  slug: string;
  name: string;
  description: string;
  imageUrl: string;
  status: string;
  provider: ServiceProvider;
  price: ServicePrice;
  availableIn: string[];
  details: string[];
  serviceLimits: string;
  keywords: string[];
  termsConditionsUrl: string;
  preparations: string;
}

export interface ServiceCategory {
  id: number;
  name: string;
  slug: string;
  subcategories: {
    id: number;
    name: string;
    slug: string;
    services: Service[];
  }[];
}

export interface ServiceMockData {
  categories: ServiceCategory[];
}

export function validateService(service: unknown): service is Service {
  // Check if service is null or undefined
  if (!service) return false;

  // Check if service is an object
  if (typeof service !== 'object') return false;

  // Cast to Record for property access
  const s = service as Record<string, unknown>;

  // Check each required property individually for better branch coverage
  if (typeof s.id !== 'number') return false;
  if (typeof s.slug !== 'string') return false;
  if (typeof s.name !== 'string') return false;
  if (typeof s.description !== 'string') return false;
  if (typeof s.imageUrl !== 'string') return false;
  if (typeof s.status !== 'string') return false;
  if (!Array.isArray(s.availableIn)) return false;
  if (!Array.isArray(s.details)) return false;
  if (typeof s.serviceLimits !== 'string') return false;
  if (!Array.isArray(s.keywords)) return false;
  if (typeof s.termsConditionsUrl !== 'string') return false;
  if (typeof s.preparations !== 'string') return false;

  // Check provider and price objects
  if (!s.provider || typeof s.provider !== 'object') return false;
  if (!s.price || typeof s.price !== 'object') return false;

  // Check optional properties if they exist
  if (s.categoryName !== undefined && typeof s.categoryName !== 'string') return false;
  if (s.subcategoryName !== undefined && typeof s.subcategoryName !== 'string') return false;
  if (s.categorySlug !== undefined && typeof s.categorySlug !== 'string') return false;
  if (s.subcategorySlug !== undefined && typeof s.subcategorySlug !== 'string') return false;

  // Validate nested objects
  if (!validateServiceProvider(s.provider)) return false;
  if (!validateServicePrice(s.price)) return false;

  // If all checks pass, return true
  return true;
}

export function validateServiceProvider(provider: unknown): provider is ServiceProvider {
  // Check if provider is null or undefined
  if (!provider) return false;

  // Check if provider is an object
  if (typeof provider !== 'object') return false;

  // Cast to Record for property access
  const p = provider as Record<string, unknown>;

  // Check each required property individually for better branch coverage
  if (typeof p.id !== 'number') return false;
  if (typeof p.name !== 'string') return false;
  if (typeof p.imageUrl !== 'string') return false;
  if (typeof p.providerUrl !== 'string') return false;
  if (typeof p.description !== 'string') return false;

  // If all checks pass, return true
  return true;
}

export function validateServicePrice(price: unknown): price is ServicePrice {
  // Check if price is null or undefined
  if (!price) return false;

  // Check if price is an object
  if (typeof price !== 'object') return false;

  // Cast to Record for property access
  const p = price as Record<string, unknown>;

  // Check each required property individually for better branch coverage
  if (typeof p.priceId !== 'number') return false;
  if (typeof p.originalPrice !== 'number') return false;
  if (typeof p.discountPrice !== 'number') return false;
  if (typeof p.finalPrice !== 'number') return false;

  // If all checks pass, return true
  return true;
}
