import { Provider, ServiceType } from '@/src/app/_interfaces';
import { CheckoutFormSchema } from '@/src/app/_utils/formValidation';

export interface PaymentResult {
  success: boolean;
  transactionId: string;
}

export interface CheckoutFormProps {
  service: ServiceType;
  provider: Provider | null;
  onSubmit: (values: CheckoutFormSchema) => Promise<void>;
}

export interface AddressFormProps {
  service: ServiceType;
  isStateSelectVisible: boolean;
  setIsStateSelectOpen: (isOpen: boolean) => void;
  focusedBlock: string | null;
  setFocusedBlock: (block: string | null) => void;
}

export interface PersonalInfoFormProps {
  focusedBlock: string | null;
  setFocusedBlock: (block: string | null) => void;
}

export interface ScheduleFormProps {
  isDatePickerVisible: boolean;
  setIsDatePickerVisible: (isVisible: boolean) => void;
  focusedBlock: string | null;
  setFocusedBlock: (block: string | null) => void;
}

export interface ServiceSummaryProps {
  service: ServiceType;
  provider: Provider | null;
  className?: string;
}

export interface CheckoutPageProps {
  params: {
    slug: string;
  };
}

export interface CheckoutPageClientProps {
  slug: string;
  initialServiceData: ServiceType;
}

export function validatePaymentResult(result: unknown): result is PaymentResult {
  return (
    typeof (result as PaymentResult)?.success === 'boolean' &&
    typeof (result as PaymentResult)?.transactionId === 'string'
  );
}

export function validateCheckoutFormProps(props: unknown): props is CheckoutFormProps {
  const typedProps = props as Partial<CheckoutFormProps>;
  return (
    typeof typedProps?.service === 'object' &&
    typedProps.service !== null &&
    (typedProps?.provider === null ||
      (typeof typedProps?.provider === 'object' && typedProps.provider !== null)) &&
    typeof typedProps?.onSubmit === 'function'
  );
}

export function validateAddressFormProps(props: unknown): props is AddressFormProps {
  const typedProps = props as Partial<AddressFormProps>;
  return (
    typeof typedProps?.service === 'object' &&
    typedProps.service !== null &&
    typeof typedProps?.isStateSelectVisible === 'boolean' &&
    typeof typedProps?.setIsStateSelectOpen === 'function' &&
    (typedProps?.focusedBlock === null || typeof typedProps?.focusedBlock === 'string') &&
    typeof typedProps?.setFocusedBlock === 'function'
  );
}

export function validatePersonalInfoFormProps(props: unknown): props is PersonalInfoFormProps {
  const typedProps = props as Partial<PersonalInfoFormProps>;
  return (
    (typedProps?.focusedBlock === null || typeof typedProps?.focusedBlock === 'string') &&
    typeof typedProps?.setFocusedBlock === 'function'
  );
}

export function validateScheduleFormProps(props: unknown): props is ScheduleFormProps {
  const typedProps = props as Partial<ScheduleFormProps>;
  return (
    typeof typedProps?.isDatePickerVisible === 'boolean' &&
    typeof typedProps?.setIsDatePickerVisible === 'function' &&
    (typedProps?.focusedBlock === null || typeof typedProps?.focusedBlock === 'string') &&
    typeof typedProps?.setFocusedBlock === 'function'
  );
}

export function validateServiceSummaryProps(props: unknown): props is ServiceSummaryProps {
  const typedProps = props as Partial<ServiceSummaryProps>;
  return (
    typeof typedProps?.service === 'object' &&
    typedProps.service !== null &&
    (typedProps?.provider === null ||
      (typeof typedProps?.provider === 'object' && typedProps.provider !== null)) &&
    (typedProps?.className === undefined || typeof typedProps?.className === 'string')
  );
}

export function validateCheckoutPageProps(props: unknown): props is CheckoutPageProps {
  const typedProps = props as Partial<CheckoutPageProps>;
  return (
    typeof typedProps?.params === 'object' &&
    typedProps.params !== null &&
    typeof typedProps?.params?.slug === 'string'
  );
}

export function validateCheckoutPageClientProps(props: unknown): props is CheckoutPageClientProps {
  const typedProps = props as Partial<CheckoutPageClientProps>;
  return (
    typeof typedProps?.slug === 'string' &&
    typeof typedProps?.initialServiceData === 'object' &&
    typedProps.initialServiceData !== null
  );
}
