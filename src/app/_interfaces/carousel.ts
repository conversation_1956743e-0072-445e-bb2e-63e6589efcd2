import { ServicePrice, ServiceProvider } from '@/src/app/_interfaces/service-type';

// Define interfaces for the carousel components
export interface CarouselService {
  id: number;
  name: string;
  slug: string;
  description?: string;
  imageUrl?: string;
  status?: string;
  price?: ServicePrice;
  provider?: ServiceProvider;
}

export interface CarouselSubcategory {
  id: number;
  name: string;
  slug: string;
  services: CarouselService[];
}

export interface CarouselCategory {
  id: number;
  name: string;
  slug: string;
  subcategories: CarouselSubcategory[];
}

export interface ServiceCarouselProps {
  services: CarouselCategory[];
  showSubcategories?: boolean;
}
