export interface PaymentResponse {
  orderId: string;
  secureUrl: string;
  errors: string[] | null;
  secureId: string;
}

export interface OrderPayload {
  priceId: number;
  phoneCode: string;
  phoneNumber: string;
  email: string;
  complement: string;
  modality: string;
  orderDate: string;
  scheduledDate: string;
  scheduledPeriod: string;
  personTypeId: number;
  firstName: string;
  lastName: string;
  socialName: string;
  identificationNumber: string;
  numberAd: string;
  successUrl: string;
  zipCode: string;
  serviceId?: string | number;
}

export function validatePaymentResponse(payment: unknown): payment is PaymentResponse {
  const typedPayment = payment as Partial<PaymentResponse>;
  return (
    typeof typedPayment?.orderId === 'string' &&
    typeof typedPayment?.secureUrl === 'string' &&
    typeof typedPayment?.secureId === 'string' &&
    (typedPayment?.errors === null ||
      (Array.isArray(typedPayment?.errors) &&
        typedPayment.errors.every((error: unknown) => typeof error === 'string')))
  );
}

export function validateOrderPayload(payload: unknown): payload is OrderPayload {
  const typedPayload = payload as Partial<OrderPayload>;
  return (
    typeof typedPayload?.priceId === 'number' &&
    typeof typedPayload?.phoneCode === 'string' &&
    typeof typedPayload?.phoneNumber === 'string' &&
    typeof typedPayload?.email === 'string' &&
    typeof typedPayload?.complement === 'string' &&
    typeof typedPayload?.modality === 'string' &&
    typeof typedPayload?.orderDate === 'string' &&
    typeof typedPayload?.scheduledDate === 'string' &&
    typeof typedPayload?.scheduledPeriod === 'string' &&
    typeof typedPayload?.personTypeId === 'number' &&
    typeof typedPayload?.firstName === 'string' &&
    typeof typedPayload?.lastName === 'string' &&
    typeof typedPayload?.socialName === 'string' &&
    typeof typedPayload?.identificationNumber === 'string' &&
    typeof typedPayload?.numberAd === 'string' &&
    typeof typedPayload?.successUrl === 'string' &&
    typeof typedPayload?.zipCode === 'string' &&
    (typedPayload?.serviceId === undefined ||
      typeof typedPayload?.serviceId === 'string' ||
      typeof typedPayload?.serviceId === 'number')
  );
}
