import { Service, ServiceCategory } from '@/src/app/_interfaces/service-type';
import { serviceTypeApi } from '@/src/app/_services/serviceTypeApi';
import { MetadataRoute } from 'next';

/**
 * Dynamically generates a sitemap for all static and dynamic routes
 * This function is automatically called by Next.js when /sitemap.xml is requested
 */
export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Base URL from environment or default
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://europ.getninjas.com.br';

  // Current date for lastModified
  const currentDate = new Date();

  try {
    // Fetch services data using the service layer
    const { categories } = await serviceTypeApi.getServiceTypeList();

    // Static routes
    const staticRoutes: MetadataRoute.Sitemap = [
      {
        url: baseUrl,
        lastModified: currentDate,
        changeFrequency: 'daily',
        priority: 1.0,
      },
      // SEO helper page
      {
        url: `${baseUrl}/avengers-assemble`,
        lastModified: currentDate,
        changeFrequency: 'daily',
        priority: 0.7,
      },
      // Add other static routes as needed
    ];

    // Dynamic routes from services
    const serviceRoutes: MetadataRoute.Sitemap = [];

    // Process categories and services
    categories.forEach((category: ServiceCategory) => {
      if (category.subcategories) {
        category.subcategories.forEach((subcategory) => {
          if (subcategory.services) {
            subcategory.services.forEach((service: Service) => {
              if (service.status === 'ATIVO' || service.status === 'active') {
                serviceRoutes.push({
                  url: `${baseUrl}/servicos/${subcategory.slug}?=${service.slug}`,
                  lastModified: currentDate,
                  changeFrequency: 'weekly',
                  priority: 0.8,
                });
              }
            });
          }
        });
      }
    });

    // Combine all routes
    return [...staticRoutes, ...serviceRoutes];
  } catch (error) {
    console.error('Error generating sitemap:', error);

    // Return only static routes if API fails
    return [
      {
        url: baseUrl,
        lastModified: currentDate,
        changeFrequency: 'daily',
        priority: 1.0,
      },
    ];
  }
}
