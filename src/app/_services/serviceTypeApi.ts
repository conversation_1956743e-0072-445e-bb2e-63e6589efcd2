import { Provider } from '@/src/app/_interfaces/provider';
import { Service, ServiceCategory } from '@/src/app/_interfaces/service-type';
import axios from 'axios';

// Re-define the Service interface with the optional properties to ensure TypeScript recognizes them
interface EnhancedService extends Omit<Service, 'categoryName' | 'subcategoryName'> {
  categoryName?: string;
  subcategoryName?: string;
  categorySlug?: string;
  subcategorySlug?: string;
}

/**
 * Interface for the response from service-type/list endpoint
 */
export interface ServiceTypeListResponse {
  categories: ServiceCategory[];
}

/**
 * Interface for the response from service-type/{slug} endpoint
 */
export interface ServiceTypeBySlugResponse {
  service: Service;
  provider?: Provider;
}

/**
 * Interface for the Service Type API
 * Defines the contract for service-related API operations
 */
export interface ServiceTypeApiInterface {
  getServiceTypeList(): Promise<ServiceTypeListResponse>;
  getServiceTypeBySlug(slug: string): Promise<ServiceTypeBySlugResponse>;
  getSubcategories(): Promise<{ subcategory: string }[]>;
}

/**
 * Service Type API implementation
 * Handles all service-related API operations
 */
export class ServiceTypeApi implements ServiceTypeApiInterface {
  private apiUrl: string;

  constructor(apiUrl?: string) {
    this.apiUrl = apiUrl || process.env.NEXT_PRIVATE_API_BASE_URL || '';
  }

  /**
   * Fetches the list of all service types
   * @returns Promise with the service type response
   */
  async getServiceTypeList(): Promise<ServiceTypeListResponse> {
    try {
      const response = await axios.get(`${this.apiUrl}/service-type/list`, {
        headers: {
          'service-provider': 'EUR',
        },
        timeout: 10000,
      });

      return response.data;
    } catch (_error) {
      // Silent error handling - return empty categories array as fallback
      return { categories: [] };
    }
  }

  /**
   * Fetches a specific service type by slug
   * @param slug - The service slug to fetch
   * @returns Promise with the service data
   */
  /**
   * Fetches a specific service type by slug with retry mechanism
   * @param slug - The service slug to fetch
   * @param retryCount - Number of retries attempted (internal use)
   * @returns Promise with the service data
   */
  async getServiceTypeBySlug(slug: string, retryCount = 0): Promise<ServiceTypeBySlugResponse> {
    try {
      // Fetch service data
      const response = await axios.get(`${this.apiUrl}/service-type/${slug}`, {
        headers: {
          'service-provider': 'EUR',
        },
        timeout: 15000, // Increased timeout for better reliability
      });

      // Enhanced validation with more detailed error messages
      if (!response.data) {
        throw new Error('Invalid response format: empty response data');
      }

      // Check if service property exists directly
      if (!response.data.service) {
        // If the response itself looks like a service object, use it directly
        if (response.data.id && response.data.slug && response.data.name) {
          // Create a new response object with the expected structure
          return { service: response.data };
        } else {
          throw new Error(
            'Invalid response format: missing service data and not a direct service object'
          );
        }
      }

      // Return the response data
      return response.data;
    } catch (_error) {
      // Implement retry logic (max 2 retries)
      if (retryCount < 2) {
        // Exponential backoff: wait longer between each retry
        await new Promise((resolve) => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
        return this.getServiceTypeBySlug(slug, retryCount + 1);
      }

      // Format the slug for a readable name
      // Remove any provider suffix (like -europ-assistance) for cleaner display
      const cleanSlug = slug.replace(/-europ-assistance$/, '');

      const formattedName = cleanSlug
        .split('-')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      // After all retries failed, return a more descriptive fallback
      // Use type assertion to tell TypeScript that we know what we're doing
      const fallbackService: EnhancedService = {
        id: 0,
        slug: slug,
        name: formattedName,
        description: `${formattedName} com profissionais qualificados e garantia de serviço. Agende agora mesmo e tenha a tranquilidade de contar com a Europ Assistance.`,
        imageUrl: '',
        status: 'active',
        provider: {
          id: 0,
          name: 'GetNinjas + Europ Assistance',
          imageUrl: '',
          providerUrl: '/',
          description: 'Serviços de qualidade com garantia.',
        },
        price: {
          priceId: 0,
          originalPrice: 0,
          discountPrice: 0,
          finalPrice: 0,
        },
        availableIn: ['São Paulo', 'Rio de Janeiro', 'Minas Gerais'],
        details: ['Serviço profissional com garantia', 'Atendimento em todo Brasil'],
        serviceLimits: 'Consulte disponibilidade para sua região.',
        keywords: cleanSlug
          .split('-')
          .concat(['europ', 'assistance', 'getninjas', 'serviço', 'profissional']),
        termsConditionsUrl: '',
        preparations: 'Entre em contato para mais informações.',
        // Optional properties that we added to the interface
        categoryName: 'Serviços',
        subcategoryName: 'Assistência',
      };

      return {
        service: fallbackService,
      };
    }
  }

  /**
   * Gets all subcategories for static path generation
   * @returns Promise with array of subcategory slugs
   */
  async getSubcategories(): Promise<{ subcategory: string }[]> {
    try {
      const { categories } = await this.getServiceTypeList();
      const params: { subcategory: string }[] = [];

      categories.forEach((category: ServiceCategory) => {
        if (category.subcategories) {
          category.subcategories.forEach((subcategory) => {
            params.push({
              subcategory: subcategory.slug,
            });
          });
        }
      });

      return params;
    } catch (_error) {
      // Silent error handling - return empty array
      return [];
    }
  }
}

// Create a singleton instance for server components
export const serviceTypeApi = new ServiceTypeApi();
