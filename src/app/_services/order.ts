import { ApiOrderResponse } from '@/src/app/_interfaces';
import { axiosInstance } from '@/src/app/_utils';
import axios from 'axios';
import { formatCPF, formatPhoneNumber } from '../_utils';

export class OrderService {
  private static readonly API_URL = '/api/order';

  static async getOrderByUuid(orderId: string): Promise<ApiOrderResponse> {
    try {
      const { data } = await axiosInstance.get<ApiOrderResponse>(`${this.API_URL}/by-uuid`, {
        params: { uuid: orderId },
      });

      // Transform the API response to match our expected format
      return {
        service: {
          id: data.service.id,
          slug: data.service.slug,
          name: data.service.name,
          description: data.service.description,
          imageUrl: data.service.imageUrl,
          status: data.service.status,
          preparations: data.service.preparations,
          termsConditionsUrl: data.service.termsConditionsUrl,
          availableIn: data.service.availableIn || [],
          // Handle details as either string or array
          details: data.service.details
            ? Array.isArray(data.service.details)
              ? data.service.details
              : typeof data.service.details === 'string'
                ? (data.service.details as string)
                    .split(/\r?\n/)
                    .filter((line: string) => line.trim())
                : []
            : [],
          serviceLimits: data.service.serviceLimits,
          keywords: data.service.keywords || [],
          price: {
            priceId: data.service.price.priceId,
            originalPrice: data.service.price.originalPrice,
            discountPrice: data.service.price.discountPrice,
            finalPrice: data.service.price.finalPrice,
          },
          provider: {
            id: data.service.provider.id,
            name: data.service.provider.name,
            imageUrl: data.service.provider.imageUrl,
            providerUrl: data.service.provider.providerUrl,
            description: data.service.provider.description,
          },
        },
        appointment: {
          date: data.appointment.date,
          // Convert MANHA/TARDE to morning/afternoon
          period: data.appointment.period === 'MANHA' ? 'morning' : 'afternoon',
        },
        customer: {
          customerId: data.customer.customerId,
          fullName: data.customer.fullName,
          // Format phone number for display (add spaces, etc.)
          phone: formatPhoneNumber(data.customer.phone, '+55'),
          email: data.customer.email,
          // Format document (CPF) for display
          document: formatCPF(data.customer.document),
        },
        address: {
          numberAd: data.address.numberAd,
          street: data.address.street,
          neighborhood: data.address.neighborhood,
          cityName: data.address.cityName,
          uf: data.address.uf,
          zipCode: data.address.zipCode,
          complement: data.address.complement,
        },
        payment: {
          method: data.payment.method,
          totalPaid: data.payment.totalPaid,
        },
      };
    } catch (error) {
      console.error('Error fetching order:', error);
      if (axios.isAxiosError(error)) {
        throw new Error(`Failed to fetch order: ${error.response?.statusText || error.message}`);
      }
      throw error;
    }
  }
}
