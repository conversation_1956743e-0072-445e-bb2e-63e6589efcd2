import { NextRequest, NextResponse } from 'next/server';

/**
 * Simple health check endpoint that returns a 200 status
 * Used for monitoring the application health and for load balancer checks
 */
export async function GET(request: NextRequest) {
  const requestUrl = request.nextUrl.toString();
  const hostname = request.headers.get('host') || 'unknown-host';

  return NextResponse.json(
    {
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      url: requestUrl,
      hostname: hostname,
      message: 'Application is healthy',
    },
    {
      status: 200,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    }
  );
}
