import { NextRequest, NextResponse } from 'next/server';

/**
 * Deep health check endpoint that verifies critical dependencies
 * This can be extended to check database connections, external services, etc.
 */
export async function GET(request: NextRequest) {
  // Get request information
  const requestUrl = request.nextUrl.toString();
  const hostname = request.headers.get('host') || 'unknown-host';

  // Initialize health status
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    url: requestUrl,
    hostname: hostname,
    memory: process.memoryUsage(),
    services: {
      // Add checks for external services here as needed
      // Example: database: "connected"
    },
  };

  try {
    // Perform additional health checks here if needed
    // For example, checking database connection, external APIs, etc.

    // For now, we're just returning a basic health check

    return NextResponse.json(health, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (error) {
    // If any checks fail, return an error response
    console.error('Health check failed:', error);

    return NextResponse.json(
      {
        status: 'error',
        timestamp: new Date().toISOString(),
        message: 'Health check failed',
        error: error instanceof Error ? error.message : String(error),
      },
      {
        status: 503, // Service Unavailable
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      }
    );
  }
}
