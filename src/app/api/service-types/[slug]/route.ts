import axios from 'axios';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

const API_URL = process.env.NEXT_PRIVATE_API_BASE_URL;

// Set proper content type for the response
const contentType = 'application/json';

export async function GET(request: NextRequest, props: { params: Promise<{ slug: string }> }) {
  const params = await props.params;
  try {
    const { slug } = params;

    if (!slug) {
      return NextResponse.json(
        { message: 'Slug is required' },
        {
          status: 400,
          headers: {
            'Content-Type': contentType,
          },
        }
      );
    }

    try {
      const { data } = await axios.get(`${API_URL}/service-type/${slug}`, {
        headers: {
          'service-provider': 'EUR',
        },
        timeout: 10000, // 10 second timeout
      });

      // Return with correct content type
      return NextResponse.json(data, {
        headers: {
          'Content-Type': contentType,
        },
      });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorStatus = error.code === 'ECONNABORTED' ? 504 : error.response?.status || 500;
        const errorText = error.response?.data || 'No response body';

        console.error(`API error (${errorStatus}): ${errorText}`);

        return NextResponse.json(
          {
            message: 'Failed to fetch service data',
            status: errorStatus,
            error: errorText,
          },
          {
            status: errorStatus,
            headers: {
              'Content-Type': contentType,
            },
          }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error('Error in service-type API route:', error);

    return NextResponse.json(
      { message: 'Internal server error' },
      {
        status: 500,
        headers: {
          'Content-Type': contentType,
        },
      }
    );
  }
}
