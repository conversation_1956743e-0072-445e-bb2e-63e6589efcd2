import axios from 'axios';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

const API_URL = process.env.NEXT_PRIVATE_API_BASE_URL;
// Set proper content type for the response
// Instead of exporting, use a constant
const CONTENT_TYPE = 'application/json';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const slug = searchParams.get('slug');

    if (!slug) {
      return NextResponse.json(
        { message: 'Slug parameter is required' },
        {
          status: 400,
          headers: {
            'Content-Type': CONTENT_TYPE,
          },
        }
      );
    }

    try {
      const { data } = await axios.get(`${API_URL}/service-type/${slug}`, {
        headers: {
          'service-provider': 'EUR',
        },
        timeout: 10000, // 10 second timeout
      });

      // Return with correct content type
      return NextResponse.json(data, {
        headers: {
          'Content-Type': CONTENT_TYPE,
        },
      });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorStatus = error.code === 'ECONNABORTED' ? 504 : error.response?.status || 500;
        const errorText = error.response?.data || 'No response body';

        console.error(`API error (${errorStatus}): ${errorText}`);

        return NextResponse.json(
          {
            message: 'Failed to fetch service',
            status: errorStatus,
            error: errorText,
          },
          {
            status: errorStatus,
            headers: {
              'Content-Type': CONTENT_TYPE,
            },
          }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error('Error in service-types API route:', error);

    return NextResponse.json(
      { message: 'Internal server error' },
      {
        status: 500,
        headers: {
          'Content-Type': CONTENT_TYPE,
        },
      }
    );
  }
}
