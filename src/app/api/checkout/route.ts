import axios from 'axios';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

const DEFAULT_API_URL = 'https://api.placeholder-for-build.com';
const API_URL = process.env.NEXT_PRIVATE_API_BASE_URL || DEFAULT_API_URL;

// 🔥 CORS headers centralizados
const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // ou use o domínio específico
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function OPTIONS() {
  // Requisições de preflight CORS (antes de POST)
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: corsHeaders,
    }
  );
}

export async function POST(request: NextRequest) {
  try {
    const payload = await request.json();

    const { data } = await axios.post(`${API_URL}/order/save`, payload, {
      headers: {
        'service-provider': 'EUR',
      },
    });

    const responseData = {
      ...data,
      id: data.id || data.orderId || data.uuid || '',
    };

    return NextResponse.json(responseData, {
      status: 200,
      headers: corsHeaders, // ✅ aplica os headers CORS na resposta também
    });
  } catch (error) {
    console.error('Error in checkout API route:', error);

    const status = axios.isAxiosError(error) && error.response ? error.response.status : 500;

    const message =
      axios.isAxiosError(error) && error.response
        ? error.response.data?.message || 'Failed to process payment'
        : 'Internal server error';

    return NextResponse.json(
      { message },
      {
        status,
        headers: corsHeaders, // ✅ também aplica em respostas de erro
      }
    );
  }
}
