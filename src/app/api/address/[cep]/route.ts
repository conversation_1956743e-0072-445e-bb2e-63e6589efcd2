import axios from 'axios';
import { NextResponse } from 'next/server';

export async function GET(request: Request, props: { params: Promise<{ cep: string }> }) {
  const params = await props.params;
  try {
    const { data } = await axios.get(
      `${process.env.NEXT_PRIVATE_API_BASE_URL}/address/find/${params.cep}`
    );

    // Return without cache
    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  } catch (error) {
    console.error('Error fetching CEP:', error);

    if (axios.isAxiosError(error)) {
      let errorMessage = 'CEP inválido. Verifique os números digitados.';

      if (error.response?.data) {
        const errorData = error.response.data;
        if (errorData.message) {
          if (
            error.response.status === 400 &&
            errorData.message.includes('O serviço não está disponível para esta região')
          ) {
            errorMessage = 'Esse serviço não está disponível para este CEP.';
          } else {
            errorMessage = errorData.message;
          }
        }
      }

      return NextResponse.json(
        { error: errorMessage, message: errorMessage },
        {
          status: error.response?.status || 500,
          headers: {
            'Cache-Control': 'no-store',
          },
        }
      );
    }

    return NextResponse.json(
      { error: 'Erro ao buscar CEP' },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-store',
        },
      }
    );
  }
}
