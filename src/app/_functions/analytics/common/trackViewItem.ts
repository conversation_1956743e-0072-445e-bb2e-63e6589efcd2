// src/app/_utils/tracking.ts

// Helper function to check if window is defined
export function isWindowDefined(): boolean {
  return typeof window !== 'undefined';
}

// Helper function to initialize dataLayer and tagManagerDataLayer
export function initializeDataLayer() {
  try {
    if (Array.isArray(window.dataLayer)) {
      // dataLayer already exists as an array, no need to initialize
    } else {
      window.dataLayer = [];
    }
    // Initialize tagManagerDataLayer as well, pointing to dataLayer if not already defined
    window.tagManagerDataLayer = window.tagManagerDataLayer || window.dataLayer;
  } catch (error) {
    console.error('Error initializing dataLayer:', error);
    window.dataLayer = [];
    window.tagManagerDataLayer = window.dataLayer;
  }
}

// Helper function to create item data object
export function createItemData(params: { id: number; name: string; price: number }) {
  return {
    item_id: params.id,
    item_name: params.name || '',
    price: typeof params.price === 'number' ? params.price : 0,
    quantity: 1,
  };
}

// Helper function to check if gtag is available
export function isGtagAvailable(): boolean {
  return typeof window.gtag === 'function';
}

export const trackViewItem = (params: { id: number; name: string; price: number }) => {
  if (!isWindowDefined()) return;
  if (!params) return;

  try {
    // Initialize dataLayer if it doesn't exist
    initializeDataLayer();

    // Create item data
    const itemData = createItemData(params);

    // Send to dataLayer and tagManagerDataLayer (for Google Tag Manager)
    try {
      const eventData = {
        event: 'view_item',
        ecommerce: {
          currency: 'BRL',
          value: params.price,
          items: [itemData],
        },
      };

      // Push to dataLayer
      window.dataLayer.push(eventData);

      // Push to tagManagerDataLayer if it's different from dataLayer
      if (window.tagManagerDataLayer !== window.dataLayer) {
        window.tagManagerDataLayer.push(eventData);
      }
    } catch (error) {
      console.error('Error pushing to dataLayer:', error);
    }

    // Send to GA4 via gtag if available
    if (isGtagAvailable()) {
      try {
        window.gtag('event', 'view_item', {
          currency: 'BRL',
          value: params.price,
          items: [itemData],
        });
      } catch (error) {
        console.error('Error sending event to gtag:', error);
      }
    }
  } catch (error) {
    console.error('Error in trackViewItem:', error);
  }
};
