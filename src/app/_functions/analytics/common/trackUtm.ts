import Cookies from 'js-cookie';

export type UtmParams = {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
};

const UTM_COOKIE_NAME = 'utm_params';
const COOKIE_EXPIRY_DAYS = 30; // Define um período de expiração de 30 dias

// Helper function to get the event name based on environment
function getEventName(): string {
  const analyticsEnvironment = process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT;
  if (analyticsEnvironment === 'homol') {
    return 'utm_captured-homol';
  }
  return 'utm_captured';
}

// Helper function to initialize dataLayer and tagManagerDataLayer
export function initializeDataLayer() {
  try {
    if (!Array.isArray(window.dataLayer)) {
      window.dataLayer = [];
    }
    // Initialize tagManagerDataLayer as well, pointing to dataLayer if not already defined
    window.tagManagerDataLayer = window.tagManagerDataLayer || window.dataLayer;
  } catch (error) {
    window.dataLayer = [];
    window.tagManagerDataLayer = window.dataLayer;
    console.error('Error initializing dataLayer:', error);
  }
}

import { hasAnalyticsConsent } from '@/src/app/_lib/hasAnalyticsConsent';

export const trackUtm = () => {
  if (typeof window === 'undefined') return;

  const eventName = getEventName();
  const utmParams: UtmParams = {};
  let urlParams;

  try {
    // Check if URLSearchParams is defined
    if (typeof URLSearchParams === 'undefined') {
      return;
    }

    // Pega os parâmetros da URL atual
    urlParams = new URLSearchParams(window.location.search);
  } catch (error) {
    console.error('Error creating URLSearchParams:', error);
    return;
  }

  // Lista de parâmetros UTM que queremos rastrear
  const utmKeys = [
    'utm_source',
    'utm_medium',
    'utm_campaign',
    'utm_term',
    'utm_content',
    'utm_banner',
  ];

  // Verifica se existe algum parâmetro UTM
  let hasUtm = false;
  try {
    utmKeys.forEach((key) => {
      try {
        const value = urlParams.get(key);
        if (value) {
          utmParams[key as keyof UtmParams] = value;
          hasUtm = true;
        }
      } catch (error) {
        console.error(`Error getting UTM parameter ${key}:`, error);
      }
    });
  } catch (error) {
    console.error('Error processing UTM parameters:', error);
    return;
  }

  if (hasUtm) {
    try {
      // Salva em um cookie com expiração
      Cookies.set(UTM_COOKIE_NAME, JSON.stringify(utmParams), {
        expires: COOKIE_EXPIRY_DAYS,
        secure: true, // Só envia em conexões HTTPS
        sameSite: 'strict', // Proteção contra CSRF
      });
    } catch (error) {
      // Silently handle cookie errors
      console.error('Error setting UTM cookie:', error);
    }

    // Envia para o dataLayer e tagManagerDataLayer
    try {
      initializeDataLayer();
      window.dataLayer.push({
        event: eventName,
        utm_data: utmParams,
      });

      // Push to tagManagerDataLayer if it's different from dataLayer
      if (window.tagManagerDataLayer !== window.dataLayer) {
        window.tagManagerDataLayer.push({
          event: eventName,
          utm_data: utmParams,
        });
      }
    } catch (error) {
      // Silently handle dataLayer errors
      console.error('Error pushing to dataLayer:', error);
    }

    // Helper function to check if gtag is available
    function isGtagAvailable(): boolean {
      return typeof window.gtag === 'function';
    }

    // Envia para o GA4
    if (isGtagAvailable() && hasAnalyticsConsent()) {
      try {
        window.gtag('event', eventName, utmParams);
      } catch (error) {
        // Silently handle gtag errors
        console.error('Error sending event to gtag:', error);
      }
    }
  }
};

// Helper function to safely parse JSON
function safeJsonParse(jsonString: string | undefined): UtmParams {
  // Return empty object if jsonString is undefined or empty
  if (!jsonString) {
    return {};
  }

  // Check if jsonString is a valid string
  if (typeof jsonString !== 'string') {
    return {};
  }

  try {
    // Parse the JSON string
    const parsedData = JSON.parse(jsonString);

    // Ensure the parsed data is an object and not an array
    if (typeof parsedData !== 'object' || parsedData === null || Array.isArray(parsedData)) {
      return {};
    }

    return parsedData;
  } catch (error) {
    // Log error and return empty object
    console.error('Error parsing UTM cookie:', error);
    return {};
  }
}

export const getUtmParams = (): UtmParams => {
  const utmCookie = Cookies.get(UTM_COOKIE_NAME);
  return safeJsonParse(utmCookie);
};
