import { hasAnalyticsConsent } from '@/src/app/_lib/hasAnalyticsConsent';

export const trackClickEvent = (title: string) => {
  if (typeof window !== 'undefined' && window.dataLayer && hasAnalyticsConsent()) {
    try {
      // Special handling for cookie consent acceptance
      if (title === 'cookie_consent_accepted') {
        // Signal to GTM that cookies are now allowed
        window.dataLayer.push({
          event: 'click',
          title: title,
          ep: {
            link_id: title,
          },
          cookieConsent: 'granted',
        });
      } else {
        // Normal click event
        window.dataLayer.push({
          event: 'click',
          title: title,
          ep: {
            link_id: title,
          },
        });
      }
    } catch (error) {
      // Silently handle any errors to prevent breaking the application
      console.error('Error sending click event:', error);
    }
  }
};
