'use client';

import type { CheckoutFormSchema } from '@/src/app/_utils';
import { usePathname, useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  CheckoutErrorModal,
  CheckoutForm,
  CheckoutPageSkeleton,
  ErrorFallback,
  JsonLd,
  Loader,
} from '@/src/app/_components';

import { useServiceBySlug } from '@/src/app/_hooks';
import { CheckoutPageClientProps, Provider } from '@/src/app/_interfaces';
import { PaymentService } from '@/src/app/_services/payment';
import { Home } from 'lucide-react';

export default function CheckoutPageClient({ slug, initialServiceData }: CheckoutPageClientProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [showLoader, setShowLoader] = useState(false);
  const [priceId, setPriceId] = useState<number | undefined>(undefined);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [formValues, setFormValues] = useState<CheckoutFormSchema | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Use the useServiceBySlug hook with initialData for hydration
  const { service, provider, isLoading, error } = useServiceBySlug(
    slug,
    initialServiceData
      ? { service: initialServiceData, provider: null as unknown as Provider }
      : undefined
  );

  // Extract category and subcategory from the current URL path
  const pathParts = pathname.split('/');
  const _category = pathParts[2] || 'categoria';
  const _subcategory = pathParts[3] || 'subcategoria';

  // Clear any form data from localStorage
  useEffect(() => {
    // Remove any form data that might be stored in localStorage
    const formKeys = Object.keys(localStorage).filter(
      (key) =>
        key.startsWith('checkout-form') || key.includes('form-data') || key.includes('formData')
    );

    formKeys.forEach((key) => {
      localStorage.removeItem(key);
    });
  }, []);

  // Extract priceId from service data when it changes
  useEffect(() => {
    if (service?.price?.priceId) {
      setPriceId(service.price.priceId);
    }
  }, [service]);

  const handleSubmit = useCallback(
    async (values: CheckoutFormSchema) => {
      // If already submitting, prevent multiple submissions
      if (isSubmitting) {
        return;
      }

      // Store form values for retry functionality
      setFormValues(values);

      // Show loader and set submitting state
      setShowLoader(true);
      setIsSubmitting(true);

      try {
        if (!priceId && !service?.id) {
          throw new Error('Missing price information');
        }

        // Adiciona o service ID e priceId diretamente aos valores enviados
        const enhancedValues = {
          ...values,
          serviceId: service?.id,
          priceId,
        };

        const response = await PaymentService.createOrder(enhancedValues);

        if (response.secureUrl) {
          // Keep the loader active during redirection
          // The loader will naturally disappear when the page unloads
          router.push(response.secureUrl);

          // Add a safety timeout in case redirection fails silently
          // This prevents the UI from being stuck in loading state forever
          setTimeout(() => {
            setShowLoader(false);
            setIsSubmitting(false);
          }, 10000); // 10 seconds timeout
        } else {
          setShowLoader(false);
          setIsSubmitting(false);
          setShowErrorModal(true);
          console.error('No secure URL received from payment service');
        }
      } catch (error) {
        setShowLoader(false);
        setIsSubmitting(false);
        setShowErrorModal(true);
        console.error('Error processing payment:', error);
      }
    },
    [service, priceId, router, isSubmitting]
  );

  const handleRetry = useCallback(() => {
    if (formValues && !isSubmitting) {
      setShowErrorModal(false);
      handleSubmit(formValues);
    }
  }, [formValues, handleSubmit, isSubmitting]);

  // Show error state if there's an error
  if (error) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <h1 className="mb-6 text-2xl font-bold text-red-600">Error Loading Service</h1>
        <p className="mb-6">{error}</p>
        <button
          onClick={() => router.push('/')}
          className="hover:bg-primary-dark rounded bg-primary px-4 py-2 font-bold text-white"
        >
          Return to Home
        </button>
      </div>
    );
  }

  // Show loading state
  if (isLoading || !service) {
    return <CheckoutPageSkeleton />;
  }

  return (
    <>
      {showLoader && <Loader />}
      <div className="container mx-auto px-8 pt-8">
        <Breadcrumb className="mb-8 py-2">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/`}>
                <Home className="my-1 ml-2 h-4 w-4" />
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href={`/servicos/${pathParts[2]}?=${slug}`}>
                {service.name}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="font-semibold text-muted-foreground">
                Agendamento
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <CheckoutForm service={service} provider={provider} onSubmit={handleSubmit} />
          <JsonLd
            data={{
              '@context': 'https://schema.org',
              '@type': 'Service',
              name: service.name,
              provider: {
                '@type': 'Organization',
                name: provider?.name,
                image: provider?.imageUrl,
              },
              offers: {
                '@type': 'Offer',
                price: service.price,
                priceCurrency: 'BRL',
              },
            }}
          />
        </ErrorBoundary>
        <div aria-live="polite" className="sr-only">
          {/* Error messages will be handled by the form components */}
        </div>

        <CheckoutErrorModal
          isOpen={showErrorModal}
          onClose={() => setShowErrorModal(false)}
          onRetry={handleRetry}
        />
      </div>
    </>
  );
}
