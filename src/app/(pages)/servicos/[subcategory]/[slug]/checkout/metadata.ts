import { ServiceApiService } from '@/src/app/_services/serviceApi';
import { Metadata } from 'next';

interface Props {
  params: { slug: string };
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = params;
  const { service } = await ServiceApiService.getServiceBySlug(slug);

  if (!service) {
    return {
      title: 'Agendamento - GetNinjas',
      description: 'Complete seu agendamento no GetNinjas',
    };
  }

  return {
    title: `Agendamento - ${service.name} - GetNinjas`,
    description: `Complete seu agendamento para ${service.name} no GetNinjas`,
    openGraph: {
      title: `Agendamento - ${service.name}`,
      description: `Complete seu agendamento para ${service.name} no GetNinjas`,
    },
  };
}
