// This is the server component entry point
import { CheckoutPageSkeleton, ServiceNotFound } from '@/src/app/_components';
import { axiosInstance } from '@/src/app/_utils';
import { Suspense } from 'react';
import CheckoutPageClient from './CheckoutPageClient';

export default async function CheckoutPage(props: { params: Promise<{ slug: string }> }) {
  const params = await props.params;
  const { slug } = params;

  if (!slug) {
    return <ServiceNotFound />;
  }

  try {
    const response = await axiosInstance.get(`/service-type/${slug}`, {
      headers: {
        'service-provider': 'EUR',
      },
    });

    // Pass the pre-fetched service data to the client component
    // This way the API data is not exposed in client-side requests
    return (
      <Suspense fallback={<CheckoutPageSkeleton />}>
        <CheckoutPageClient slug={response.data.slug} initialServiceData={response.data} />
      </Suspense>
    );
  } catch (error) {
    console.error('Error fetching service for checkout:', error);
    return <ServiceNotFound />;
  }
}
