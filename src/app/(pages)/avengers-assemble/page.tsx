import { Service, ServiceCategory } from '@/src/app/_interfaces/service-type';
import { serviceTypeApi } from '@/src/app/_services/serviceTypeApi';
import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Avengers Assemble - Complete Service Directory',
  description:
    'Complete archive of all services provided by GetNinjas and Europ Assistance. Find and explore all available services in one place.',
  robots: {
    index: false,
    follow: true,
    googleBot: {
      index: false,
      follow: true,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: process.env.NEXT_PUBLIC_BASE_URL || 'https://europ.getninjas.com.br',
  },
};

// Enable Incremental Static Regeneration with a revalidation period of 1 hour
export const revalidate = 3600;

export default async function AvengersAssemblePage() {
  // Fetch all services data
  const { categories } = await serviceTypeApi.getServiceTypeList();

  // Base URL from environment or default
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://europ.getninjas.com.br';

  // Current date for display
  const currentDate = new Date();
  const formattedDate = new Intl.DateTimeFormat('pt-BR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(currentDate);

  // Organize services by first letter for alphabetical index
  interface EnhancedService extends Service {
    subcategorySlug: string;
    subcategoryName: string;
    categoryName: string;
  }

  interface AlphabeticalIndex {
    [key: string]: EnhancedService[];
  }

  const alphabeticalIndex: AlphabeticalIndex = {};
  categories.forEach((category: ServiceCategory) => {
    category.subcategories?.forEach((subcategory) => {
      subcategory.services?.forEach((service: Service) => {
        const firstLetter = service.name.charAt(0).toUpperCase();
        if (!alphabeticalIndex[firstLetter]) {
          alphabeticalIndex[firstLetter] = [];
        }
        alphabeticalIndex[firstLetter].push({
          ...service,
          subcategorySlug: subcategory.slug,
          subcategoryName: subcategory.name,
          categoryName: category.name,
        });
      });
    });
  });

  // Sort the keys alphabetically
  const sortedLetters = Object.keys(alphabeticalIndex).sort();

  // Generate JSON-LD structured data
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: 'Stark Industries Service Archive',
    description: 'Complete archive of all services provided by Stark Industries and partners',
    publisher: {
      '@type': 'Organization',
      name: 'GetNinjas + Europ Assistance',
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/images/getninjas_europ_logo.svg`,
      },
    },
    mainEntity: {
      '@type': 'ItemList',
      itemListElement: categories.flatMap(
        (category: ServiceCategory, categoryIndex) =>
          category.subcategories?.flatMap(
            (subcategory, subcategoryIndex) =>
              subcategory.services?.map((service: Service, serviceIndex) => ({
                '@type': 'ListItem',
                position: categoryIndex * 1000 + subcategoryIndex * 100 + serviceIndex + 1,
                item: {
                  '@type': 'Service',
                  name: service.name,
                  url: `${baseUrl}/servicos/${subcategory.slug}?=${service.slug}`,
                  description: service.description,
                  provider: {
                    '@type': 'Organization',
                    name: 'GetNinjas + Europ Assistance',
                  },
                },
              })) || []
          ) || []
      ),
    },
  };

  return (
    <>
      {/* Add JSON-LD structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      <div className="container mx-auto max-w-6xl px-4 py-8">
        <header className="mb-8 text-center">
          <h1 className="mb-2 text-3xl font-bold">Avengers Assemble</h1>
          <p className="text-gray-500">Last updated: {formattedDate}</p>
          <div className="mt-4 inline-block rounded bg-gray-100 p-2">
            <p className="text-sm text-gray-700">
              Project Insight: Comprehensive Service Directory
            </p>
          </div>
        </header>

        <div className="mb-8 rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="mb-2 flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="mr-2 h-5 w-5 text-blue-700"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h2 className="font-semibold text-blue-800">Archive Information</h2>
          </div>
          <p className="text-sm text-blue-700">
            This is a technical archive of all services. For regular browsing, please visit our{' '}
            <Link href="/" className="font-semibold underline">
              homepage
            </Link>
            . This archive is maintained for technical and indexing purposes.
          </p>
        </div>

        {/* Alphabetical Index */}
        <nav className="sticky top-0 z-10 mb-8 border-b bg-white p-4">
          <h2 className="sr-only">Alphabetical Index</h2>
          <div className="flex flex-wrap justify-center gap-2">
            {sortedLetters.map((letter) => (
              <a
                key={letter}
                href={`#letter-${letter}`}
                className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-sm font-medium hover:bg-gray-200"
              >
                {letter}
              </a>
            ))}
          </div>
        </nav>

        {/* Alphabetical Listing */}
        <div className="space-y-8">
          {sortedLetters.map((letter) => (
            <div key={letter} id={`letter-${letter}`} className="scroll-mt-20">
              <h2 className="mb-4 border-b border-gray-300 pb-2 text-2xl font-bold">{letter}</h2>

              <ul className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                {alphabeticalIndex[letter].map((service: EnhancedService) => (
                  <li key={service.id} className="rounded border p-3 hover:bg-gray-50">
                    <Link
                      href={`/servicos/${service.subcategorySlug}?=${service.slug}`}
                      className="block font-medium text-blue-600 hover:text-blue-800 hover:underline"
                    >
                      {service.name}
                    </Link>
                    <div className="mt-1 text-xs text-gray-500">
                      <span className="mr-1 inline-block">{service.categoryName}</span>
                      {service.subcategoryName !== service.categoryName && (
                        <span className="inline-block">&rsaquo; {service.subcategoryName}</span>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Category Listing */}
        <div className="mt-16">
          <h2 className="mb-6 border-b border-gray-300 pb-2 text-2xl font-bold">
            Browse by Category
          </h2>

          <div className="grid gap-8 md:grid-cols-2">
            {categories.map((category: ServiceCategory) => (
              <div key={category.id} className="rounded-lg border p-4 shadow-sm">
                <h3 className="mb-3 text-xl font-semibold">{category.name}</h3>

                <ul className="space-y-4">
                  {category.subcategories?.map((subcategory) => (
                    <li key={subcategory.id}>
                      <h4 className="mb-2 font-medium">{subcategory.name}</h4>
                      <ul className="space-y-1 pl-4">
                        {subcategory.services?.slice(0, 5).map((service: Service) => (
                          <li key={service.id} className="text-sm">
                            <Link
                              href={`/servicos/${subcategory.slug}?=${service.slug}`}
                              className="text-blue-600 hover:text-blue-800 hover:underline"
                            >
                              {service.name}
                            </Link>
                          </li>
                        ))}
                        {(subcategory.services?.length || 0) > 5 && (
                          <li className="text-xs italic text-gray-500">
                            + {(subcategory.services?.length || 0) - 5} more services
                          </li>
                        )}
                      </ul>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-12 rounded-lg bg-gray-100 p-4">
          <h2 className="mb-2 text-lg font-semibold">Technical Resources</h2>
          <ul className="space-y-2">
            <li>
              <Link href="/sitemap.xml" className="text-blue-600 hover:underline">
                Sitemap XML
              </Link>
              <span className="ml-2 text-sm text-gray-500">- Complete sitemap in XML format</span>
            </li>
            <li>
              <Link href="/robots.txt" className="text-blue-600 hover:underline">
                Robots.txt
              </Link>
              <span className="ml-2 text-sm text-gray-500">- Crawler instructions</span>
            </li>
            <li>
              <Link href="/" className="text-blue-600 hover:underline">
                Homepage
              </Link>
              <span className="ml-2 text-sm text-gray-500">- Return to main website</span>
            </li>
          </ul>
        </div>

        <footer className="mt-12 border-t pt-6 text-center text-sm text-gray-500">
          <p>"Sometimes you gotta run before you can walk." - Tony Stark</p>
          <p className="mt-2">
            <Link href="/" className="text-blue-600 hover:underline">
              Return to Homepage
            </Link>
          </p>
          <p className="mt-4 text-xs">
            This is a technical archive page. Not for public distribution.
          </p>
        </footer>
      </div>
    </>
  );
}
