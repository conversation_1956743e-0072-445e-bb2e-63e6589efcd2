import { ErrorDisplay, Loader } from '@/src/app/_components';
import { ClientLayoutWrapper } from '@/src/app/_components/Common/DynamicImports/ClientDynamicImports';
import { AnalyticsProvider, QueryProvider, ServiceProvider } from '@/src/app/_context';
import { axiosInstance } from '@/src/app/_utils';
import { AxiosError } from 'axios';
import { Metadata } from 'next';
import { Inter } from 'next/font/google';
import React, { Suspense } from 'react';
import './globals.css';

// Force dynamic rendering for each request
export const dynamicRendering = 'force-dynamic';

// Fonte Inter com `display: swap` para melhorar FCP
const inter = Inter({ subsets: ['latin'], display: 'swap' });

export const metadata: Metadata = {
  title: {
    default: 'GetNinjas Europ Assistance | Serviços Residenciais com Garantia',
    template: '%s | GetNinjas Europ Assistance',
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_GETNINJAS_URL || 'https://europ.getninjas.com.br'),
  description:
    'Encontre serviços de confiança com segurança e agilidade através da parceria GetNinjas e Europ Assistance. Profissionais qualificados para sua residência.',
  keywords: [
    'getninjas europ',
    'europ assistance',
    'getninjas europa', // Include the autocorrected term for mobile users
    'europa assistance', // Include the autocorrected term for mobile users
    'serviços residenciais',
    'assistência técnica',
    'chaveiro',
    'encanador',
    'eletricista',
    'dedetização',
    'profissionais qualificados',
    'conserto de eletrodomésticos',
    'instalações',
    'vazamentos',
    'iluminação',
    'chuveiro',
  ],
  authors: [{ name: 'GetNinjas' }],
  creator: 'GetNinjas',
  publisher: 'GetNinjas',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: 'GetNinjas Europ | Serviços Residenciais com Garantia',
    description:
      'Encontre serviços de confiança com segurança e agilidade no GetNinjas Europ. Parceria com Europ Assistance para garantia de serviço.',
    siteName: 'GetNinjas Europ',
    locale: 'pt_BR',
    type: 'website',
    images: [
      {
        url: 'https://europ.getninjas.com.br/images/getninjas_europ_logo.svg',
        width: 1200,
        height: 630,
        alt: 'GetNinjas Europ',
      },
    ],
  },
  alternates: {
    canonical: 'https://europ.getninjas.com.br',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-snippet': -1,
      'max-image-preview': 'large',
    },
  },
};

export async function getServices() {
  try {
    const response = await axiosInstance.get('/service-type/list', {
      headers: {
        'service-provider': 'EUR',
      },
    });
    const data = await response.data;
    return data.categories || []; // Retorna a lista de categorias
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`API error: ${error.message}`);
    }
    throw error;
  }
}

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const logoPath = process.env.NEXT_PUBLIC_LOGO_PATH || '/images/getninjas_europ_logo.svg';

  let services;

  try {
    services = await getServices();
  } catch (_error) {
    // Silent error handling - just show error display
    return <ErrorDisplay fullPage message="Erro ao carregar serviços" />;
  }

  return (
    <html lang="pt-BR">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'Organization',
              name: 'GetNinjas Europ',
              alternateName: [
                'GetNinjas Europ Assistance',
                'GetNinjas Europa', // Include autocorrected variation for mobile users
                'GetNinjas + Europ Assistance',
              ],
              url: 'https://europ.getninjas.com.br',
              logo: 'https://europ.getninjas.com.br/images/getninjas_europ_logo.svg',
              description:
                'Serviços residenciais de qualidade através da parceria GetNinjas e Europ Assistance.',
              sameAs: ['https://www.getninjas.com.br', 'https://www.europ-assistance.com.br'],
              keywords: [
                'getninjas europ',
                'europ getninjas',
                'getninjas europa',
                'europ assistance',
                'serviços residenciais',
                'chaveiro',
                'encanador',
                'eletricista',
              ],
              potentialAction: {
                '@type': 'SearchAction',
                target: 'https://europ.getninjas.com.br/search?q={search_term_string}',
                'query-input': 'required name=search_term_string',
              },
            }),
          }}
        />
      </head>
      <body className={inter.className}>
        <Suspense fallback={<Loader />}>
          <QueryProvider>
            <ServiceProvider services={services}>
              <ClientLayoutWrapper logoPath={logoPath}>
                <AnalyticsProvider>{children}</AnalyticsProvider>
              </ClientLayoutWrapper>
            </ServiceProvider>
          </QueryProvider>
        </Suspense>
      </body>
    </html>
  );
}
