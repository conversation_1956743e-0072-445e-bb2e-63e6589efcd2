'use client';
/* eslint-disable */
import { getUtmParams } from '@/src/app/_functions/analytics/common';
import { trackUtm } from '@/src/app/_functions/analytics/common/trackUtm';
import Script from 'next/script';
import { createContext, useCallback, useContext, useEffect } from 'react';
import { initMixpanel } from '../_lib/initMixpanel';

// Global Window interface is defined in global.d.ts

// Define the context type
interface AnalyticsContextType {
  trackEvent: (eventName: string, payload?: Record<string, any>) => void;
}

// Create the context with a default value
const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined);

export const AnalyticsProvider = ({ children }: { children: React.ReactNode }) => {
  const GTM_ID = process.env.NEXT_PUBLIC_GTM_ID;
  const GA4_ID = process.env.NEXT_PUBLIC_GA4_ID;
  const META_PIXEL_ID = process.env.NEXT_PUBLIC_META_PIXEL_ID;

  // Track event function
  const trackEvent = useCallback((eventName: string, payload: Record<string, any> = {}) => {
    if (typeof window === 'undefined') return;

    // Get UTM parameters
    const utmData = getUtmParams();

    try {
      // Initialize dataLayer and tagManagerDataLayer if they don't exist
      // Also handle case where dataLayer is not an array
      if (!window.dataLayer || !Array.isArray(window.dataLayer)) {
        window.dataLayer = [];
      }

      if (!window.tagManagerDataLayer || !Array.isArray(window.tagManagerDataLayer)) {
        window.tagManagerDataLayer = window.dataLayer;
      }

      // Push event to dataLayer
      try {
        window.dataLayer.push({
          event: eventName,
          ...payload,
          utm_data: utmData,
        });
      } catch (error) {
        console.error(`Failed to push to dataLayer:`, error);
      }

      // Push the same event to tagManagerDataLayer if it's different from dataLayer
      if (window.tagManagerDataLayer !== window.dataLayer) {
        try {
          window.tagManagerDataLayer.push({
            event: eventName,
            ...payload,
            utm_data: utmData,
          });
        } catch (error) {
          console.error(`Failed to push to tagManagerDataLayer:`, error);
        }
      }

      // Send to GA4 via gtag if available
      if (typeof window.gtag === 'function') {
        try {
          window.gtag('event', eventName, {
            ...payload,
            ...utmData,
          });
        } catch (error) {
          console.error(`Failed to send event to gtag:`, error);
        }
      }

      // Send to Facebook Pixel if available
      if (typeof window.fbq === 'function') {
        try {
          window.fbq('trackCustom', eventName, {
            ...payload,
            ...utmData,
          });
        } catch (error) {
          console.error(`Failed to send event to Facebook Pixel:`, error);
        }
      }

      // Send to Mixpanel
      if (typeof mixpanel !== 'undefined') {
        try {
          if (typeof mixpanel.track === 'function') {
            mixpanel.track(eventName, {
              ...payload,
              ...utmData,
            });
          }
        } catch (error) {
          console.error(`Failed to track Mixpanel event "${eventName}":`, error);
        }
      }
    } catch (error) {
      console.error(`Unexpected error in trackEvent:`, error);
    }
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Initialize both dataLayer and tagManagerDataLayer
      window.dataLayer = window.dataLayer || [];
      window.tagManagerDataLayer = window.tagManagerDataLayer || window.dataLayer;
      trackUtm();
      initMixpanel();
    }
  }, []);

  return (
    <AnalyticsContext.Provider value={{ trackEvent }}>
      <>
        {/* GTM, GA4, Facebook Pixel scripts */}
        {GTM_ID && (
          <>
            <Script
              id="gtm-init-dataLayer"
              strategy="beforeInteractive"
              dangerouslySetInnerHTML={{
                __html: `
                  window.dataLayer = window.dataLayer || [];
                  // Signal GTM that we're handling cookie consent ourselves
                  window.dataLayer.push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js',
                    // Disable GTM's built-in cookie consent feature
                    'cookieConsent': 'processed'
                  });
                  window.tagManagerDataLayer = window.tagManagerDataLayer || window.dataLayer;
                `,
              }}
            />
            <Script
              id="google-tag-manager-head"
              strategy="beforeInteractive"
              dangerouslySetInnerHTML={{
                __html: `
                  (function(w,d,s,l,i){
                    w[l]=w[l]||[];
                    // Signal GTM that we're handling cookie consent ourselves
                    w[l].push({
                      'gtm.start': new Date().getTime(),
                      event:'gtm.js',
                      // Disable GTM's built-in cookie consent feature
                      'cookieConsent': 'processed'
                    });
                    var f=d.getElementsByTagName(s)[0],
                        j=d.createElement(s),
                        dl=l!='dataLayer'?'&l='+l:'';
                    j.async=true;
                    // Add parameters to disable cookie consent banner
                    j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl+'&gtm_cookies_win=x';
                    f.parentNode.insertBefore(j,f);
                  })(window,document,'script','dataLayer','${GTM_ID}');
                `,
              }}
            />
          </>
        )}

        {GA4_ID && (
          <>
            <Script async src={`https://www.googletagmanager.com/gtag/js?id=${GA4_ID}`} />
            <Script
              id="google-analytics"
              strategy="afterInteractive"
              dangerouslySetInnerHTML={{
                __html: `
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', '${GA4_ID}');
                `,
              }}
            />
          </>
        )}

        {META_PIXEL_ID && (
          <Script
            id="facebook-pixel"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                !function(f,b,e,v,n,t,s)
                {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                n.queue=[];t=b.createElement(e);t.async=!0;
                t.src=v;s=b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t,s)}(window, document,'script',
                'https://connect.facebook.net/en_US/fbevents.js');
                fbq('init', '${META_PIXEL_ID}');
                fbq('track', 'PageView');
              `,
            }}
          />
        )}

        {GTM_ID && (
          <noscript>
            <iframe
              src={`https://www.googletagmanager.com/ns.html?id=${GTM_ID}&gtm_cookies_win=x`}
              height="0"
              width="0"
              style={{ display: 'none', visibility: 'hidden' }}
            ></iframe>
          </noscript>
        )}

        {children}
      </>
    </AnalyticsContext.Provider>
  );
};

export const useAnalytics = () => {
  const context = useContext(AnalyticsContext);

  if (context === undefined) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }

  return context;
};
