import { renderHook, act } from '@testing-library/react';
import { useSubmenu } from '@/src/app/_hooks/useSubmenu';

describe('useSubmenu', () => {
  // Setup DOM elements for testing
  let servicesButtonElement: HTMLDivElement;
  let submenuRefElement: HTMLDivElement;
  let outsideElement: HTMLDivElement;

  beforeEach(() => {
    // Create DOM elements for testing
    servicesButtonElement = document.createElement('div');
    servicesButtonElement.setAttribute('data-trigger', 'Serviços');
    submenuRefElement = document.createElement('div');
    outsideElement = document.createElement('div');

    // Add elements to document
    document.body.appendChild(servicesButtonElement);
    document.body.appendChild(submenuRefElement);
    document.body.appendChild(outsideElement);

    // Mock querySelector to return the services button
    document.querySelector = jest.fn((selector) => {
      if (selector === '[data-trigger="Serviços"]') {
        return servicesButtonElement;
      }
      return null;
    });
  });

  afterEach(() => {
    // Clean up
    document.body.removeChild(servicesButtonElement);
    document.body.removeChild(submenuRefElement);
    document.body.removeChild(outsideElement);
    jest.resetAllMocks();
  });

  it('should initialize with submenu closed', () => {
    // Arrange
    const submenuRef = { current: submenuRefElement };

    // Act
    const { result } = renderHook(() => useSubmenu(submenuRef));

    // Assert
    expect(result.current.isSubmenuOpen).toBe(false);
  });

  it('should toggle submenu state', () => {
    // Arrange
    const submenuRef = { current: submenuRefElement };

    // Act
    const { result } = renderHook(() => useSubmenu(submenuRef));

    act(() => {
      result.current.toggleSubmenu();
    });

    // Assert - first toggle should open it
    expect(result.current.isSubmenuOpen).toBe(true);

    act(() => {
      result.current.toggleSubmenu();
    });

    // Assert - second toggle should close it
    expect(result.current.isSubmenuOpen).toBe(false);
  });

  it('should open submenu on mouse enter', () => {
    // Arrange
    const submenuRef = { current: submenuRefElement };

    // Act
    const { result } = renderHook(() => useSubmenu(submenuRef));

    act(() => {
      result.current.handleMouseEnter();
    });

    // Assert
    expect(result.current.isSubmenuOpen).toBe(true);
  });

  it('should close submenu on mouse leave', () => {
    // Arrange
    const submenuRef = { current: submenuRefElement };

    // Act
    const { result } = renderHook(() => useSubmenu(submenuRef));

    // First open the submenu
    act(() => {
      result.current.handleMouseEnter();
    });

    // Then close it with mouse leave
    act(() => {
      result.current.handleMouseLeave();
    });

    // Assert
    expect(result.current.isSubmenuOpen).toBe(false);
  });

  it('should close submenu when clicking outside', () => {
    // Arrange
    const submenuRef = { current: submenuRefElement };

    // Act
    const { result } = renderHook(() => useSubmenu(submenuRef));

    // First open the submenu
    act(() => {
      result.current.handleMouseEnter();
    });

    // Simulate a click outside both submenu and trigger button
    const clickEvent = new MouseEvent('mousedown', {
      bubbles: true,
      cancelable: true,
    });

    act(() => {
      result.current.handleClickOutside(clickEvent);
    });

    // Assert
    expect(result.current.isSubmenuOpen).toBe(false);
  });

  it('should not close submenu when clicking on submenu', () => {
    // Arrange
    const submenuRef = { current: submenuRefElement };

    // Act
    const { result } = renderHook(() => useSubmenu(submenuRef));

    // First open the submenu
    act(() => {
      result.current.handleMouseEnter();
    });

    // Create a mock event where target is inside the submenu
    Object.defineProperty(submenuRefElement, 'contains', {
      value: () => true,
    });

    const clickEvent = new MouseEvent('mousedown', {
      bubbles: true,
      cancelable: true,
    });

    act(() => {
      result.current.handleClickOutside(clickEvent);
    });

    // Assert - submenu should stay open
    expect(result.current.isSubmenuOpen).toBe(true);
  });

  it('should add and remove event listener for click outside', () => {
    // Arrange
    const submenuRef = { current: submenuRefElement };
    const addEventListenerSpy = jest.spyOn(document, 'addEventListener');
    const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');

    // Act
    const { unmount } = renderHook(() => useSubmenu(submenuRef));

    // Assert - should add event listener on mount
    expect(addEventListenerSpy).toHaveBeenCalledWith('mousedown', expect.any(Function));

    // Act - unmount the hook
    unmount();

    // Assert - should remove event listener on unmount
    expect(removeEventListenerSpy).toHaveBeenCalledWith('mousedown', expect.any(Function));
  });
});
