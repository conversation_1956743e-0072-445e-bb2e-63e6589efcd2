import { useScheduling } from '@/src/app/_hooks/useScheduling';
import { isAfternoonPeriodAvailable, isMorningPeriodAvailable } from '@/src/app/_utils/dateUtils';
import { act, renderHook } from '@testing-library/react';
import { useFormContext } from 'react-hook-form';

// Mock dependencies
jest.mock('react-hook-form', () => ({
  useFormContext: jest.fn(),
}));

jest.mock('@/src/app/_utils/dateUtils', () => ({
  isMorningPeriodAvailable: jest.fn(),
  isAfternoonPeriodAvailable: jest.fn(),
}));

// Mock form context
const mockForm = {
  getValues: jest.fn(),
  setValue: jest.fn(),
  trigger: jest.fn(),
};

describe('useScheduling', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock implementation
    (useFormContext as jest.Mock).mockReturnValue(mockForm);
    mockForm.getValues.mockImplementation((field) => {
      if (field === 'date') return null;
      if (field === 'period') return null;
      return null;
    });
    (isMorningPeriodAvailable as jest.Mock).mockReturnValue(true);
    (isAfternoonPeriodAvailable as jest.Mock).mockReturnValue(true);
  });

  it('should initialize with default values', () => {
    // Arrange
    mockForm.getValues.mockReturnValueOnce(undefined); // For date

    // Act
    const { result } = renderHook(() => useScheduling());

    // Assert
    expect(result.current.selectedDate).toBeUndefined();
    expect(result.current.isMorningAvailable).toBe(false);
    expect(result.current.isAfternoonAvailable).toBe(false);
    expect(result.current.isDatePickerVisible).toBe(false);
    expect(result.current.focusedBlock).toBeNull();
  });

  it('should initialize with provided props', () => {
    // Act
    const { result } = renderHook(() =>
      useScheduling({
        isDatePickerVisible: true,
        focusedBlock: 'schedule',
      })
    );

    // Assert
    expect(result.current.isDatePickerVisible).toBe(true);
    expect(result.current.focusedBlock).toBe('schedule');
  });

  it('should update date picker visibility when props change', () => {
    // Arrange
    const { result, rerender } = renderHook((props) => useScheduling(props), {
      initialProps: { isDatePickerVisible: false },
    });

    // Initial state
    expect(result.current.isDatePickerVisible).toBe(false);

    // Act - update props
    rerender({ isDatePickerVisible: true });

    // Assert
    expect(result.current.isDatePickerVisible).toBe(true);
  });

  it('should update focused block when props change', () => {
    // Arrange
    const { result, rerender } = renderHook((props) => useScheduling(props), {
      initialProps: { focusedBlock: null },
    });

    // Initial state
    expect(result.current.focusedBlock).toBeNull();

    // Act - update props
    rerender({ focusedBlock: 'schedule' });

    // Assert
    expect(result.current.focusedBlock).toBe('schedule');
  });

  it('should update period availability when date changes', () => {
    // Arrange
    const testDate = new Date();
    (isMorningPeriodAvailable as jest.Mock).mockReturnValue(true);
    (isAfternoonPeriodAvailable as jest.Mock).mockReturnValue(false);

    // Act
    const { result } = renderHook(() => useScheduling());
    act(() => {
      result.current.handleDateChange(testDate);
    });

    // Assert
    expect(isMorningPeriodAvailable).toHaveBeenCalledWith(testDate);
    expect(isAfternoonPeriodAvailable).toHaveBeenCalledWith(testDate);
    expect(result.current.isMorningAvailable).toBe(true);
    expect(result.current.isAfternoonAvailable).toBe(false);
    expect(mockForm.setValue).toHaveBeenCalledWith('date', testDate);
    expect(mockForm.setValue).toHaveBeenCalledWith('period', 'morning');
    expect(mockForm.trigger).toHaveBeenCalledWith('date');
  });

  it('should handle when both periods are unavailable', () => {
    // Arrange
    const testDate = new Date();
    (isMorningPeriodAvailable as jest.Mock).mockReturnValue(false);
    (isAfternoonPeriodAvailable as jest.Mock).mockReturnValue(false);

    // Act
    const { result } = renderHook(() => useScheduling());
    act(() => {
      result.current.handleDateChange(testDate);
    });

    // Assert
    expect(result.current.isMorningAvailable).toBe(false);
    expect(result.current.isAfternoonAvailable).toBe(false);
    expect(mockForm.setValue).toHaveBeenCalledWith('period', 'morning');
  });

  it('should change period if current period becomes unavailable', () => {
    // Arrange
    const testDate = new Date();
    mockForm.getValues.mockImplementation((field) => {
      if (field === 'date') return null;
      if (field === 'period') return 'morning';
      return null;
    });
    (isMorningPeriodAvailable as jest.Mock).mockReturnValue(false);
    (isAfternoonPeriodAvailable as jest.Mock).mockReturnValue(true);

    // Act
    const { result } = renderHook(() => useScheduling());
    act(() => {
      result.current.handleDateChange(testDate);
    });

    // Assert
    expect(mockForm.setValue).toHaveBeenCalledWith('period', 'afternoon');
  });

  it('should handle focus events correctly', () => {
    // Arrange
    const mockSetFocusedBlock = jest.fn();

    // Act
    const { result } = renderHook(() => useScheduling({ setFocusedBlock: mockSetFocusedBlock }));

    act(() => {
      result.current.handleFocus();
    });

    // Assert
    expect(result.current.focusedBlock).toBe('schedule');
    expect(mockSetFocusedBlock).toHaveBeenCalledWith('schedule');

    // Act - test blur
    act(() => {
      result.current.handleBlur();
    });

    // Assert
    expect(result.current.focusedBlock).toBeNull();
    expect(mockSetFocusedBlock).toHaveBeenCalledWith(null);
  });

  it('should handle period change correctly', () => {
    // Act
    const { result } = renderHook(() => useScheduling());

    act(() => {
      result.current.handlePeriodChange('afternoon');
    });

    // Assert
    expect(mockForm.setValue).toHaveBeenCalledWith('period', 'afternoon');
    expect(mockForm.trigger).toHaveBeenCalledWith('period');
  });

  it('should sync isDatePickerVisible with props callback', () => {
    // Arrange
    const mockSetIsDatePickerVisible = jest.fn();

    // Act
    const { result } = renderHook(() =>
      useScheduling({
        isDatePickerVisible: false,
        setIsDatePickerVisible: mockSetIsDatePickerVisible,
      })
    );

    act(() => {
      result.current.setIsDatePickerVisible(true);
    });

    // Assert
    expect(result.current.isDatePickerVisible).toBe(true);
    expect(mockSetIsDatePickerVisible).toHaveBeenCalledWith(true);
  });

  it('should handle afternoon period becoming unavailable with morning available', () => {
    // Arrange
    const testDate = new Date();
    mockForm.getValues.mockImplementation((field) => {
      if (field === 'date') return null;
      if (field === 'period') return 'afternoon';
      return null;
    });
    (isMorningPeriodAvailable as jest.Mock).mockReturnValue(true);
    (isAfternoonPeriodAvailable as jest.Mock).mockReturnValue(false);

    // Act
    const { result } = renderHook(() => useScheduling());
    act(() => {
      result.current.handleDateChange(testDate);
    });

    // Assert
    expect(mockForm.setValue).toHaveBeenCalledWith('period', 'morning');
  });

  it('should handle afternoon period becoming unavailable with morning also unavailable', () => {
    // Arrange
    const testDate = new Date();
    mockForm.getValues.mockImplementation((field) => {
      if (field === 'date') return null;
      if (field === 'period') return 'afternoon';
      return null;
    });
    (isMorningPeriodAvailable as jest.Mock).mockReturnValue(false);
    (isAfternoonPeriodAvailable as jest.Mock).mockReturnValue(false);

    // Act
    const { result } = renderHook(() => useScheduling());
    act(() => {
      result.current.handleDateChange(testDate);
    });

    // Assert
    expect(mockForm.setValue).toHaveBeenCalledWith('period', 'afternoon');
  });

  it('should handle undefined date in handleDateChange', () => {
    // Act
    const { result } = renderHook(() => useScheduling());

    // Initial state - selectedDate is null by default because mockForm.getValues('date') returns null
    expect(result.current.selectedDate).toBeNull();

    act(() => {
      result.current.handleDateChange(undefined);
    });

    // Assert - nothing should happen for undefined date
    expect(mockForm.setValue).not.toHaveBeenCalled();
    expect(mockForm.trigger).not.toHaveBeenCalled();
    expect(result.current.selectedDate).toBeNull();
  });

  it('should initialize with date from form', () => {
    // Arrange
    const testDate = new Date();
    mockForm.getValues.mockImplementation((field) => {
      if (field === 'date') return testDate;
      if (field === 'period') return null;
      return null;
    });

    // Act
    const { result } = renderHook(() => useScheduling());

    // Assert
    expect(result.current.selectedDate).toBe(testDate);
  });

  it('should not call setIsDatePickerVisible callback when values are the same', () => {
    // Arrange
    const mockSetIsDatePickerVisible = jest.fn();

    // Act
    const { result } = renderHook(() =>
      useScheduling({
        isDatePickerVisible: true,
        setIsDatePickerVisible: mockSetIsDatePickerVisible,
      })
    );

    // Assert - callback should not be called during initialization
    expect(mockSetIsDatePickerVisible).not.toHaveBeenCalled();

    // Act - set to the same value
    act(() => {
      result.current.setIsDatePickerVisible(true);
    });

    // Assert - callback should not be called when value doesn't change
    expect(mockSetIsDatePickerVisible).not.toHaveBeenCalled();
  });

  it('should auto-select afternoon period when morning is not available', () => {
    // Arrange
    const testDate = new Date();
    mockForm.getValues.mockImplementation((field) => {
      if (field === 'date') return null;
      if (field === 'period') return null; // No period selected yet
      return null;
    });
    (isMorningPeriodAvailable as jest.Mock).mockReturnValue(false);
    (isAfternoonPeriodAvailable as jest.Mock).mockReturnValue(true);

    // Act
    const { result } = renderHook(() => useScheduling());
    act(() => {
      result.current.handleDateChange(testDate);
    });

    // Assert
    expect(mockForm.setValue).toHaveBeenCalledWith('period', 'afternoon');
  });

  it('should handle null props', () => {
    // Act
    const { result } = renderHook(() => useScheduling(null));

    // Assert - should use default values
    expect(result.current.isDatePickerVisible).toBe(false);
    expect(result.current.focusedBlock).toBeNull();
  });

  it('should handle undefined props', () => {
    // Act
    const { result } = renderHook(() => useScheduling(undefined));

    // Assert - should use default values
    expect(result.current.isDatePickerVisible).toBe(false);
    expect(result.current.focusedBlock).toBeNull();
  });
});
