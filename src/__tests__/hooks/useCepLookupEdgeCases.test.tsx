import { useCepLookup } from '@/src/app/_hooks/useCepLookup';
import { axiosInstance } from '@/src/app/_utils';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { act, renderHook } from '@testing-library/react';
import { AxiosError } from 'axios';
import { useFormContext } from 'react-hook-form';

// Mock dependencies
jest.mock('react-hook-form', () => ({
  useFormContext: jest.fn(),
}));

jest.mock('@/src/app/_utils', () => ({
  axiosInstance: {
    get: jest.fn(),
  },
}));

// Create a custom wrapper with a new QueryClient for each test
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

// Create a wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = createTestQueryClient();
  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
};

describe('useCepLookup edge cases', () => {
  // Global mocks
  const mockForm = {
    setValue: jest.fn(),
    getValues: jest.fn(),
  };

  // Mock console.error
  const originalConsoleError = console.error;

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup mocks
    (useFormContext as jest.Mock).mockReturnValue(mockForm);

    // Mock document.querySelector
    document.querySelector = jest.fn().mockReturnValue({
      focus: jest.fn(),
    });

    // Mock console.error
    console.error = jest.fn();

    // Setup fake timers
    jest.useFakeTimers();
  });

  afterEach(() => {
    console.error = originalConsoleError;
    jest.useRealTimers();
  });

  it('should handle formatErrorMessage with null error', () => {
    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    // Assert - error should be null initially
    expect(result.current.error).toBeNull();
  });

  it('should handle formatErrorMessage with AxiosError but no response', async () => {
    // Create an AxiosError without response property
    const axiosError = new AxiosError('Network Error');
    (axiosInstance.get as jest.Mock).mockRejectedValueOnce(axiosError);

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
  });

  it('should handle formatErrorMessage with AxiosError with response but no data', async () => {
    // Create an AxiosError with response but no data property
    const axiosError = new AxiosError('Bad Request');
    axiosError.response = {
      status: 400,
      statusText: 'Bad Request',
      headers: {},
      config: { headers: {} as any },
      data: undefined,
    };
    (axiosInstance.get as jest.Mock).mockRejectedValueOnce(axiosError);

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
  });

  it('should handle formatErrorMessage with AxiosError with response and data but no message', async () => {
    // Create an AxiosError with response and data but no message property
    const axiosError = new AxiosError('Bad Request');
    axiosError.response = {
      status: 400,
      statusText: 'Bad Request',
      headers: {},
      config: { headers: {} as any },
      data: { error: 'Some error' },
    };
    (axiosInstance.get as jest.Mock).mockRejectedValueOnce(axiosError);

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
  });

  it('should handle formatErrorMessage with AxiosError with non-400 status code', async () => {
    // Create an AxiosError with non-400 status code
    const axiosError = new AxiosError('Server Error');
    axiosError.response = {
      status: 500,
      statusText: 'Server Error',
      headers: {},
      config: { headers: {} as any },
      data: { message: 'Internal Server Error' },
    };
    (axiosInstance.get as jest.Mock).mockRejectedValueOnce(axiosError);

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
  });

  it('should handle API response with missing street', async () => {
    // Mock incomplete response (missing street)
    const mockIncompleteAddressData = {
      cep: '12345678',
      // Missing street
      cityName: 'Test City',
      neighborhood: 'Test Neighborhood',
      uf: 'TS',
    };

    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: mockIncompleteAddressData,
    });

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
  });

  it('should handle API response with missing cityName', async () => {
    // Mock incomplete response (missing cityName)
    const mockIncompleteAddressData = {
      cep: '12345678',
      street: 'Test Street',
      // Missing cityName
      neighborhood: 'Test Neighborhood',
      uf: 'TS',
    };

    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: mockIncompleteAddressData,
    });

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
  });

  it('should handle API response with missing uf', async () => {
    // Mock incomplete response (missing uf)
    const mockIncompleteAddressData = {
      cep: '12345678',
      street: 'Test Street',
      cityName: 'Test City',
      neighborhood: 'Test Neighborhood',
      // Missing uf
    };

    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: mockIncompleteAddressData,
    });

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
  });

  it('should handle document.querySelector returning element without focus method', async () => {
    // Mock successful response
    const mockAddressData = {
      cep: '12345678',
      street: 'Test Street',
      cityName: 'Test City',
      neighborhood: 'Test Neighborhood',
      uf: 'TS',
    };

    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: mockAddressData,
    });

    // Mock document.querySelector to return an element without focus method
    document.querySelector = jest.fn().mockReturnValue({
      // Element without focus method
    });

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Run the setTimeout callback
    act(() => {
      jest.runAllTimers();
    });

    // Assert
    expect(document.querySelector).toHaveBeenCalledWith('input[name="streetNumber"]');
    // No error should be thrown
  });

  it('should handle document.querySelector returning null', async () => {
    // Mock successful response
    const mockAddressData = {
      cep: '12345678',
      street: 'Test Street',
      cityName: 'Test City',
      neighborhood: 'Test Neighborhood',
      uf: 'TS',
    };

    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: mockAddressData,
    });

    // Mock document.querySelector to return null
    document.querySelector = jest.fn().mockReturnValue(null);

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Run the setTimeout callback
    act(() => {
      jest.runAllTimers();
    });

    // Assert
    expect(document.querySelector).toHaveBeenCalledWith('input[name="streetNumber"]');
    // No error should be thrown
  });

  it('should handle formatErrorMessage with standard Error object', async () => {
    // Create a standard Error object
    const standardError = new Error('Standard error message');
    (axiosInstance.get as jest.Mock).mockRejectedValueOnce(standardError);

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
    // We don't test the exact error message since it's handled internally
  });

  it('should handle formatErrorMessage with unknown error type', async () => {
    // Create an unknown error type (string)
    const unknownError = 'This is a string error';
    (axiosInstance.get as jest.Mock).mockRejectedValueOnce(unknownError);

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
    // We don't test the exact error message since it's handled internally
  });

  it('should handle formatErrorMessage with AxiosError containing service unavailable message', async () => {
    // Create an AxiosError with a message about service unavailability
    const axiosError = new AxiosError('Bad Request');
    axiosError.response = {
      status: 400,
      statusText: 'Bad Request',
      headers: {},
      config: { headers: {} as any },
      data: { message: 'O serviço não está disponível para esta região' },
    };
    (axiosInstance.get as jest.Mock).mockRejectedValueOnce(axiosError);

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
    // We don't test the exact error message since it's handled internally
  });

  it('should handle API response with null data', async () => {
    // Mock response with null data
    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: null,
    });

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
  });

  it('should handle API response with missing neighborhood', async () => {
    // Mock incomplete response (missing neighborhood)
    const mockIncompleteAddressData = {
      cep: '12345678',
      street: 'Test Street',
      cityName: 'Test City',
      // Missing neighborhood
      uf: 'TS',
    };

    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: mockIncompleteAddressData,
    });

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
  });

  it('should handle API response with missing street', async () => {
    // Mock incomplete response (missing street)
    const mockIncompleteAddressData = {
      cep: '12345678',
      // Missing street
      cityName: 'Test City',
      neighborhood: 'Test Neighborhood',
      uf: 'TS',
    };

    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: mockIncompleteAddressData,
    });

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
  });

  it('should handle API response with missing cityName', async () => {
    // Mock incomplete response (missing cityName)
    const mockIncompleteAddressData = {
      cep: '12345678',
      street: 'Test Street',
      // Missing cityName
      neighborhood: 'Test Neighborhood',
      uf: 'TS',
    };

    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: mockIncompleteAddressData,
    });

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
  });

  it('should handle API response with missing uf', async () => {
    // Mock incomplete response (missing uf)
    const mockIncompleteAddressData = {
      cep: '12345678',
      street: 'Test Street',
      cityName: 'Test City',
      neighborhood: 'Test Neighborhood',
      // Missing uf
    };

    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: mockIncompleteAddressData,
    });

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(console.error).toHaveBeenCalled();
    expect(result.current.fieldsFilledByCep).toBe(false);
  });

  it('should handle invalid CEP format in lookupCep', async () => {
    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('1234'); // Invalid format (too short)
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(result.current.fieldsFilledByCep).toBe(false);
    // The mutation should not be called for invalid CEP
    expect(axiosInstance.get).not.toHaveBeenCalled();
  });

  it('should handle empty CEP in lookupCep', async () => {
    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep(''); // Empty CEP
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(result.current.fieldsFilledByCep).toBe(false);
    // The mutation should not be called for empty CEP
    expect(axiosInstance.get).not.toHaveBeenCalled();
  });

  it('should handle null CEP in lookupCep', async () => {
    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      // @ts-ignore - Testing with invalid input
      result.current.lookupCep(null); // Null CEP
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(result.current.fieldsFilledByCep).toBe(false);
    // The mutation should not be called for null CEP
    expect(axiosInstance.get).not.toHaveBeenCalled();
  });

  it('should handle undefined CEP in lookupCep', async () => {
    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      // @ts-ignore - Testing with invalid input
      result.current.lookupCep(undefined); // Undefined CEP
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(result.current.fieldsFilledByCep).toBe(false);
    // The mutation should not be called for undefined CEP
    expect(axiosInstance.get).not.toHaveBeenCalled();
  });

  it('should handle CEP with non-digit characters in lookupCep', async () => {
    // Mock successful response
    const mockAddressData = {
      cep: '12345678',
      street: 'Test Street',
      cityName: 'Test City',
      neighborhood: 'Test Neighborhood',
      uf: 'TS',
    };

    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: mockAddressData,
    });

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    act(() => {
      result.current.lookupCep('12.345-678'); // CEP with non-digit characters
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(axiosInstance.get).toHaveBeenCalledWith('/api/address/12345678');
    expect(result.current.fieldsFilledByCep).toBe(true);
  });

  it('should handle resetCepFillStatus', async () => {
    // Mock successful response
    const mockAddressData = {
      cep: '12345678',
      street: 'Test Street',
      cityName: 'Test City',
      neighborhood: 'Test Neighborhood',
      uf: 'TS',
    };

    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: mockAddressData,
    });

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    // First, lookup a valid CEP
    act(() => {
      result.current.lookupCep('12345678');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Verify that fieldsFilledByCep is true
    expect(result.current.fieldsFilledByCep).toBe(true);

    // Now reset the CEP fill status
    act(() => {
      result.current.resetCepFillStatus();
    });

    // Verify that fieldsFilledByCep is now false
    expect(result.current.fieldsFilledByCep).toBe(false);
  });

  it('should abort previous request when making a new one', async () => {
    // Create a mock AbortController
    const mockAbort = jest.fn();
    const mockAbortController = {
      abort: mockAbort,
      signal: {} as AbortSignal,
    };

    // Mock AbortController constructor
    global.AbortController = jest.fn().mockImplementation(() => mockAbortController);

    // Mock successful response
    const mockAddressData = {
      cep: '12345678',
      street: 'Test Street',
      cityName: 'Test City',
      neighborhood: 'Test Neighborhood',
      uf: 'TS',
    };

    (axiosInstance.get as jest.Mock).mockResolvedValue({
      data: mockAddressData,
    });

    // Act
    const { result } = renderHook(() => useCepLookup(), {
      wrapper: TestWrapper,
    });

    // First lookup
    act(() => {
      result.current.lookupCep('12345678');
    });

    // Second lookup (should abort the first one)
    act(() => {
      result.current.lookupCep('87654321');
    });

    // Wait for the async operation to complete
    await act(async () => {
      await Promise.resolve();
    });

    // Assert
    expect(mockAbort).toHaveBeenCalled();
    expect(global.AbortController).toHaveBeenCalledTimes(2);
  });
});
