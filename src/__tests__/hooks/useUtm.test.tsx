import { renderHook } from '@testing-library/react';
import { useUtm } from '@/src/app/_hooks/useUtm';
import { getUtmParams } from '@/src/app/_functions/analytics/common/trackUtm';

// Mock dependencies
jest.mock('@/src/app/_functions/analytics/common/trackUtm', () => ({
  getUtmParams: jest.fn(),
}));

describe('useUtm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return empty object when no UTM params are available', () => {
    // Arrange
    (getUtmParams as jest.Mock).mockReturnValue({});

    // Act
    const { result } = renderHook(() => useUtm());

    // Assert
    expect(result.current).toEqual({});
  });

  it('should return UTM params from getUtmParams', () => {
    // Arrange
    const mockUtmParams = {
      utm_source: 'google',
      utm_medium: 'cpc',
      utm_campaign: 'brand',
    };

    (getUtmParams as jest.Mock).mockReturnValue(mockUtmParams);

    // Act
    const { result } = renderHook(() => useUtm());

    // Assert
    expect(result.current).toEqual(mockUtmParams);
    expect(getUtmParams).toHaveBeenCalled();
  });

  it('should call getUtmParams only once on mount', () => {
    // Arrange
    (getUtmParams as jest.Mock).mockReturnValue({});

    // Act
    renderHook(() => useUtm());

    // Assert
    expect(getUtmParams).toHaveBeenCalledTimes(1);
  });
});
