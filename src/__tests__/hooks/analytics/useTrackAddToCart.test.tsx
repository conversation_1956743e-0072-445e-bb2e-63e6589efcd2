import { useHandleAddToCart } from '@/src/app/_hooks/analytics/useTrackAddToCart';
import mixpanel from 'mixpanel-browser';
import { mockService } from '../../__mocks__/mockService';

jest.mock('mixpanel-browser', () => ({
  track: jest.fn(),
}));

jest.mock('@/src/app/_lib/initMixpanel', () => ({
  trackMixpanelEvent: jest.fn((eventName, properties) => {
    // Call the mocked mixpanel.track directly to ensure tests pass
    mixpanel.track(eventName, properties);
  }),
}));

jest.mock('@/src/app/_functions/analytics/common', () => ({
  getUtmParams: jest.fn(() => ({
    utm_source: 'test-source',
    utm_medium: 'test-medium',
  })),
}));

describe('useHandleAddToCart', () => {
  beforeEach(() => {
    window.cookieConsent = 'granted';
    (global as any).window = Object.create(window);
    window.dataLayer = [];
    window.fbq = jest.fn();
    window.gtag = jest.fn();
    (global as any).mixpanel = { track: jest.fn() };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should push add_to_cart event to dataLayer', () => {
    const { handleAddToCart } = useHandleAddToCart();
    handleAddToCart(mockService);

    expect(window.dataLayer).toContainEqual({
      event: 'add_to_cart',
      ecommerce: {
        currency: 'BRL',
        value: 150,
        items: [
          {
            item_id: 1,
            item_name: 'Mock Service',
            price: 150,
            quantity: 1,
          },
        ],
      },
      utm_data: {
        utm_source: 'test-source',
        utm_medium: 'test-medium',
      },
    });
  });

  it('should call gtag with add_to_cart event if available', () => {
    const { handleAddToCart } = useHandleAddToCart();
    handleAddToCart(mockService);

    expect(window.gtag).toHaveBeenCalledWith('event', 'add_to_cart', {
      currency: 'BRL',
      value: 150,
      items: [
        {
          item_id: 1,
          item_name: 'Mock Service',
          price: 150,
          quantity: 1,
        },
      ],
    });
  });

  it('should call fbq with AddToCart event if available', () => {
    const { handleAddToCart } = useHandleAddToCart();
    handleAddToCart(mockService);

    expect(window.fbq).toHaveBeenCalledWith('track', 'AddToCart', {
      content_ids: [1],
      content_name: 'Mock Service',
      content_type: 'product',
      value: 150,
      currency: 'BRL',
      utm_source: 'test-source',
      utm_medium: 'test-medium',
    });
  });

  it('should call mixpanel.track with correct event', () => {
    const { handleAddToCart } = useHandleAddToCart();
    handleAddToCart(mockService);

    expect((mixpanel as any).track).toHaveBeenCalledWith('Add To Cart', {
      item_id: 1,
      item_name: 'Mock Service',
      price: 150,
      quantity: 1,
      utm_source: 'test-source',
      utm_medium: 'test-medium',
    });
  });

  it('should not throw if window is undefined', () => {
    const originalWindow = global.window;
    // @ts-ignore
    delete global.window;

    const { handleAddToCart } = useHandleAddToCart();

    expect(() => handleAddToCart(mockService)).not.toThrow();

    global.window = originalWindow;
  });

  it('should not call gtag if it is not a function', () => {
    // Set gtag to a non-function value
    window.gtag = 'not a function' as any;

    const { handleAddToCart } = useHandleAddToCart();

    // This should not throw
    expect(() => handleAddToCart(mockService)).not.toThrow();
  });

  it('should not call fbq if it is not a function', () => {
    // Set fbq to a non-function value
    window.fbq = 'not a function' as any;

    const { handleAddToCart } = useHandleAddToCart();

    // This should not throw
    expect(() => handleAddToCart(mockService)).not.toThrow();
  });

  it('should handle errors when pushing to dataLayer', () => {
    // Create a dataLayer with a push method that throws an error
    window.dataLayer = [];
    window.dataLayer.push = jest.fn().mockImplementationOnce(() => {
      throw new Error('Test dataLayer error');
    });

    const { handleAddToCart } = useHandleAddToCart();

    // This should not throw
    expect(() => handleAddToCart(mockService)).not.toThrow();
  });

  it('should handle errors when calling mixpanel.track', () => {
    // Make mixpanel.track throw an error
    (mixpanel.track as jest.Mock).mockImplementationOnce(() => {
      throw new Error('Test mixpanel error');
    });

    const { handleAddToCart } = useHandleAddToCart();

    // This should not throw
    expect(() => handleAddToCart(mockService)).not.toThrow();
  });

  it('should handle service with null id and name', () => {
    const serviceWithNullValues = {
      ...mockService,
      id: null,
      name: null,
    } as any;

    const { handleAddToCart } = useHandleAddToCart();

    // This should not throw
    expect(() => handleAddToCart(serviceWithNullValues)).not.toThrow();

    // Check that dataLayer was called with default values
    expect(window.dataLayer).toContainEqual(
      expect.objectContaining({
        event: 'add_to_cart',
        ecommerce: expect.objectContaining({
          items: [
            expect.objectContaining({
              item_id: 0,
              item_name: '',
            }),
          ],
        }),
      })
    );
  });

  it('should handle service with null price', () => {
    const serviceWithNullPrice = {
      ...mockService,
      price: {
        ...mockService.price,
        finalPrice: null,
      },
    } as any;

    const { handleAddToCart } = useHandleAddToCart();

    // This should not throw
    expect(() => handleAddToCart(serviceWithNullPrice)).not.toThrow();

    // Check that dataLayer was called with default price value
    expect(window.dataLayer).toContainEqual(
      expect.objectContaining({
        event: 'add_to_cart',
        ecommerce: expect.objectContaining({
          value: 0,
          items: [
            expect.objectContaining({
              price: 0,
            }),
          ],
        }),
      })
    );
  });

  it('should handle service with completely null price object', () => {
    const serviceWithNullPriceObject = {
      ...mockService,
      price: null,
    } as any;

    const { handleAddToCart } = useHandleAddToCart();

    // This should not throw
    expect(() => handleAddToCart(serviceWithNullPriceObject)).not.toThrow();

    // Check that dataLayer was called with default price value
    expect(window.dataLayer).toContainEqual(
      expect.objectContaining({
        event: 'add_to_cart',
        ecommerce: expect.objectContaining({
          value: 0,
          items: [
            expect.objectContaining({
              price: 0,
            }),
          ],
        }),
      })
    );
  });

  it('should handle errors in createItemData function', () => {
    // Create a service that will cause an error in createItemData
    const problematicService = {} as any; // Empty object will cause errors when accessing properties

    const { handleAddToCart } = useHandleAddToCart();

    // This should not throw
    expect(() => handleAddToCart(problematicService)).not.toThrow();

    // Check that dataLayer was called with default values
    expect(window.dataLayer).toContainEqual(
      expect.objectContaining({
        event: 'add_to_cart',
        ecommerce: expect.objectContaining({
          items: [
            expect.objectContaining({
              item_id: 0,
              item_name: '',
              price: 0,
              quantity: 1,
            }),
          ],
        }),
      })
    );
  });

  it('should handle errors when calling gtag', () => {
    // Set gtag to a function that throws an error
    window.gtag = jest.fn().mockImplementationOnce(() => {
      throw new Error('Test gtag error');
    });

    const { handleAddToCart } = useHandleAddToCart();

    // This should not throw
    expect(() => handleAddToCart(mockService)).not.toThrow();

    // Verify gtag was called
    expect(window.gtag).toHaveBeenCalled();
  });

  it('should handle errors when calling fbq', () => {
    // Set fbq to a function that throws an error
    window.fbq = jest.fn().mockImplementationOnce(() => {
      throw new Error('Test fbq error');
    });

    const { handleAddToCart } = useHandleAddToCart();

    // This should not throw
    expect(() => handleAddToCart(mockService)).not.toThrow();

    // Verify fbq was called
    expect(window.fbq).toHaveBeenCalled();
  });

  it('should handle null service', () => {
    const { handleAddToCart } = useHandleAddToCart();

    // This should not throw and should return early
    expect(() => handleAddToCart(null as any)).not.toThrow();

    // Verify dataLayer was not called with add_to_cart event
    expect(window.dataLayer).not.toContainEqual(
      expect.objectContaining({
        event: 'add_to_cart',
      })
    );
  });

  it('should handle unexpected errors in the main try/catch block', () => {
    // Create a scenario that will cause an error in the main try/catch block
    // by making getUtmParams throw an error
    const { getUtmParams } = require('@/src/app/_functions/analytics/common');
    getUtmParams.mockImplementationOnce(() => {
      throw new Error('Test unexpected error');
    });

    const { handleAddToCart } = useHandleAddToCart();

    // This should not throw
    expect(() => handleAddToCart(mockService)).not.toThrow();
  });

  it('should handle errors in createItemData function directly', () => {
    // Create a service that will cause an error in createItemData
    // by making service.id throw an error when accessed
    const problematicService = {
      get id() {
        throw new Error('Error accessing id');
      },
      name: 'Test Service',
      price: {
        finalPrice: 100,
      },
    } as any;

    const { handleAddToCart } = useHandleAddToCart();

    // This should not throw
    expect(() => handleAddToCart(problematicService)).not.toThrow();

    // Check that dataLayer was called with default values
    expect(window.dataLayer).toContainEqual(
      expect.objectContaining({
        event: 'add_to_cart',
        ecommerce: expect.objectContaining({
          items: [
            expect.objectContaining({
              item_id: 0,
              item_name: '',
              price: 0,
              quantity: 1,
            }),
          ],
        }),
      })
    );
  });
});
