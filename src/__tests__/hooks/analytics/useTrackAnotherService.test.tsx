import { useTrackScheduleAnotherServiceClick } from '@/src/app/_hooks';

describe('useTrackScheduleAnotherServiceClick (pure)', () => {
  beforeEach(() => {
    (global as any).window = Object.create(window);
    window.dataLayer = [];
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should push the correct event to dataLayer', () => {
    const { trackScheduleAnotherServiceClick } = useTrackScheduleAnotherServiceClick();

    trackScheduleAnotherServiceClick();

    expect(window.dataLayer).toContainEqual({
      event: 'schedule_another_service_click',
      event_category: 'Success Page',
      event_label: 'Agendar outro serviço',
    });
  });

  it('should not throw if window is undefined', () => {
    const originalWindow = global.window;
    // @ts-ignore
    delete global.window;

    const { trackScheduleAnotherServiceClick } = useTrackScheduleAnotherServiceClick();

    expect(() => {
      trackScheduleAnotherServiceClick();
    }).not.toThrow();

    global.window = originalWindow;
  });

  it('should initialize dataLayer if it does not exist', () => {
    const originalWindow = global.window;
    global.window = Object.create(window);
    // @ts-ignore
    delete global.window.dataLayer;

    const { trackScheduleAnotherServiceClick } = useTrackScheduleAnotherServiceClick();
    trackScheduleAnotherServiceClick();

    // Check that dataLayer was initialized and has the event
    expect(global.window.dataLayer).toBeDefined();
    expect(global.window.dataLayer.length).toBe(1);
    expect(global.window.dataLayer[0].event).toBe('schedule_another_service_click');

    global.window = originalWindow;
  });

  it('should initialize tagManagerDataLayer if it does not exist', () => {
    // Set up window with dataLayer but no tagManagerDataLayer
    window.dataLayer = [];
    window.tagManagerDataLayer = undefined as any;

    const { trackScheduleAnotherServiceClick } = useTrackScheduleAnotherServiceClick();
    trackScheduleAnotherServiceClick();

    // Verify tagManagerDataLayer was initialized and points to dataLayer
    expect(window.tagManagerDataLayer).toBeDefined();
    expect(window.tagManagerDataLayer).toBe(window.dataLayer);

    // Verify event was pushed to dataLayer
    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0].event).toBe('schedule_another_service_click');
  });

  it('should handle dataLayer push error', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Create a dataLayer with a push method that throws an error
    window.dataLayer = [];
    window.dataLayer.push = jest.fn().mockImplementation(() => {
      throw new Error('Test dataLayer error');
    });

    const { trackScheduleAnotherServiceClick } = useTrackScheduleAnotherServiceClick();

    // This should not throw
    expect(() => trackScheduleAnotherServiceClick()).not.toThrow();

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle dataLayer as non-array', () => {
    // Set dataLayer to a non-array value
    // @ts-ignore - Testing with invalid input
    window.dataLayer = 'not an array';

    const { trackScheduleAnotherServiceClick } = useTrackScheduleAnotherServiceClick();

    // This should not throw
    expect(() => trackScheduleAnotherServiceClick()).not.toThrow();

    // Should have initialized dataLayer as an array
    expect(Array.isArray(window.dataLayer)).toBe(true);
  });

  it('should push to tagManagerDataLayer when it differs from dataLayer', () => {
    // Set up different dataLayer and tagManagerDataLayer
    window.dataLayer = [];
    window.tagManagerDataLayer = [];

    // Ensure they are different objects
    expect(window.dataLayer).not.toBe(window.tagManagerDataLayer);

    const { trackScheduleAnotherServiceClick } = useTrackScheduleAnotherServiceClick();
    trackScheduleAnotherServiceClick();

    // Verify event was pushed to both dataLayer and tagManagerDataLayer
    expect(window.dataLayer).toHaveLength(1);
    expect(window.tagManagerDataLayer).toHaveLength(1);

    expect(window.dataLayer[0]).toEqual({
      event: 'schedule_another_service_click',
      event_category: 'Success Page',
      event_label: 'Agendar outro serviço',
    });

    expect(window.tagManagerDataLayer[0]).toEqual({
      event: 'schedule_another_service_click',
      event_category: 'Success Page',
      event_label: 'Agendar outro serviço',
    });
  });

  it('should handle unexpected errors gracefully', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Create a scenario that will trigger the outer catch block
    // by making the entire function throw an error
    const originalWindow = global.window;
    Object.defineProperty(global, 'window', {
      get: () => {
        throw new Error('Unexpected error');
      },
      configurable: true,
    });

    const { trackScheduleAnotherServiceClick } = useTrackScheduleAnotherServiceClick();

    // This should not throw despite the error
    expect(() => trackScheduleAnotherServiceClick()).not.toThrow();

    // Verify error was logged
    expect(console.error).toHaveBeenCalledWith(
      'Unexpected error in trackScheduleAnotherServiceClick:',
      expect.any(Error)
    );

    // Restore original window and console.error
    Object.defineProperty(global, 'window', {
      value: originalWindow,
      configurable: true,
    });
    console.error = originalConsoleError;
  });
});
