import { useHandleBeginCheckout } from '@/src/app/_hooks/analytics/useTrackBeginCheckout';
import mixpanel from 'mixpanel-browser';

// Mock console.error to avoid polluting test output
const originalConsoleError = console.error;
const mockConsoleError = jest.fn();

// Mock mixpanel
jest.mock('mixpanel-browser', () => ({
  track: jest.fn(),
}));

// Mock getUtmParams
const mockGetUtmParams = jest.fn().mockReturnValue({ utm_source: 'google' });

// Mock trackMixpanelEvent
const mockTrackMixpanelEvent = jest.fn();

// Mock the modules after all mocks are defined
const mockCommonModule = {
  getUtmParams: mockGetUtmParams,
};

// Mock the initMixpanel module
jest.mock('@/src/app/_lib/initMixpanel', () => ({
  trackMixpanelEvent: (...args: any[]) => mockTrackMixpanelEvent(...args),
}));

// Mock the window object and its properties
const mockWindow = {
  dataLayer: [],
  tagManagerDataLayer: [],
  gtag: jest.fn(),
  fbq: jest.fn(),
};

// Mock the global window object
Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true,
});

// Mock the modules
jest.mock('@/src/app/_functions/analytics/common', () => ({
  getUtmParams: (...args: any[]) => mockCommonModule.getUtmParams(...args),
}));

jest.mock('@/src/app/_lib/initMixpanel', () => ({
  trackMixpanelEvent: (eventName: string, properties: any) => {
    return mockTrackMixpanelEvent(eventName, properties);
  },
}));

// Mock process.env
const originalEnv = process.env;

beforeEach(() => {
  // Reset all mocks before each test
  jest.clearAllMocks();

  // Reset window object
  window.dataLayer = [];
  window.tagManagerDataLayer = [];
  window.gtag = jest.fn();
  window.fbq = jest.fn();

  // Set up default mock implementation
  mockGetUtmParams.mockReturnValue({ utm_source: 'test' });
  mockTrackMixpanelEvent.mockImplementation(() => {});

  // Reset process.env
  process.env = { ...originalEnv };
});

afterAll(() => {
  // Restore original process.env
  process.env = originalEnv;
});

describe('useHandleBeginCheckout', () => {
  let originalWindow: any;

  beforeEach(() => {
    originalWindow = global.window;
    global.window = Object.create(window);
    global.window.dataLayer = [];
    global.window.fbq = jest.fn();
    global.window.gtag = jest.fn();

    // Reset environment variable to ensure consistent test behavior
    delete process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT;
  });

  afterEach(() => {
    global.window = originalWindow;
    jest.clearAllMocks();
    mockGetUtmParams.mockReturnValue({ utm_source: 'google' });
    console.error = originalConsoleError; // Restore console.error
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  it('should push event to dataLayer, fbq, and mixpanel with correct data', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    const service = {
      id: 'test-service',
      name: 'Test Service',
      price: 99.9,
    };

    trackBeginCheckout(service);

    // Google Tag Manager
    expect(global.window.dataLayer[0]).toEqual({
      event: 'begin_checkout',
      ecommerce: {
        currency: 'BRL',
        value: 99.9,
        items: [
          {
            item_id: 'test-service',
            item_name: 'Test Service',
            price: 99.9,
            quantity: 1,
          },
        ],
      },
      utm_data: {
        utm_source: 'test', // Updated to match our mock
      },
    });

    // Google Analytics 4
    expect(global.window.gtag).toHaveBeenCalledWith('event', 'begin_checkout', {
      currency: 'BRL',
      value: 99.9,
      items: [
        {
          item_id: 'test-service',
          item_name: 'Test Service',
          price: 99.9,
          quantity: 1,
        },
      ],
    });

    // Meta Pixel (Facebook)
    expect(global.window.fbq).toHaveBeenCalledWith('track', 'InitiateCheckout', {
      content_ids: ['test-service'],
      content_name: 'Test Service',
      content_type: 'product',
      value: 99.9,
      currency: 'BRL',
      utm_source: 'test', // Updated to match our mock
    });

    // Mixpanel
    expect(mockTrackMixpanelEvent).toHaveBeenCalledWith(
      'Begin Checkout',
      expect.objectContaining({
        item_id: 'test-service',
        item_name: 'Test Service',
        price: 99.9,
        quantity: 1,
        currency: 'BRL',
        utm_source: 'test', // Updated to match our mock
      })
    );
  });

  it('should use homol event name when in homol environment', () => {
    // Set homol environment
    process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT = 'homol';
    const { trackBeginCheckout } = useHandleBeginCheckout();
    const service = {
      id: '123',
      name: 'Homol Service',
      price: 199.9,
    };

    trackBeginCheckout(service);

    // Check if homol event name was used
    expect(global.window.dataLayer[0].event).toBe('begin_checkout-homol');
    expect(mockTrackMixpanelEvent).toHaveBeenCalledWith(
      'Begin Checkout',
      expect.objectContaining({
        item_name: 'Homol Service',
      })
    );
  });

  it('should handle different tagManagerDataLayer', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();
    (global.window as any).dataLayer = [];
    (global.window as any).tagManagerDataLayer = [];

    const service = {
      id: 'tag-123',
      name: 'Tag Manager Test',
      price: 299.9,
    };

    trackBeginCheckout(service);

    // Check if data was pushed to both data layers
    expect((global.window as any).dataLayer).toHaveLength(1);
    expect((global.window as any).tagManagerDataLayer).toHaveLength(1);
    expect((global.window as any).tagManagerDataLayer[0].ecommerce.items[0].item_id).toBe(
      'tag-123'
    );
  });

  it('should handle missing service name and price', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    const service = {
      id: 'no-details',
    };

    trackBeginCheckout(service);

    // Check default values are used
    expect((global.window as any).dataLayer[0].ecommerce.items[0]).toEqual({
      item_id: 'no-details',
      item_name: '',
      price: 0,
      quantity: 1,
    });
  });

  it('should handle errors when calling fbq', () => {
    // Mock console.error
    console.error = mockConsoleError;
    const { trackBeginCheckout } = useHandleBeginCheckout();
    // Make fbq throw an error
    const error = new Error('FBQ error');
    (global.window as any).fbq = jest.fn().mockImplementationOnce(() => {
      throw error;
    });

    const service = {
      id: 'fbq-error',
      name: 'FBQ Error Test',
      price: 100,
    };

    // Should not throw
    trackBeginCheckout(service);

    // Verify error was handled silently (no console.error since we removed it)
    expect(mockConsoleError).not.toHaveBeenCalled();
    // Verify other analytics still worked
    expect(mockTrackMixpanelEvent).toHaveBeenCalled();
    expect((global.window as any).dataLayer).toHaveLength(1);
  });

  it('should handle errors when getting UTM params', () => {
    // Save original implementation
    const originalConsoleError = console.error;
    const mockConsoleError = jest.fn();
    console.error = mockConsoleError;

    // Mock getUtmParams to throw an error
    const mockError = new Error('UTM params error');
    mockGetUtmParams.mockImplementationOnce(() => {
      throw mockError;
    });

    const { trackBeginCheckout } = useHandleBeginCheckout();
    const service = {
      id: 'utm-error',
      name: 'UTM Error Test',
      price: 100,
    };

    // Clear any previous calls
    mockTrackMixpanelEvent.mockClear();

    // Should not throw
    trackBeginCheckout(service);

    // The Mixpanel event should NOT be called because getUtmParams threw an error
    expect(mockTrackMixpanelEvent).not.toHaveBeenCalled();

    // Verify the error was handled silently (no console.error since we removed it)
    expect(mockConsoleError).not.toHaveBeenCalled();

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should not execute if window is undefined', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Save original window
    const originalWindow = global.window;
    // @ts-ignore - Intentionally setting window to undefined for test
    global.window = undefined as unknown as Window & typeof globalThis;

    const service = {
      id: '123',
      name: 'Test Service',
      price: 99.9,
    };

    // This should not throw
    expect(() => trackBeginCheckout(service)).not.toThrow();

    // Restore window
    global.window = originalWindow;
  });

  it('should initialize dataLayer if it does not exist', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Delete dataLayer
    delete (global.window as any).dataLayer;
    delete (global.window as any).tagManagerDataLayer;

    const service = {
      id: '123',
      name: 'Test Service',
      price: 99.9,
    };

    trackBeginCheckout(service);

    // Check that dataLayer was initialized
    expect((global.window as any).dataLayer).toBeDefined();
    expect((global.window as any).dataLayer.length).toBe(1);
  });

  it('should use existing tagManagerDataLayer if it exists', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Set up a custom tagManagerDataLayer
    const customDataLayer: any = [];
    (global.window as any).tagManagerDataLayer = customDataLayer;

    const service = {
      id: 'test',
      name: 'Test Service',
      price: 50,
    };

    trackBeginCheckout(service);

    // Verify the custom tagManagerDataLayer was used
    expect((global.window as any).tagManagerDataLayer).toBe(customDataLayer);
  });

  it('should handle case where tagManagerDataLayer is different from dataLayer', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Set up different dataLayer and tagManagerDataLayer
    const dataLayer: any = [];
    const tagManagerDataLayer: any = [];
    (global.window as any).dataLayer = dataLayer;
    (global.window as any).tagManagerDataLayer = tagManagerDataLayer;

    const service = {
      id: 'test-diff',
      name: 'Test Different Layers',
      price: 75,
    };

    trackBeginCheckout(service);

    // Both dataLayer and tagManagerDataLayer should have the event
    expect(dataLayer.length).toBe(1);
    expect(tagManagerDataLayer.length).toBe(1);
    expect(dataLayer[0]).toEqual(tagManagerDataLayer[0]);
  });

  it('should handle case where tagManagerDataLayer is defined but different from dataLayer', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Set up dataLayer and a different tagManagerDataLayer
    const dataLayer: any = [];
    const existingTagManagerDataLayer: any = [];

    // Set dataLayer first
    (global.window as any).dataLayer = dataLayer;

    // Then set tagManagerDataLayer to something different
    (global.window as any).tagManagerDataLayer = existingTagManagerDataLayer;

    // Verify they are different references
    expect((global.window as any).dataLayer).not.toBe((global.window as any).tagManagerDataLayer);

    const service = {
      id: 'test-diff-refs',
      name: 'Test Different Layer Refs',
      price: 85,
    };

    trackBeginCheckout(service);

    // Both should have the event
    expect(dataLayer.length).toBe(1);
    expect(existingTagManagerDataLayer.length).toBe(1);

    // The event data should be the same
    expect(dataLayer[0].event).toBe('begin_checkout');
    expect(existingTagManagerDataLayer[0].event).toBe('begin_checkout');

    // The arrays should still be different references
    expect(dataLayer).not.toBe(existingTagManagerDataLayer);
  });

  it('should initialize tagManagerDataLayer to dataLayer when not defined', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Set up only dataLayer
    const dataLayer: any = [];
    (global.window as any).dataLayer = dataLayer;
    delete (global.window as any).tagManagerDataLayer;

    const service = {
      id: 'test-init-tagmanager',
      name: 'Test Init TagManager',
      price: 95,
    };

    trackBeginCheckout(service);

    // tagManagerDataLayer should now be defined and have the same content as dataLayer
    expect((global.window as any).tagManagerDataLayer).toBeDefined();
    expect((global.window as any).tagManagerDataLayer).toEqual(dataLayer);

    // The event should be in both layers
    expect(dataLayer.length).toBe(1);
    expect((global.window as any).tagManagerDataLayer.length).toBe(1);
    expect(dataLayer[0].event).toBe('begin_checkout');
  });

  it('should handle case where tagManagerDataLayer is undefined but dataLayer is defined', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Set up dataLayer but not tagManagerDataLayer
    const dataLayer: any = [];
    (global.window as any).dataLayer = dataLayer;

    // Explicitly set tagManagerDataLayer to undefined
    Object.defineProperty(window, 'tagManagerDataLayer', {
      value: undefined,
      writable: true,
      configurable: true,
    });

    const service = {
      id: 'test-undefined-tagmanager',
      name: 'Test Undefined TagManager',
      price: 105,
    };

    trackBeginCheckout(service);

    // tagManagerDataLayer should now be defined and equal to dataLayer
    expect((global.window as any).tagManagerDataLayer).toBeDefined();
    expect((global.window as any).tagManagerDataLayer).toBe(dataLayer);

    // The event should be in both layers
    expect(dataLayer.length).toBe(1);
    expect((global.window as any).tagManagerDataLayer.length).toBe(1);
    expect(dataLayer[0].event).toBe('begin_checkout');
  });

  it('should not call fbq if it is not a function', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Set fbq to a non-function value
    (global.window as any).fbq = 'not a function' as any;

    const service = {
      id: '123',
      name: 'Test Service',
      price: 99.9,
    };

    // This should not throw
    expect(() => trackBeginCheckout(service)).not.toThrow();

    // fbq should not be called
    expect(typeof (global.window as any).fbq).not.toBe('function');
  });

  it('should not call gtag if it is not a function', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Set gtag to a non-function value
    (global.window as any).gtag = 'not a function' as any;

    const service = {
      id: '123',
      name: 'Test Service',
      price: 99.9,
    };

    // This should not throw
    expect(() => trackBeginCheckout(service)).not.toThrow();

    // gtag should not be called
    expect(typeof (global.window as any).gtag).not.toBe('function');
  });

  it('should handle errors when calling mixpanel.track', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Make mixpanel.track throw an error
    (mixpanel.track as jest.Mock).mockImplementationOnce(() => {
      throw new Error('Test mixpanel error');
    });

    const service = {
      id: '123',
      name: 'Test Service',
      price: 99.9,
    };

    // This should not throw
    expect(() => trackBeginCheckout(service)).not.toThrow();
  });

  it('should handle errors when pushing to dataLayer', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Create a dataLayer with a push method that throws an error
    (global.window as any).dataLayer = [];
    (global.window as any).dataLayer.push = jest.fn().mockImplementationOnce(() => {
      throw new Error('Test dataLayer error');
    });

    const service = {
      id: '123',
      name: 'Test Service',
      price: 99.9,
    };

    // This should not throw
    expect(() => trackBeginCheckout(service)).not.toThrow();
  });

  it('should handle errors when calling gtag', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Make gtag throw an error
    (global.window as any).gtag = jest.fn().mockImplementationOnce(() => {
      throw new Error('Test gtag error');
    });

    const service = {
      id: '123',
      name: 'Test Service',
      price: 99.9,
    };

    // This should not throw
    expect(() => trackBeginCheckout(service)).not.toThrow();
  });

  it('should handle errors when calling fbq', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Make fbq throw an error
    (global.window as any).fbq = jest.fn().mockImplementationOnce(() => {
      throw new Error('Test fbq error');
    });

    const service = {
      id: '123',
      name: 'Test Service',
      price: 99.9,
    };

    // This should not throw
    expect(() => trackBeginCheckout(service)).not.toThrow();
  });

  it('should handle homol environment', () => {
    // Set homol environment
    process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT = 'homol';

    const { trackBeginCheckout } = useHandleBeginCheckout();

    const service = {
      id: '123',
      name: 'Test Service',
      price: 99.9,
    };

    trackBeginCheckout(service);

    // Check that dataLayer contains the event with homol suffix
    expect((global.window as any).dataLayer[0].event).toBe('begin_checkout-homol');

    // Check that gtag was called with homol suffix
    expect((global.window as any).gtag).toHaveBeenCalledWith(
      'event',
      'begin_checkout-homol',
      expect.any(Object)
    );

    // Remove environment variable
    if ('NEXT_PUBLIC_ANALYTICS_ENVIRONMENT' in process.env) {
      delete process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT;
    }
  });

  it('should handle service with missing properties', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Service with missing properties
    const service = {
      id: '123',
      // name is missing
      // price is missing
    } as any;

    // This should not throw
    expect(() => trackBeginCheckout(service)).not.toThrow();

    // Check that dataLayer contains the event with default values
    expect(global.window.dataLayer[0].ecommerce.items[0].item_name).toBe('');
    expect(global.window.dataLayer[0].ecommerce.items[0].price).toBe(0);
    expect(global.window.dataLayer[0].ecommerce.value).toBe(0);
  });

  it('should handle service with null values', () => {
    const { trackBeginCheckout } = useHandleBeginCheckout();

    // Service with null values
    const service = {
      id: '123',
      name: null,
      price: null,
    } as any;

    // This should not throw
    expect(() => trackBeginCheckout(service)).not.toThrow();

    // Check that dataLayer contains the event with default values
    expect(global.window.dataLayer[0].ecommerce.items[0].item_name).toBe('');
    expect(global.window.dataLayer[0].ecommerce.items[0].price).toBe(0);
    expect(global.window.dataLayer[0].ecommerce.value).toBe(0);
  });

  it('should handle unexpected error in main try-catch block', () => {
    // Save original implementation
    const originalGetUtmParams = mockCommonModule.getUtmParams;

    try {
      // Mock getUtmParams to throw an error
      mockCommonModule.getUtmParams = jest.fn().mockImplementationOnce(() => {
        throw new Error('Unexpected error');
      });

      const { trackBeginCheckout } = useHandleBeginCheckout();
      const service = {
        id: 'unexpected-error',
        name: 'Unexpected Error Test',
        price: 100,
      };

      // Should not throw
      expect(() => trackBeginCheckout(service)).not.toThrow();
    } finally {
      // Restore original implementation
      mockCommonModule.getUtmParams = originalGetUtmParams;
    }
    console.error = originalConsoleError;
  });
});
