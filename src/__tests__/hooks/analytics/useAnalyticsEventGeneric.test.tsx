// Import from the hook file with correct path reference
import { useAnalyticsEventGeneric } from '../../../app/_hooks/analytics/useAnalyticsEventGeneric';

// Add type declarations to prevent TypeScript errors
declare global {
  interface Window {
    dataLayer: any[];
    tagManagerDataLayer: any[];
    gtag: any;
  }
}

describe('useAnalyticsEventGeneric', () => {
  beforeEach(() => {
    (global as any).window = Object.create(window);
    window.dataLayer = [];
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should push a custom event with payload to dataLayer', () => {
    const { sendEvent } = useAnalyticsEventGeneric();

    const eventName = 'test_event';
    const payload = { foo: 'bar', value: 42 };

    sendEvent(eventName, payload);

    expect(window.dataLayer).toContainEqual({
      event: eventName,
      foo: 'bar',
      value: 42,
    });
  });

  it('should push a custom event without payload to dataLayer', () => {
    const { sendEvent } = useAnalyticsEventGeneric();

    sendEvent('simple_event');

    expect(window.dataLayer).toContainEqual({
      event: 'simple_event',
    });
  });

  it('should not throw if window is undefined', () => {
    const originalWindow = global.window;
    // @ts-ignore
    delete global.window;

    const { sendEvent } = useAnalyticsEventGeneric();

    expect(() => sendEvent('event_without_window')).not.toThrow();

    global.window = originalWindow;
  });

  it('should initialize dataLayer if it does not exist', () => {
    const originalWindow = global.window;
    global.window = Object.create(window);
    // @ts-ignore
    delete global.window.dataLayer;

    const { sendEvent } = useAnalyticsEventGeneric();
    sendEvent('initialize_datalayer_test');

    expect(window.dataLayer).toBeDefined();
    expect(window.dataLayer).toContainEqual({
      event: 'initialize_datalayer_test',
    });

    global.window = originalWindow;
  });

  it('should handle case where dataLayer exists but push is not a function', () => {
    const originalWindow = global.window;
    global.window = Object.create(window);
    // Create dataLayer as a non-array object
    // @ts-ignore
    global.window.dataLayer = {};

    const { sendEvent } = useAnalyticsEventGeneric();
    sendEvent('test_invalid_datalayer');

    // Should reinitialize dataLayer as an array
    expect(Array.isArray(window.dataLayer)).toBe(true);
    expect(window.dataLayer).toContainEqual({
      event: 'test_invalid_datalayer',
    });

    global.window = originalWindow;
  });

  it('should handle errors when pushing to dataLayer', () => {
    const originalConsoleError = console.error;
    console.error = jest.fn();

    const originalWindow = global.window;
    global.window = Object.create(window);

    // Create a dataLayer with a push method that throws an error
    window.dataLayer = [];
    window.dataLayer.push = jest.fn().mockImplementation(() => {
      throw new Error('Test error');
    });

    const { sendEvent } = useAnalyticsEventGeneric();
    sendEvent('error_test');

    // Should catch the error and log it
    expect(console.error).toHaveBeenCalledWith('Error sending analytics event:', expect.any(Error));

    // Restore console.error
    console.error = originalConsoleError;
    global.window = originalWindow;
  });

  it('should handle case where window.gtag is not a function', () => {
    // Set gtag to a non-function value
    window.gtag = 'not a function' as any;

    const { sendEvent } = useAnalyticsEventGeneric();

    // This should not throw
    expect(() => sendEvent('gtag_test')).not.toThrow();

    // Verify dataLayer was updated
    expect(window.dataLayer).toContainEqual({
      event: 'gtag_test',
    });

    // Verify gtag was not called as a function
    expect(typeof window.gtag).not.toBe('function');
  });

  it('should handle empty event name', () => {
    const originalConsoleWarn = console.warn;
    console.warn = jest.fn();

    const { sendEvent } = useAnalyticsEventGeneric();

    // Call with empty event name
    sendEvent('');

    // Should warn about missing event name
    expect(console.warn).toHaveBeenCalledWith('Event name is required for analytics tracking');

    // Should not add to dataLayer
    expect(window.dataLayer).not.toContainEqual({
      event: '',
    });

    // Restore console.warn
    console.warn = originalConsoleWarn;
  });

  it('should handle non-array dataLayer', () => {
    const originalWindow = global.window;
    global.window = Object.create(window);

    // Create dataLayer as a non-array object that is not empty
    // @ts-ignore
    global.window.dataLayer = { someProperty: 'value' };

    const { sendEvent } = useAnalyticsEventGeneric();
    sendEvent('test_non_array_datalayer');

    // Should reinitialize dataLayer as an array
    expect(Array.isArray(window.dataLayer)).toBe(true);
    expect(window.dataLayer).toContainEqual({
      event: 'test_non_array_datalayer',
    });

    global.window = originalWindow;
  });

  it('should handle case where window.gtag is a function', () => {
    // Mock gtag function
    window.gtag = jest.fn();

    const { sendEvent } = useAnalyticsEventGeneric();
    const eventName = 'gtag_function_test';
    const payload = { test: 'data' };

    sendEvent(eventName, payload);

    // Verify gtag was called with correct parameters
    expect(window.gtag).toHaveBeenCalledWith('event', eventName, payload);
  });

  it('should handle case where tagManagerDataLayer exists and is different from dataLayer', () => {
    // Setup window with both dataLayer and tagManagerDataLayer
    window.dataLayer = [];
    window.tagManagerDataLayer = [] as any;

    // Ensure they're different arrays
    expect(window.dataLayer).not.toBe(window.tagManagerDataLayer);

    const { sendEvent } = useAnalyticsEventGeneric();
    const eventName = 'tag_manager_test';

    sendEvent(eventName);

    // Verify both were updated
    expect(window.dataLayer).toContainEqual({ event: eventName });
    expect(window.tagManagerDataLayer).toContainEqual({ event: eventName });
  });

  it('should handle case where tagManagerDataLayer exists but is not an array', () => {
    // Setup window with dataLayer as array but tagManagerDataLayer as non-array
    window.dataLayer = [];
    window.tagManagerDataLayer = {} as any;

    const { sendEvent } = useAnalyticsEventGeneric();
    sendEvent('invalid_tag_manager');

    // Should update dataLayer but not tagManagerDataLayer
    expect(window.dataLayer).toContainEqual({ event: 'invalid_tag_manager' });
  });

  it('should handle case where tagManagerDataLayer exists but push is not a function', () => {
    // Setup window with tagManagerDataLayer as array but with a non-functional push method
    window.dataLayer = [];
    window.tagManagerDataLayer = [] as any;
    window.tagManagerDataLayer.push = {} as any; // Object instead of a function

    const { sendEvent } = useAnalyticsEventGeneric();

    // This should not throw
    expect(() => sendEvent('tag_manager_no_push')).not.toThrow();

    // dataLayer should still be updated
    expect(window.dataLayer).toContainEqual({ event: 'tag_manager_no_push' });
  });

  it('should handle errors when calling gtag', () => {
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Mock gtag to throw an error
    window.gtag = jest.fn().mockImplementation(() => {
      throw new Error('Test gtag error');
    });

    const { sendEvent } = useAnalyticsEventGeneric();

    // This should not throw
    expect(() => sendEvent('gtag_error_test')).not.toThrow();

    // Should catch the error and log it
    expect(console.error).toHaveBeenCalledWith('Error sending gtag event:', expect.any(Error));

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle multiple data layers correctly', () => {
    // Set up environment with multiple data layers
    window.dataLayer = [];
    window.tagManagerDataLayer = [];

    const spy1 = jest.spyOn(window.dataLayer, 'push');
    const spy2 = jest.spyOn(window.tagManagerDataLayer, 'push');

    const { sendEvent } = useAnalyticsEventGeneric();
    sendEvent('multi_layer_test');

    // Both data layers should be updated
    expect(spy1).toHaveBeenCalled();
    expect(spy2).toHaveBeenCalled();

    spy1.mockRestore();
    spy2.mockRestore();
  });

  it('should handle errors when pushing to tagManagerDataLayer', () => {
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Setup environment with working dataLayer but failing tagManagerDataLayer
    window.dataLayer = [];
    window.tagManagerDataLayer = [];

    // Make tagManagerDataLayer.push throw an error
    window.tagManagerDataLayer.push = jest.fn().mockImplementation(() => {
      throw new Error('Test tagManager error');
    });

    const { sendEvent } = useAnalyticsEventGeneric();

    // This should not throw despite tagManager error
    expect(() => sendEvent('tag_manager_error_test')).not.toThrow();

    // Should catch the error and log it
    expect(console.error).toHaveBeenCalledWith('Error sending analytics event:', expect.any(Error));

    // Regular dataLayer should still be updated
    expect(window.dataLayer).toContainEqual({ event: 'tag_manager_error_test' });

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle null event name', () => {
    const originalConsoleWarn = console.warn;
    console.warn = jest.fn();

    const { sendEvent } = useAnalyticsEventGeneric();

    // Call with null event name
    // @ts-ignore - Testing with invalid input
    sendEvent(null);

    // Should warn about missing event name
    expect(console.warn).toHaveBeenCalledWith('Event name is required for analytics tracking');

    // Should not add to dataLayer
    expect(window.dataLayer).toHaveLength(0);

    // Restore console.warn
    console.warn = originalConsoleWarn;
  });

  it('should handle undefined event name', () => {
    const originalConsoleWarn = console.warn;
    console.warn = jest.fn();

    const { sendEvent } = useAnalyticsEventGeneric();

    // Call with undefined event name
    // @ts-ignore - Testing with invalid input
    sendEvent(undefined);

    // Should warn about missing event name
    expect(console.warn).toHaveBeenCalledWith('Event name is required for analytics tracking');

    // Should not add to dataLayer
    expect(window.dataLayer).toHaveLength(0);

    // Restore console.warn
    console.warn = originalConsoleWarn;
  });

  it('should handle dataLayer as null', () => {
    const originalWindow = global.window;
    global.window = Object.create(window);

    // Set dataLayer to null
    // @ts-ignore - Testing with invalid input
    global.window.dataLayer = null;

    const { sendEvent } = useAnalyticsEventGeneric();
    sendEvent('test_null_datalayer');

    // Should reinitialize dataLayer as an array
    expect(Array.isArray(window.dataLayer)).toBe(true);
    expect(window.dataLayer).toContainEqual({
      event: 'test_null_datalayer',
    });

    global.window = originalWindow;
  });

  it('should handle dataLayer as undefined', () => {
    const originalWindow = global.window;
    global.window = Object.create(window);

    // Set dataLayer to undefined
    // @ts-ignore - Testing with invalid input
    global.window.dataLayer = undefined;

    const { sendEvent } = useAnalyticsEventGeneric();
    sendEvent('test_undefined_datalayer');

    // Should reinitialize dataLayer as an array
    expect(Array.isArray(window.dataLayer)).toBe(true);
    expect(window.dataLayer).toContainEqual({
      event: 'test_undefined_datalayer',
    });

    global.window = originalWindow;
  });
});
