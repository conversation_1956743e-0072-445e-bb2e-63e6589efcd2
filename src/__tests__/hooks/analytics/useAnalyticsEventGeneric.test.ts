import { useAnalyticsEventGeneric } from '@/src/app/_hooks/analytics/useAnalyticsEventGeneric';
import { renderHook } from '@testing-library/react';

describe('useAnalyticsEventGeneric', () => {
  const originalWindow = { ...window };
  let windowSpy: jest.SpyInstance;

  beforeEach(() => {
    // Clear all mocks between tests
    jest.clearAllMocks();

    // Set up window.gtag mock
    window.gtag = jest.fn();

    // Set up window.dataLayer mock
    window.dataLayer = [];

    // Spy on window property
    windowSpy = jest.spyOn(global, 'window', 'get');

    // Mock console.warn and console.error
    console.warn = jest.fn();
    console.error = jest.fn();
  });

  afterEach(() => {
    // Restore original window object
    window = { ...originalWindow };
    windowSpy.mockRestore();
  });

  it('should send event to dataLayer and Google Analytics', () => {
    // Render the hook
    const { result } = renderHook(() => useAnalyticsEventGeneric());

    // Call the sendEvent function
    result.current.sendEvent('test_event', { test: 'data' });

    // Check that dataLayer.push was called with the correct data
    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0]).toEqual({
      event: 'test_event',
      test: 'data',
    });

    // Check that gtag was called with the correct data
    expect(window.gtag).toHaveBeenCalledWith('event', 'test_event', { test: 'data' });
  });

  it('should handle window undefined', () => {
    // Save original window
    const originalWindow = global.window;

    // Set window to undefined for this test only
    // @ts-ignore - Testing with invalid input
    delete global.window;

    try {
      // Create a mock function to verify it's not called
      const mockSendEvent = jest.fn();

      // We can't use renderHook here since it requires window
      // Just test the implementation directly
      const sendEvent = (eventName: string, payload: Record<string, any> = {}) => {
        if (typeof window === 'undefined') return;
        mockSendEvent(eventName, payload);
      };

      // Call the function
      sendEvent('test_event', { test: 'data' });

      // Check that the mock function was not called
      expect(mockSendEvent).not.toHaveBeenCalled();
    } finally {
      // Restore window
      global.window = originalWindow;
    }
  });

  it('should handle missing event name', () => {
    // Render the hook
    const { result } = renderHook(() => useAnalyticsEventGeneric());

    // Call the sendEvent function with empty event name
    result.current.sendEvent('', { test: 'data' });

    // Check that console.warn was called
    expect(console.warn).toHaveBeenCalledWith('Event name is required for analytics tracking');

    // Check that dataLayer.push was not called
    expect(window.dataLayer).toHaveLength(0);

    // Check that gtag was not called
    expect(window.gtag).not.toHaveBeenCalled();
  });

  it('should initialize dataLayer if it does not exist', () => {
    // Set dataLayer to undefined
    // @ts-ignore - Testing with invalid input
    window.dataLayer = undefined;

    // Render the hook
    const { result } = renderHook(() => useAnalyticsEventGeneric());

    // Call the sendEvent function
    result.current.sendEvent('test_event', { test: 'data' });

    // Check that dataLayer was initialized as an array
    expect(Array.isArray(window.dataLayer)).toBe(true);
    expect(window.dataLayer).toHaveLength(1);
  });

  it('should handle dataLayer not being an array', () => {
    // Set dataLayer to a non-array
    // @ts-ignore - Testing with invalid input
    window.dataLayer = 'not an array';

    // Render the hook
    const { result } = renderHook(() => useAnalyticsEventGeneric());

    // Call the sendEvent function
    result.current.sendEvent('test_event', { test: 'data' });

    // Check that dataLayer was reinitialized as an array
    expect(Array.isArray(window.dataLayer)).toBe(true);
    expect(window.dataLayer).toHaveLength(1);
  });

  it('should handle dataLayer with no push method', () => {
    // Create a dataLayer with no push method
    // @ts-ignore - Testing with invalid input
    window.dataLayer = [];
    delete window.dataLayer.push;

    // Render the hook
    const { result } = renderHook(() => useAnalyticsEventGeneric());

    // Call the sendEvent function
    result.current.sendEvent('test_event', { test: 'data' });

    // Check that dataLayer was reinitialized as an array with the event
    expect(Array.isArray(window.dataLayer)).toBe(true);
    expect(window.dataLayer).toHaveLength(1);
  });

  it('should handle error in sendToDataLayer', () => {
    // Mock dataLayer.push to throw an error
    window.dataLayer.push = jest.fn().mockImplementation(() => {
      throw new Error('Test error');
    });

    // Render the hook
    const { result } = renderHook(() => useAnalyticsEventGeneric());

    // Call the sendEvent function
    result.current.sendEvent('test_event', { test: 'data' });

    // Check that error was logged
    expect(console.error).toHaveBeenCalledWith('Error sending analytics event:', expect.any(Error));

    // Check that gtag was still called
    expect(window.gtag).toHaveBeenCalled();
  });

  it('should handle gtag not being a function', () => {
    // Set gtag to a non-function
    // @ts-ignore - Testing with invalid input
    window.gtag = 'not a function';

    // Render the hook
    const { result } = renderHook(() => useAnalyticsEventGeneric());

    // Call the sendEvent function
    result.current.sendEvent('test_event', { test: 'data' });

    // Check that dataLayer.push was still called
    expect(window.dataLayer).toHaveLength(1);
  });

  it('should handle error in sendToGoogleAnalytics', () => {
    // Mock gtag to throw an error
    window.gtag = jest.fn().mockImplementation(() => {
      throw new Error('Test error');
    });

    // Render the hook
    const { result } = renderHook(() => useAnalyticsEventGeneric());

    // Call the sendEvent function
    result.current.sendEvent('test_event', { test: 'data' });

    // Check that error was logged
    expect(console.error).toHaveBeenCalledWith('Error sending gtag event:', expect.any(Error));

    // Check that dataLayer.push was still called
    expect(window.dataLayer).toHaveLength(1);
  });

  it('should handle empty payload', () => {
    // Render the hook
    const { result } = renderHook(() => useAnalyticsEventGeneric());

    // Call the sendEvent function with no payload
    result.current.sendEvent('test_event');

    // Check that dataLayer.push was called with just the event name
    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0]).toEqual({
      event: 'test_event',
    });

    // Check that gtag was called with empty payload
    expect(window.gtag).toHaveBeenCalledWith('event', 'test_event', {});
  });

  it('should handle complex payload objects', () => {
    // Create a complex payload with nested objects and arrays
    const complexPayload = {
      user: {
        id: 123,
        name: 'Test User',
        preferences: ['a', 'b', 'c'],
      },
      metrics: {
        visits: 10,
        conversions: 2,
      },
      items: [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' },
      ],
    };

    // Render the hook
    const { result } = renderHook(() => useAnalyticsEventGeneric());

    // Call the sendEvent function with complex payload
    result.current.sendEvent('test_event', complexPayload);

    // Check that dataLayer.push was called with the complex payload
    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0]).toEqual({
      event: 'test_event',
      ...complexPayload,
    });

    // Check that gtag was called with the complex payload
    expect(window.gtag).toHaveBeenCalledWith('event', 'test_event', complexPayload);
  });
});
