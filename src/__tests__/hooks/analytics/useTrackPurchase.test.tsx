import { useTrackPurchaseEvent } from '@/src/app/_hooks/analytics/useTrackPurchase';
import mixpanel from 'mixpanel-browser';
import { orderMock } from '../../__mocks__/orderMock';

jest.mock('mixpanel-browser', () => ({
  track: jest.fn(),
}));

jest.mock('@/src/app/_lib/initMixpanel', () => ({
  trackMixpanelEvent: jest.fn((eventName, properties) => {
    // Call the mocked mixpanel.track directly to ensure tests pass
    mixpanel.track(eventName, properties);
  }),
}));

describe('useTrackPurchaseEvent', () => {
  let originalWindow: any;

  beforeEach(() => {
    originalWindow = global.window;
    global.window = Object.create(window);
    (window as any).cookieConsent = 'granted';
    (window as any).dataLayer = [];
    (window as any).fbq = jest.fn();
    (window as any).gtag = jest.fn();
    (global as any).mixpanel = { track: jest.fn() };

    // Reset environment variables
    delete process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT;
  });

  afterEach(() => {
    global.window = originalWindow;
    jest.clearAllMocks();
  });

  it('should push to dataLayer, fbq and mixpanel with correct purchase data', () => {
    // Import the hook directly to avoid React hook validation
    const { trackPurchaseEvent } = useTrackPurchaseEvent();

    trackPurchaseEvent(orderMock);

    expect((global.window as any).dataLayer[0]).toEqual({
      event: 'purchase',
      ecommerce: {
        currency: 'BRL',
        value: 120.0,
        transaction_id: '',
        items: [
          {
            item_id: 1,
            item_name: 'Service Test',
            price: 120.0,
            quantity: 1,
          },
        ],
      },
    });

    expect((global.window as any).gtag).toHaveBeenCalledWith('event', 'purchase', {
      currency: 'BRL',
      value: 120.0,
      transaction_id: '',
      items: [
        {
          item_id: 1,
          item_name: 'Service Test',
          price: 120.0,
          quantity: 1,
        },
      ],
    });

    expect((global.window as any).fbq).toHaveBeenCalledWith('track', 'Purchase', {
      content_ids: [1],
      content_name: 'Service Test',
      content_type: 'product',
      value: 120.0,
      currency: 'BRL',
      transaction_id: '',
    });

    expect((mixpanel as any).track).toHaveBeenCalledWith('Purchase', {
      item_id: 1,
      item_name: 'Service Test',
      price: 120.0,
      quantity: 1,
      currency: 'BRL',
      transaction_id: '',
      distinct_id: '<EMAIL>',
    });
  });

  it('should include provided orderId as transaction_id', () => {
    const { trackPurchaseEvent } = useTrackPurchaseEvent();
    const testOrderId = 'test-order-123';

    trackPurchaseEvent(orderMock, testOrderId);

    expect((global.window as any).dataLayer[0].ecommerce.transaction_id).toBe(testOrderId);
    expect((global.window as any).gtag).toHaveBeenCalledWith(
      'event',
      'purchase',
      expect.objectContaining({
        transaction_id: testOrderId,
      })
    );
    expect((global.window as any).fbq).toHaveBeenCalledWith(
      'track',
      'Purchase',
      expect.objectContaining({
        transaction_id: testOrderId,
      })
    );
    expect(mixpanel.track).toHaveBeenCalledWith(
      'Purchase',
      expect.objectContaining({
        transaction_id: testOrderId,
      })
    );
  });

  it('should use homol event name in homol environment', () => {
    // Set homol environment
    process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT = 'homol';

    // Import the hook directly to avoid React hook validation
    const { trackPurchaseEvent } = useTrackPurchaseEvent();

    trackPurchaseEvent(orderMock);

    expect((global.window as any).dataLayer[0].event).toBe('purchase-homol');
    expect((global.window as any).gtag).toHaveBeenCalledWith(
      'event',
      'purchase-homol',
      expect.any(Object)
    );
  });

  it('should return early if orderData or service or price is missing', () => {
    const { trackPurchaseEvent } = useTrackPurchaseEvent();

    trackPurchaseEvent(null as any);
    trackPurchaseEvent({} as any);
    trackPurchaseEvent({ service: null } as any);
    trackPurchaseEvent({ service: { price: {} } } as any);

    expect((global.window as any).dataLayer.length).toBe(0);
    expect((global.window as any).fbq).not.toHaveBeenCalled();
    expect(mixpanel.track).not.toHaveBeenCalled();
  });

  it('should handle dataLayer push error', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Create a dataLayer with a push method that throws an error
    (global.window as any).dataLayer = [];
    (global.window as any).dataLayer.push = jest.fn().mockImplementationOnce(() => {
      throw new Error('Test dataLayer error');
    });

    const { trackPurchaseEvent } = useTrackPurchaseEvent();

    // This should not throw
    expect(() => trackPurchaseEvent(orderMock)).not.toThrow();

    // Check that error was logged
    expect(console.error).toHaveBeenCalledWith('Error pushing to dataLayer:', expect.any(Error));

    // Check that other tracking calls were still made
    expect((global.window as any).gtag).toHaveBeenCalled();
    expect((global.window as any).fbq).toHaveBeenCalled();
    expect(mixpanel.track).toHaveBeenCalled();

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle gtag error', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Make gtag throw an error
    (global.window as any).gtag = jest.fn().mockImplementationOnce(() => {
      throw new Error('Test gtag error');
    });

    const { trackPurchaseEvent } = useTrackPurchaseEvent();

    // This should not throw
    expect(() => trackPurchaseEvent(orderMock)).not.toThrow();

    // Check that error was logged
    expect(console.error).toHaveBeenCalledWith('Error sending event to gtag:', expect.any(Error));

    // Check that other tracking calls were still made
    expect((global.window as any).dataLayer.length).toBe(1);
    expect((global.window as any).fbq).toHaveBeenCalled();
    expect(mixpanel.track).toHaveBeenCalled();

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle fbq error', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Make fbq throw an error
    (global.window as any).fbq = jest.fn().mockImplementationOnce(() => {
      throw new Error('Test fbq error');
    });

    const { trackPurchaseEvent } = useTrackPurchaseEvent();

    // This should not throw
    expect(() => trackPurchaseEvent(orderMock)).not.toThrow();

    // Check that error was logged
    expect(console.error).toHaveBeenCalledWith('Error sending event to fbq:', expect.any(Error));

    // Check that other tracking calls were still made
    expect((global.window as any).dataLayer.length).toBe(1);
    expect((global.window as any).gtag).toHaveBeenCalled();
    expect(mixpanel.track).toHaveBeenCalled();

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle dataLayer as non-array', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Set dataLayer to a non-array value
    // @ts-ignore - Testing with invalid input
    (global.window as any).dataLayer = 'not an array';

    const { trackPurchaseEvent } = useTrackPurchaseEvent();

    // This should not throw
    expect(() => trackPurchaseEvent(orderMock)).not.toThrow();

    // Should have initialized dataLayer as an array
    expect(Array.isArray((global.window as any).dataLayer)).toBe(true);

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle unexpected error in main try-catch block', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Create a new object that will throw an error when accessed
    const errorOrderData = {
      get service() {
        throw new Error('Unexpected error');
      },
    };

    const { trackPurchaseEvent } = useTrackPurchaseEvent();

    // This should not throw
    expect(() => trackPurchaseEvent(errorOrderData as any)).not.toThrow();

    // Check that error was logged
    expect(console.error).toHaveBeenCalledWith(
      'Unexpected error in trackPurchaseEvent:',
      expect.any(Error)
    );

    // Restore console.error
    console.error = originalConsoleError;
  });
});
