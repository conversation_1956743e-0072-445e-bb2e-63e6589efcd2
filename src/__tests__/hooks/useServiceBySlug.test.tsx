// useServiceBySlug.test.tsx
import { useServiceContext } from '@/src/app/_context/ServiceContext';
import { useServiceBySlug } from '@/src/app/_hooks/useServiceBySlug';
import { describe, expect, it } from '@jest/globals';
import { renderHook } from '@testing-library/react';
import React from 'react';

// Mock the ServiceContext
jest.mock('@/src/app/_context/ServiceContext', () => ({
  useServiceContext: jest.fn(),
}));

const mockService = {
  id: 1,
  name: 'Test Service',
  slug: 'test-service',
  description: 'Test service description',
  imageUrl: '/test-image.jpg',
  status: 'active',
  price: {
    priceId: 1,
    originalPrice: 100,
    discountPrice: 20,
    finalPrice: 80,
  },
  provider: {
    id: 1, // Keep as number to match the expected type in the mock data
    name: 'Test Provider',
    description: 'Test provider description',
    imageUrl: '/provider-image.jpg',
    providerUrl: 'https://provider.com',
    testimonials: [],
  },
  availableIn: ['SP', 'RJ'],
  details: ['Detail 1', 'Detail 2'],
  serviceLimits: 'Service limits description',
  keywords: ['test', 'service'],
  termsConditionsUrl: 'https://example.com/terms',
  preparations: 'Service preparations',
  categoryName: 'Test Category',
  categorySlug: 'test-category',
  subcategoryName: 'Test Subcategory',
  subcategorySlug: 'test-subcategory',
};

const mockCategory = {
  id: '1',
  name: 'Test Category',
  slug: 'test-category',
  subcategories: [
    {
      id: '1-1',
      name: 'Test Subcategory',
      slug: 'test-subcategory',
      services: [mockService],
    },
  ],
};

describe('useServiceBySlug', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useServiceContext as jest.Mock).mockReturnValue({
      services: [mockCategory],
      isLoading: false,
      error: null,
    });
  });

  it('returns loading state initially when no initialData', () => {
    (useServiceContext as jest.Mock).mockReturnValue({
      services: null,
      isLoading: true,
      error: null,
    });

    const { result } = renderHook(() => useServiceBySlug('test-service'));

    // Initial render should be in loading state
    expect(result.current.isLoading).toBe(true);
    expect(result.current.service).toBeNull();
  });

  it('uses initialData when provided', () => {
    const initialData = {
      service: {
        ...mockService,
        id: 100,
        name: 'Initial Service',
        slug: 'initial-service',
      },
      provider: {
        ...mockService.provider,
        id: '100', // Convert to string for type compatibility
        name: 'Initial Provider',
      },
    };

    // Mock the context to include the initialData service
    const mockInitialService = {
      ...mockService,
      id: 100,
      name: 'Initial Service',
      slug: 'initial-service',
      provider: {
        ...mockService.provider,
        id: '100', // Convert to string for type compatibility
        name: 'Initial Provider',
      },
    };
    const mockInitialCategory = {
      ...mockCategory,
      subcategories: [
        {
          ...mockCategory.subcategories[0],
          services: [mockInitialService],
        },
      ],
    };
    (useServiceContext as jest.Mock).mockReturnValue({
      services: [mockInitialCategory],
      isLoading: false,
      error: null,
    });

    const { result } = renderHook(() => useServiceBySlug('initial-service', initialData));

    // The hook should immediately use the initialData, then update with context
    expect(result.current.service?.name).toBe('Initial Service');
    expect(result.current.provider?.name).toBe('Initial Provider');
    expect(result.current.isLoading).toBe(false);
  });

  // NOTE: This test verifies the supported scenario: the hook is mounted when the context is already loaded.
  // The current implementation of useServiceBySlug does NOT handle context updates after mount unless the slug changes.
  it('finds the service when context is loaded at mount', () => {
    const React = require('react');
    const ServiceContext = React.createContext({
      services: [mockCategory],
      isLoading: false,
      error: null,
    });
    function MockProvider({ children }: { children: React.ReactNode }) {
      return (
        <ServiceContext.Provider
          value={{ services: [mockCategory], isLoading: false, error: null }}
        >
          {children}
        </ServiceContext.Provider>
      );
    }
    const { useServiceContext } = require('@/src/app/_context/ServiceContext');
    (useServiceContext as jest.Mock).mockImplementation(() => React.useContext(ServiceContext));
    const { result } = renderHook(() => useServiceBySlug('test-service'), {
      wrapper: MockProvider,
    });
    expect(result.current.isLoading).toBe(false);
    expect(result.current.service?.name).toBe('Test Service');
    expect(result.current.error).toBeNull();
  });

  it('sets error state when provider is not in service data', () => {
    // Create a valid service without a provider
    const serviceWithoutProvider = {
      ...mockService,
      slug: 'no-provider-service',
    };

    // Create initial data with a valid service but undefined provider
    const initialData = {
      service: serviceWithoutProvider,
      provider: undefined,
    };

    // Use the initialData directly as it's the most controlled way to test this case
    const { result } = renderHook(() =>
      useServiceBySlug('no-provider-service', initialData as any)
    );

    // The hook should set error state when provider is missing
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isError).toBe(true); // Error state is set when provider is missing
    expect(result.current.service).toBeNull(); // Service is null in error state
    expect(result.current.provider).toBeNull(); // Provider is null as expected
  });

  it('returns error when service is not found', () => {
    const { result } = renderHook(() => useServiceBySlug('non-existent-service'));

    expect(result.current.isLoading).toBe(false);
    expect(result.current.service).toBeNull();
    expect(result.current.error).toContain('not found');
    expect(result.current.isError).toBe(true);
  });

  it('handles empty slug', () => {
    const { result } = renderHook(() => useServiceBySlug(''));

    expect(result.current.isLoading).toBe(false);
    expect(result.current.service).toBeNull();
    expect(result.current.provider).toBeNull();
    expect(result.current.error).toBeNull();
    expect(result.current.isError).toBe(false);
  });

  it('does not update state if slug is unchanged and already processed', () => {
    // Arrange
    const { result, rerender } = renderHook(({ slug }) => useServiceBySlug(slug), {
      initialProps: { slug: 'test-service' },
    });
    // Simulate effect: slugRef.current === slug and serviceProcessed.current === true
    // This is not directly accessible, but we can rerender with the same slug
    rerender({ slug: 'test-service' });
    // Should not update state or re-process
    expect(result.current.isLoading).toBe(false);
    expect(result.current.service?.name).toBe('Test Service');
    expect(result.current.error).toBeNull();
  });

  it('resets serviceProcessed when slug changes', () => {
    // Arrange
    const { result, rerender } = renderHook(({ slug }) => useServiceBySlug(slug), {
      initialProps: { slug: 'test-service' },
    });
    // Rerender with a new slug
    rerender({ slug: 'non-existent-service' });
    // Should update state for new slug
    expect(result.current.service).toBeNull();
    expect(result.current.error).toContain('not found');
    expect(result.current.isError).toBe(true);
  });

  it('handles case when services array is empty', () => {
    (useServiceContext as jest.Mock).mockReturnValue({
      services: [],
      isLoading: false,
      error: null,
    });

    const { result } = renderHook(() => useServiceBySlug('test-service'));

    expect(result.current.isLoading).toBe(false);
    expect(result.current.service).toBeNull();
    expect(result.current.error).toContain('not found');
    expect(result.current.isError).toBe(true);
  });

  it('handles case when services is not an array', () => {
    (useServiceContext as jest.Mock).mockReturnValue({
      services: {},
      isLoading: false,
      error: null,
    });

    const { result } = renderHook(() => useServiceBySlug('test-service'));

    expect(result.current.isLoading).toBe(true); // Still loading since services are invalid
    expect(result.current.service).toBeNull();
  });

  it('processes initialData only once', () => {
    // Create a test component that will allow us to directly validate
    // the initialDataProcessed ref behavior
    const React = require('react');

    // Create a spy for useState and useRef to verify the behavior
    const setStateMock = jest.fn();
    let refValue = { current: false };

    // Mock the useState and useRef hooks
    jest
      .spyOn(React, 'useState')
      .mockImplementation(() => [
        { service: mockService, provider: mockService.provider, isLoading: false },
        setStateMock,
      ]);
    jest.spyOn(React, 'useRef').mockImplementation((initialValue) => {
      if (initialValue === false) {
        return refValue;
      }
      // Return different refs for other useRef calls
      return { current: initialValue };
    });

    // Create initialData - need to convert provider.id to string for type compatibility
    const initialData = {
      service: mockService,
      provider: { ...mockService.provider, id: String(mockService.provider.id) },
    };

    // Mock the useEffect implementation to verify the condition
    let effectCallback;
    jest.spyOn(React, 'useEffect').mockImplementation((callback, deps) => {
      // If this is the effect with empty deps (the one processing initialData)
      if (deps && deps.length === 0) {
        effectCallback = callback;
      }
    });

    // Render the hook with proper type conversion
    renderHook(() => useServiceBySlug('test-service', initialData));

    // First call should set the ref to true
    if (effectCallback) {
      // Assert effectCallback is a function before calling it
      expect(typeof effectCallback).toBe('function');
      effectCallback();
      expect(refValue.current).toBe(true);
      expect(setStateMock).toHaveBeenCalled();
    }

    // Reset the mock to check second call
    setStateMock.mockClear();

    // Second call shouldn't update state because ref is true
    if (effectCallback) {
      effectCallback();
      expect(setStateMock).not.toHaveBeenCalled();
    }

    // Restore original React methods
    jest.restoreAllMocks();
  });

  it('handles services with no provider information', () => {
    // Mock a service with minimal provider information
    const mockIncompleteService = {
      ...mockService,
      provider: {
        id: 2, // Keep as number for consistency with mock
        name: 'Incomplete Provider',
        testimonials: [],
        // Missing description and imageUrl
      },
    };

    const mockIncompleteCategory = {
      ...mockCategory,
      subcategories: [
        {
          ...mockCategory.subcategories[0],
          services: [mockIncompleteService],
        },
      ],
    };

    (useServiceContext as jest.Mock).mockReturnValue({
      services: [mockIncompleteCategory],
      isLoading: false,
      error: null,
    });

    const { result } = renderHook(() => useServiceBySlug('test-service'));

    expect(result.current.isLoading).toBe(false);
    expect(result.current.service).not.toBeNull();
    expect(result.current.provider).not.toBeNull();
    expect(result.current.provider?.description).toBe('');
    expect(result.current.provider?.imageUrl).toBe('');
  });
});
