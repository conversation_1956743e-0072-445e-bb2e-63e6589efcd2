import { useDebouncedValidation } from '@/src/app/_hooks/useDebouncedValidation';
import { act, renderHook } from '@testing-library/react';
import { UseFormReturn } from 'react-hook-form';

// Mock the debounce function to execute immediately for testing
jest.mock('@/src/app/_utils/debounce', () => ({
  debounce: (fn: any) => fn,
}));

describe('useDebouncedValidation', () => {
  // Create a mock for the form
  const mockForm: Partial<UseFormReturn<Record<string, unknown>>> = {
    trigger: jest.fn(),
    formState: {
      errors: {},
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with all required functions', () => {
    // Act
    const { result } = renderHook(() => useDebouncedValidation(mockForm as UseFormReturn<any>));

    // Assert
    expect(typeof result.current.validateField).toBe('function');
    expect(typeof result.current.validateFieldImmediate).toBe('function');
    expect(typeof result.current.validateFields).toBe('function');
    expect(typeof result.current.hasError).toBe('function');
  });

  it('should call form.trigger immediately when validateFieldImmediate is called', () => {
    // Arrange
    const { result } = renderHook(() => useDebouncedValidation(mockForm as UseFormReturn<any>));

    // Act
    result.current.validateFieldImmediate('testField');

    // Assert
    expect(mockForm.trigger).toHaveBeenCalledWith('testField');
  });

  it('should detect errors correctly', () => {
    // Arrange
    const mockFormWithErrors: Partial<UseFormReturn<Record<string, unknown>>> = {
      ...mockForm,
      formState: {
        errors: {
          field1: { type: 'required', message: 'Field required' },
        },
      },
    };

    const { result } = renderHook(() =>
      useDebouncedValidation(mockFormWithErrors as UseFormReturn<any>)
    );

    // Act & Assert
    expect(result.current.hasError('field1')).toBe(true);
    expect(result.current.hasError('field2')).toBe(false);
  });

  it('should return debounced functions from validateField and validateFields', () => {
    // Arrange & Act
    const { result } = renderHook(() => useDebouncedValidation(mockForm as UseFormReturn<any>));

    // Assert - these functions return debounced functions
    const validateFieldFn = result.current.validateField();
    const validateFieldsFn = result.current.validateFields();

    expect(typeof validateFieldFn).toBe('function');
    expect(typeof validateFieldsFn).toBe('function');
  });

  it('should call form.trigger when validateField is called with a field name', () => {
    // Arrange
    const { result } = renderHook(() => useDebouncedValidation(mockForm as UseFormReturn<any>));

    // Act
    const validateFieldFn = result.current.validateField();
    act(() => {
      validateFieldFn('testField');
    });

    // Assert
    expect(mockForm.trigger).toHaveBeenCalledWith('testField');
  });

  it('should call form.trigger for each field when validateFields is called with field names', () => {
    // Arrange
    const { result } = renderHook(() => useDebouncedValidation(mockForm as UseFormReturn<any>));

    // Act
    const validateFieldsFn = result.current.validateFields();
    act(() => {
      validateFieldsFn(['field1', 'field2', 'field3']);
    });

    // Assert
    expect(mockForm.trigger).toHaveBeenCalledTimes(3);
    expect(mockForm.trigger).toHaveBeenCalledWith('field1');
    expect(mockForm.trigger).toHaveBeenCalledWith('field2');
    expect(mockForm.trigger).toHaveBeenCalledWith('field3');
  });

  it('should use custom delay when provided', () => {
    // Arrange & Act
    const customDelay = 500;
    const { result } = renderHook(() =>
      useDebouncedValidation(mockForm as UseFormReturn<any>, { delay: customDelay })
    );

    // Assert
    expect(result.current).toBeDefined();
  });
});
