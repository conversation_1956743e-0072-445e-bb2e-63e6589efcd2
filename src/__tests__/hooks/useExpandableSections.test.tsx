import { renderHook, act } from '@testing-library/react';
import { useExpandableSections } from '@/src/app/_hooks/useExpandableSections';

describe('useExpandableSections', () => {
  it('should initialize with the provided state', () => {
    // Arrange
    const initialState = {
      section1: true,
      section2: false,
      section3: true,
    };

    // Act
    const { result } = renderHook(() => useExpandableSections(initialState));

    // Assert
    expect(result.current.section1.isExpanded).toBe(true);
    expect(result.current.section2.isExpanded).toBe(false);
    expect(result.current.section3.isExpanded).toBe(true);
  });

  it('should have correct section IDs', () => {
    // Arrange
    const initialState = {
      section1: true,
      section2: false,
    };

    // Act
    const { result } = renderHook(() => useExpandableSections(initialState));

    // Assert
    expect(result.current.section1.id).toBe('section1');
    expect(result.current.section2.id).toBe('section2');
  });

  it('should toggle section state when toggle is called', () => {
    // Arrange
    const initialState = {
      section1: true,
      section2: false,
    };

    // Act
    const { result } = renderHook(() => useExpandableSections(initialState));

    // Initial state
    expect(result.current.section1.isExpanded).toBe(true);
    expect(result.current.section2.isExpanded).toBe(false);

    // Toggle section1
    act(() => {
      result.current.section1.toggle();
    });

    // Assert section1 is now closed
    expect(result.current.section1.isExpanded).toBe(false);
    expect(result.current.section2.isExpanded).toBe(false);

    // Toggle section2
    act(() => {
      result.current.section2.toggle();
    });

    // Assert section2 is now open
    expect(result.current.section1.isExpanded).toBe(false);
    expect(result.current.section2.isExpanded).toBe(true);
  });

  it('should handle empty initial state', () => {
    // Act
    const { result } = renderHook(() => useExpandableSections({}));

    // Assert
    expect(result.current).toEqual({});
  });

  it('should handle multiple toggles on the same section', () => {
    // Arrange
    const initialState = {
      section1: false,
    };

    // Act
    const { result } = renderHook(() => useExpandableSections(initialState));

    // Initial state
    expect(result.current.section1.isExpanded).toBe(false);

    // Toggle on
    act(() => {
      result.current.section1.toggle();
    });

    // Assert it's on
    expect(result.current.section1.isExpanded).toBe(true);

    // Toggle off again
    act(() => {
      result.current.section1.toggle();
    });

    // Assert it's off again
    expect(result.current.section1.isExpanded).toBe(false);
  });

  it('should maintain independent state for each section', () => {
    // Arrange
    const initialState = {
      section1: true,
      section2: true,
      section3: true,
    };

    // Act
    const { result } = renderHook(() => useExpandableSections(initialState));

    // Toggle sections in different ways
    act(() => {
      result.current.section1.toggle(); // Now false
      result.current.section3.toggle(); // Now false
    });

    // Assert sections updated correctly
    expect(result.current.section1.isExpanded).toBe(false);
    expect(result.current.section2.isExpanded).toBe(true); // Unchanged
    expect(result.current.section3.isExpanded).toBe(false);
  });
});
