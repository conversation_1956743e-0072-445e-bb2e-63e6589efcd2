import { useMenu } from '@/src/app/_hooks/useMenu';
import { act } from '@testing-library/react';
import { renderHook } from './__helpers__/renderHook';

/**
 * Testes para o hook useMenu
 */
describe('useMenu Hook', () => {
  it('should initialize with menu closed', () => {
    const { result } = renderHook(() => useMenu());

    expect(result.current.isMenuOpen).toBe(false);
  });

  it('should toggle menu state when toggleMenu is called', () => {
    const { result } = renderHook(() => useMenu());

    // Inicialmente o menu deve estar fechado
    expect(result.current.isMenuOpen).toBe(false);

    // Abrir o menu
    act(() => {
      result.current.toggleMenu();
    });

    // O menu deve estar aberto
    expect(result.current.isMenuOpen).toBe(true);

    // Fechar o menu
    act(() => {
      result.current.toggleMenu();
    });

    // O menu deve estar fechado novamente
    expect(result.current.isMenuOpen).toBe(false);
  });

  it('should update body class when menu state changes', () => {
    const { result } = renderHook(() => useMenu());

    // Estado inicial do body
    expect(document.body.classList.contains('menu-open')).toBe(false);

    // Abrir o menu
    act(() => {
      result.current.toggleMenu();
    });

    // Body deve ter a classe menu-open quando o menu está aberto
    expect(document.body.classList.contains('menu-open')).toBe(true);

    // Fechar o menu
    act(() => {
      result.current.toggleMenu();
    });

    // Body não deve ter a classe menu-open quando o menu está fechado
    expect(document.body.classList.contains('menu-open')).toBe(false);
  });
});
