import { useIsMobile } from '@/src/app/_hooks/useMobile';
import { act, renderHook } from '@testing-library/react';

describe('useIsMobile hook', () => {
  // Store original window.innerWidth
  const originalInnerWidth = window.innerWidth;

  // Mock the window.matchMedia function
  const mockMatchMedia = jest.fn();

  // Create a mock addEventListener function
  const mockAddEventListener = jest.fn();

  // Create a mock removeEventListener function
  const mockRemoveEventListener = jest.fn();

  beforeEach(() => {
    // Reset mocks between tests
    jest.resetAllMocks();

    // Mock matchMedia implementation
    mockMatchMedia.mockReturnValue({
      matches: false,
      addEventListener: mockAddEventListener,
      removeEventListener: mockRemoveEventListener,
    });

    // Apply the mock to window.matchMedia
    window.matchMedia = mockMatchMedia;
  });

  afterEach(() => {
    // Restore original window.innerWidth
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      value: originalInnerWidth,
    });
  });

  test('should return false for desktop viewport', () => {
    // Set window.innerWidth to desktop size
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      value: 1024, // desktop size
    });

    const { result } = renderHook(() => useIsMobile());

    expect(result.current).toBe(false);
    expect(mockAddEventListener).toHaveBeenCalledWith('change', expect.any(Function));
  });

  test('should return true for mobile viewport', () => {
    // Set window.innerWidth to mobile size
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      value: 480, // mobile size
    });

    const { result } = renderHook(() => useIsMobile());

    expect(result.current).toBe(true);
    expect(mockAddEventListener).toHaveBeenCalledWith('change', expect.any(Function));
  });

  test('should remove event listener on unmount', () => {
    const { unmount } = renderHook(() => useIsMobile());

    unmount();

    expect(mockRemoveEventListener).toHaveBeenCalledWith('change', expect.any(Function));
  });

  test('should update when window size changes', () => {
    // Start with desktop size
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      value: 1024,
    });

    const { result } = renderHook(() => useIsMobile());
    expect(result.current).toBe(false);

    // Simulate window resize to mobile size
    act(() => {
      // First, change the window.innerWidth
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        value: 480,
      });

      // Then, find the onChange function that was registered with addEventListener
      const onChangeFn = mockAddEventListener.mock.calls[0][1];

      // Call that function to simulate the media query change
      onChangeFn();
    });

    expect(result.current).toBe(true);
  });
});
