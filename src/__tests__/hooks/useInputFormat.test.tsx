import { renderHook } from '@testing-library/react';
import { useInputFormat } from '@/src/app/_hooks/useInputFormat';

describe('useInputFormat', () => {
  it('should initialize with all formatting functions', () => {
    // Act
    const { result } = renderHook(() => useInputFormat());

    // Assert
    expect(result.current.formatCep).toBeDefined();
    expect(result.current.formatNumbersOnly).toBeDefined();
    expect(result.current.capitalizeWords).toBeDefined();
    expect(result.current.formatCpf).toBeDefined();
  });

  describe('formatCep', () => {
    it('should format a CEP correctly', () => {
      // Arrange
      const { result } = renderHook(() => useInputFormat());

      // Act & Assert
      expect(result.current.formatCep('12345678')).toBe('12345-678');
      expect(result.current.formatCep('12345')).toBe('12345');
      expect(result.current.formatCep('1234')).toBe('1234');
      expect(result.current.formatCep('12345-678')).toBe('12345-678');
      expect(result.current.formatCep('123456789')).toBe('12345-678'); // Truncates excess digits
    });

    it('should handle empty and invalid inputs for CEP', () => {
      // Arrange
      const { result } = renderHook(() => useInputFormat());

      // Act & Assert
      expect(result.current.formatCep('')).toBe('');
      expect(result.current.formatCep('abc')).toBe('');
      expect(result.current.formatCep('123-45-678')).toBe('12345-678');
    });
  });

  describe('formatNumbersOnly', () => {
    it('should strip non-digit characters', () => {
      // Arrange
      const { result } = renderHook(() => useInputFormat());

      // Act & Assert
      expect(result.current.formatNumbersOnly('123')).toBe('123');
      expect(result.current.formatNumbersOnly('123abc')).toBe('123');
      expect(result.current.formatNumbersOnly('abc123')).toBe('123');
      expect(result.current.formatNumbersOnly('123-456')).toBe('123456');
      expect(result.current.formatNumbersOnly('(123) 456-7890')).toBe('1234567890');
      expect(result.current.formatNumbersOnly('')).toBe('');
    });
  });

  describe('capitalizeWords', () => {
    it('should capitalize each word in a string', () => {
      // Arrange
      const { result } = renderHook(() => useInputFormat());

      // Act & Assert
      expect(result.current.capitalizeWords('john doe')).toBe('John Doe');
      expect(result.current.capitalizeWords('JOHN DOE')).toBe('John Doe');
      expect(result.current.capitalizeWords('john DOE')).toBe('John Doe');
      expect(result.current.capitalizeWords('john doe smith')).toBe('John Doe Smith');
    });

    it('should handle empty and special cases for capitalizeWords', () => {
      // Arrange
      const { result } = renderHook(() => useInputFormat());

      // Act & Assert
      expect(result.current.capitalizeWords('')).toBe('');
      expect(result.current.capitalizeWords(' ')).toBe(' ');
      expect(result.current.capitalizeWords('a')).toBe('A');
    });
  });

  describe('formatCpf', () => {
    it('should format a CPF correctly', () => {
      // Arrange
      const { result } = renderHook(() => useInputFormat());

      // Act & Assert
      expect(result.current.formatCpf('12345678900')).toBe('123.456.789-00');
      expect(result.current.formatCpf('123')).toBe('123');
      expect(result.current.formatCpf('123456')).toBe('123.456');
      expect(result.current.formatCpf('123456789')).toBe('123.456.789');
    });

    it('should handle non-digit characters in CPF', () => {
      // Arrange
      const { result } = renderHook(() => useInputFormat());

      // Act & Assert
      expect(result.current.formatCpf('123.456.789-00')).toBe('123.456.789-00');
      expect(result.current.formatCpf('abc123def456ghi789jkl00')).toBe('123.456.789-00');
      expect(result.current.formatCpf('')).toBe('');
    });
  });
});
