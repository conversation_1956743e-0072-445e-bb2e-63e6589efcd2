import { orderQueryFn } from '@/src/app/_hooks/useOrderData';
import { OrderService } from '@/src/app/_services/order';

jest.mock('@/src/app/_services/order', () => ({
  OrderService: {
    getOrderByUuid: jest.fn(),
  },
}));

describe('orderQueryFn', () => {
  it('throws if orderId is undefined', async () => {
    await expect(() => orderQueryFn(undefined)).toThrow(
      'ID do pedido não encontrado nos parâmetros da URL'
    );
  });

  it('throws if orderId is null', async () => {
    await expect(() => orderQueryFn(null)).toThrow(
      'ID do pedido não encontrado nos parâmetros da URL'
    );
  });

  it('calls OrderService.getOrderByUuid if orderId is present', async () => {
    const mockOrder = { id: '123' };
    (OrderService.getOrderByUuid as jest.Mock).mockResolvedValueOnce(mockOrder);
    const result = orderQueryFn('123');
    expect(OrderService.getOrderByUuid).toHaveBeenCalledWith('123');
    expect(result).resolves.toEqual(mockOrder);
  });
});
