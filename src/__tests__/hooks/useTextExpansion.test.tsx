import { useTextExpansion } from '@/src/app/_hooks/useTextExpansion';
import { act, renderHook } from '@testing-library/react';

describe('useTextExpansion hook', () => {
  test('initializes with showFullText as false', () => {
    const { result } = renderHook(() => useTextExpansion());
    expect(result.current.showFullText).toBe(false);
  });

  test('toggleTextExpansion toggles showFullText state', () => {
    const { result } = renderHook(() => useTextExpansion());

    // Initially false
    expect(result.current.showFullText).toBe(false);

    // Toggle to true
    act(() => {
      result.current.toggleTextExpansion();
    });
    expect(result.current.showFullText).toBe(true);

    // Toggle back to false
    act(() => {
      result.current.toggleTextExpansion();
    });
    expect(result.current.showFullText).toBe(false);
  });

  test('truncateText truncates text when over maxLines length', () => {
    const { result } = renderHook(() => useTextExpansion({ charsPerLine: 10 }));

    // Text under the threshold (maxLines = 5, charsPerLine = 10)
    const shortText = 'Short text';
    expect(result.current.truncateText(shortText)).toBe(shortText);

    // Text over the threshold (maxLines = 5, charsPerLine = 10)
    const longText =
      'This is a very long text that should be truncated because it exceeds the maximum number of characters allowed';
    const truncated = result.current.truncateText(longText);

    // 50 chars (5 lines * 10 chars) + 3 for ellipsis = 53 characters
    expect(truncated.endsWith('...')).toBe(true);
    // Check specific text content without specifying exact length
    expect(truncated.startsWith('This is a very long text')).toBe(true);
  });

  test('truncateText returns empty string for undefined input', () => {
    const { result } = renderHook(() => useTextExpansion());
    expect(result.current.truncateText(undefined)).toBe('');
  });

  test('truncateText respects custom maxLines parameter', () => {
    const { result } = renderHook(() => useTextExpansion({ charsPerLine: 10 }));

    const longText = 'This is a very long text that should be truncated';

    // With custom maxLines = 2
    const truncatedCustom = result.current.truncateText(longText, 2);
    expect(truncatedCustom.length).toBe(23); // 20 chars + 3 for ellipsis
    expect(truncatedCustom.startsWith('This is a very')).toBe(true);
    expect(truncatedCustom.endsWith('...')).toBe(true);
  });

  test('formatText handles string input', () => {
    const { result } = renderHook(() => useTextExpansion());
    const text = 'Hello world';
    const defaultText = 'Default';

    expect(result.current.formatText(text, defaultText)).toBe(text);
  });

  test('formatText handles string array input', () => {
    const { result } = renderHook(() => useTextExpansion());
    const textArray = ['Hello', 'World'];
    const defaultText = 'Default';

    expect(result.current.formatText(textArray, defaultText)).toBe('Hello\nWorld');
  });

  test('formatText returns default text for undefined input', () => {
    const { result } = renderHook(() => useTextExpansion());
    const defaultText = 'Default';

    expect(result.current.formatText(undefined, defaultText)).toBe(defaultText);
  });

  test('shouldShowExpandButton returns true for long text', () => {
    const { result } = renderHook(() => useTextExpansion({ truncateThreshold: 20 }));
    const longText = 'This text is definitely longer than 20 characters';

    expect(result.current.shouldShowExpandButton(longText)).toBe(true);
  });

  test('shouldShowExpandButton returns false for short text', () => {
    const { result } = renderHook(() => useTextExpansion({ truncateThreshold: 50 }));
    const shortText = 'Short text';

    expect(result.current.shouldShowExpandButton(shortText)).toBe(false);
  });

  test('shouldShowExpandButton handles array input', () => {
    const { result } = renderHook(() => useTextExpansion({ truncateThreshold: 20 }));
    const longTextArray = ['This is a very long', 'text split into array items'];

    expect(result.current.shouldShowExpandButton(longTextArray)).toBe(true);
  });

  test('shouldShowExpandButton returns false for undefined input', () => {
    const { result } = renderHook(() => useTextExpansion());
    expect(result.current.shouldShowExpandButton(undefined)).toBe(false);
  });

  test('respects custom truncateThreshold', () => {
    const { result } = renderHook(() => useTextExpansion({ truncateThreshold: 10 }));
    const text = 'This text is more than 10 chars';

    expect(result.current.shouldShowExpandButton(text)).toBe(true);
  });
});
