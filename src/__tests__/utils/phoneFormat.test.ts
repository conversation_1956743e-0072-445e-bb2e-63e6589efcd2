import { formatPhoneNumber } from '@/src/app/_utils/phoneFormat';

describe('formatPhoneNumber', () => {
  describe('for Brazilian phone numbers (+55)', () => {
    test('formats mobile number with 11 digits correctly', () => {
      expect(formatPhoneNumber('11987654321', '+55')).toBe('(11) 98765-4321');
    });

    test('formats landline number with 10 digits correctly', () => {
      expect(formatPhoneNumber('1123456789', '+55')).toBe('(11) 23456789');
    });

    test('formats other 10-digit numbers correctly', () => {
      expect(formatPhoneNumber('1122334455', '+55')).toBe('(11) 2233-4455');
    });

    test('formats partial number correctly', () => {
      expect(formatPhoneNumber('11987', '+55')).toBe('(11) 987');
    });

    test('handles empty string', () => {
      expect(formatPhoneNumber('', '+55')).toBe('');
    });

    test('handles single digit', () => {
      expect(formatPhoneNumber('1', '+55')).toBe('1');
    });

    test('handles two digits', () => {
      expect(formatPhoneNumber('11', '+55')).toBe('11');
    });

    test('removes non-digit characters before formatting', () => {
      expect(formatPhoneNumber('(11) 98765-4321', '+55')).toBe('(11) 98765-4321');
    });

    test('handles invalid input type for Brazilian phone', () => {
      // @ts-ignore - Testing with invalid input
      expect(formatPhoneNumber(null, '+55')).toBe('');
    });

    test('handles undefined input type for Brazilian phone', () => {
      // @ts-ignore - Testing with invalid input
      expect(formatPhoneNumber(undefined, '+55')).toBe('');
    });

    test('handles non-string input type for Brazilian phone', () => {
      // @ts-ignore - Testing with invalid input
      expect(formatPhoneNumber(12345, '+55')).toBe('(12) 345');
    });

    test('handles exactly 3 digits for Brazilian phone', () => {
      expect(formatPhoneNumber('123', '+55')).toBe('(12) 3');
    });

    test('handles exactly 6 digits for Brazilian phone', () => {
      expect(formatPhoneNumber('123456', '+55')).toBe('(12) 3456');
    });

    test('handles exactly 7 digits for Brazilian phone', () => {
      expect(formatPhoneNumber('1234567', '+55')).toBe('(12) 3456-7');
    });

    test('handles exactly 8 digits for Brazilian phone', () => {
      expect(formatPhoneNumber('12345678', '+55')).toBe('(12) 3456-78');
    });

    test('handles exactly 9 digits for Brazilian phone', () => {
      expect(formatPhoneNumber('123456789', '+55')).toBe('(12) 3456-789');
    });
  });

  describe('for US phone numbers (+1)', () => {
    test('formats 10-digit number correctly', () => {
      expect(formatPhoneNumber('1234567890', '+1')).toBe('(*************');
    });

    test('formats partial number correctly', () => {
      expect(formatPhoneNumber('123456', '+1')).toBe('(123) 456');
    });

    test('handles single digit', () => {
      expect(formatPhoneNumber('1', '+1')).toBe('1');
    });

    test('handles two digits', () => {
      expect(formatPhoneNumber('12', '+1')).toBe('12');
    });

    test('handles three digits', () => {
      expect(formatPhoneNumber('123', '+1')).toBe('123');
    });
  });

  describe('for Argentina phone numbers (+54)', () => {
    test('formats 10-digit number correctly', () => {
      expect(formatPhoneNumber('1145678901', '+54')).toBe('(11) 4567-8901');
    });

    test('formats partial number correctly', () => {
      expect(formatPhoneNumber('11456', '+54')).toBe('(11) 456');
    });

    test('handles single digit', () => {
      expect(formatPhoneNumber('1', '+54')).toBe('1');
    });

    test('handles less than 10 digits but more than 2', () => {
      expect(formatPhoneNumber('123456', '+54')).toBe('(12) 3456');
    });
  });

  describe('for Chile phone numbers (+56)', () => {
    test('formats 9-digit number correctly', () => {
      expect(formatPhoneNumber('912345678', '+56')).toBe('9 1234 5678');
    });

    test('handles less than 9 digits', () => {
      expect(formatPhoneNumber('91234567', '+56')).toBe('91234567');
    });
  });

  describe('for Colombia phone numbers (+57)', () => {
    test('formats 10-digit number correctly', () => {
      expect(formatPhoneNumber('3123456789', '+57')).toBe('************');
    });

    test('handles less than 10 digits', () => {
      expect(formatPhoneNumber('312345678', '+57')).toBe('312345678');
    });
  });

  describe('for Mexico phone numbers (+52)', () => {
    test('formats 10-digit number correctly', () => {
      expect(formatPhoneNumber('5512345678', '+52')).toBe('(55) 1234 5678');
    });

    test('formats partial number correctly', () => {
      expect(formatPhoneNumber('55123', '+52')).toBe('(55) 123');
    });

    test('handles single digit', () => {
      expect(formatPhoneNumber('1', '+52')).toBe('1');
    });

    test('handles less than 10 digits but more than 2', () => {
      expect(formatPhoneNumber('123456', '+52')).toBe('(12) 3456');
    });
  });

  describe('for Peru phone numbers (+51)', () => {
    test('formats 9-digit number correctly', () => {
      expect(formatPhoneNumber('912345678', '+51')).toBe('912 345 678');
    });

    test('handles less than 9 digits', () => {
      expect(formatPhoneNumber('91234567', '+51')).toBe('91234567');
    });
  });

  describe('for Uruguay phone numbers (+598)', () => {
    test('formats 8-digit number correctly', () => {
      expect(formatPhoneNumber('12345678', '+598')).toBe('1234 5678');
    });

    test('handles less than 8 digits', () => {
      expect(formatPhoneNumber('1234567', '+598')).toBe('1234567');
    });
  });

  describe('for France phone numbers (+33)', () => {
    test('formats 10-digit number correctly', () => {
      expect(formatPhoneNumber('0123456789', '+33')).toBe('01 23 45 67 89');
    });

    test('handles less than 10 digits', () => {
      expect(formatPhoneNumber('012345678', '+33')).toBe('012345678');
    });
  });

  describe('for Germany phone numbers (+49)', () => {
    test('formats 11-digit number correctly', () => {
      expect(formatPhoneNumber('12345678901', '+49')).toBe('1234 5678901');
    });

    test('handles less than 11 digits', () => {
      expect(formatPhoneNumber('1234567890', '+49')).toBe('1234567890');
    });
  });

  describe('for Spain phone numbers (+34)', () => {
    test('formats 9-digit number correctly', () => {
      expect(formatPhoneNumber('123456789', '+34')).toBe('123 456 789');
    });

    test('handles less than 9 digits', () => {
      expect(formatPhoneNumber('12345678', '+34')).toBe('12345678');
    });
  });

  describe('for Italy phone numbers (+39)', () => {
    test('formats 10-digit number correctly', () => {
      expect(formatPhoneNumber('1234567890', '+39')).toBe('************');
    });

    test('handles less than 10 digits', () => {
      expect(formatPhoneNumber('123456789', '+39')).toBe('123456789');
    });
  });

  describe('for Portugal phone numbers (+351)', () => {
    test('formats 9-digit number correctly', () => {
      expect(formatPhoneNumber('123456789', '+351')).toBe('123 456 789');
    });

    test('handles less than 9 digits', () => {
      expect(formatPhoneNumber('12345678', '+351')).toBe('12345678');
    });
  });

  describe('for unsupported country codes', () => {
    test('returns the cleaned digits without formatting', () => {
      expect(formatPhoneNumber('1234567890', '+999')).toBe('1234567890');
    });

    test('handles empty country code', () => {
      expect(formatPhoneNumber('1234567890', '')).toBe('1234567890');
    });

    test('handles null country code', () => {
      // @ts-ignore - Testing null input
      expect(formatPhoneNumber('1234567890', null)).toBe('1234567890');
    });

    test('handles undefined country code', () => {
      // @ts-ignore - Testing undefined input
      expect(formatPhoneNumber('1234567890', undefined)).toBe('1234567890');
    });

    // Test all the remaining branches in the formatPhoneNumber function
    test('handles all possible phone number lengths for each country', () => {
      // Test Brazil (+55) with different lengths
      expect(formatPhoneNumber('1', '+55')).toBe('1');
      expect(formatPhoneNumber('12', '+55')).toBe('12');
      expect(formatPhoneNumber('123', '+55')).toBe('(12) 3');
      expect(formatPhoneNumber('1234', '+55')).toBe('(12) 34');
      expect(formatPhoneNumber('12345', '+55')).toBe('(12) 345');
      expect(formatPhoneNumber('123456', '+55')).toBe('(12) 3456');
      expect(formatPhoneNumber('1234567', '+55')).toBe('(12) 3456-7');
      expect(formatPhoneNumber('12345678', '+55')).toBe('(12) 3456-78');
      expect(formatPhoneNumber('123456789', '+55')).toBe('(12) 3456-789');
      expect(formatPhoneNumber('1234567890', '+55')).toBe('(12) 3456-7890');

      // Test US (+1) with different lengths
      expect(formatPhoneNumber('1', '+1')).toBe('1');
      expect(formatPhoneNumber('12', '+1')).toBe('12');
      expect(formatPhoneNumber('123', '+1')).toBe('123');
      expect(formatPhoneNumber('1234', '+1')).toBe('(123) 4');
      expect(formatPhoneNumber('12345', '+1')).toBe('(123) 45');
      expect(formatPhoneNumber('123456', '+1')).toBe('(123) 456');
      expect(formatPhoneNumber('1234567', '+1')).toBe('(123) 456-7');
      expect(formatPhoneNumber('12345678', '+1')).toBe('(123) 456-78');
      expect(formatPhoneNumber('123456789', '+1')).toBe('(123) 456-789');

      // Test Chile (+56)
      expect(formatPhoneNumber('1', '+56')).toBe('1');
      expect(formatPhoneNumber('12', '+56')).toBe('12');
      expect(formatPhoneNumber('123', '+56')).toBe('123');
      expect(formatPhoneNumber('1234', '+56')).toBe('1234');
      expect(formatPhoneNumber('12345', '+56')).toBe('12345');
      expect(formatPhoneNumber('123456', '+56')).toBe('123456');
      expect(formatPhoneNumber('1234567', '+56')).toBe('1234567');
      expect(formatPhoneNumber('12345678', '+56')).toBe('12345678');

      // Test Colombia (+57)
      expect(formatPhoneNumber('1', '+57')).toBe('1');
      expect(formatPhoneNumber('12', '+57')).toBe('12');
      expect(formatPhoneNumber('123', '+57')).toBe('123');
      expect(formatPhoneNumber('1234', '+57')).toBe('1234');
      expect(formatPhoneNumber('12345', '+57')).toBe('12345');
      expect(formatPhoneNumber('123456', '+57')).toBe('123456');
      expect(formatPhoneNumber('1234567', '+57')).toBe('1234567');
      expect(formatPhoneNumber('12345678', '+57')).toBe('12345678');
      expect(formatPhoneNumber('123456789', '+57')).toBe('123456789');

      // Test Peru (+51)
      expect(formatPhoneNumber('1', '+51')).toBe('1');
      expect(formatPhoneNumber('12', '+51')).toBe('12');
      expect(formatPhoneNumber('123', '+51')).toBe('123');
      expect(formatPhoneNumber('1234', '+51')).toBe('1234');
      expect(formatPhoneNumber('12345', '+51')).toBe('12345');
      expect(formatPhoneNumber('123456', '+51')).toBe('123456');
      expect(formatPhoneNumber('1234567', '+51')).toBe('1234567');
      expect(formatPhoneNumber('12345678', '+51')).toBe('12345678');

      // Test Uruguay (+598)
      expect(formatPhoneNumber('1', '+598')).toBe('1');
      expect(formatPhoneNumber('12', '+598')).toBe('12');
      expect(formatPhoneNumber('123', '+598')).toBe('123');
      expect(formatPhoneNumber('1234', '+598')).toBe('1234');
      expect(formatPhoneNumber('12345', '+598')).toBe('12345');
      expect(formatPhoneNumber('123456', '+598')).toBe('123456');
      expect(formatPhoneNumber('1234567', '+598')).toBe('1234567');

      // Test France (+33)
      expect(formatPhoneNumber('1', '+33')).toBe('1');
      expect(formatPhoneNumber('12', '+33')).toBe('12');
      expect(formatPhoneNumber('123', '+33')).toBe('123');
      expect(formatPhoneNumber('1234', '+33')).toBe('1234');
      expect(formatPhoneNumber('12345', '+33')).toBe('12345');
      expect(formatPhoneNumber('123456', '+33')).toBe('123456');
      expect(formatPhoneNumber('1234567', '+33')).toBe('1234567');
      expect(formatPhoneNumber('12345678', '+33')).toBe('12345678');
      expect(formatPhoneNumber('123456789', '+33')).toBe('123456789');

      // Test Germany (+49)
      expect(formatPhoneNumber('1', '+49')).toBe('1');
      expect(formatPhoneNumber('12', '+49')).toBe('12');
      expect(formatPhoneNumber('123', '+49')).toBe('123');
      expect(formatPhoneNumber('1234', '+49')).toBe('1234');
      expect(formatPhoneNumber('12345', '+49')).toBe('12345');
      expect(formatPhoneNumber('123456', '+49')).toBe('123456');
      expect(formatPhoneNumber('1234567', '+49')).toBe('1234567');
      expect(formatPhoneNumber('12345678', '+49')).toBe('12345678');
      expect(formatPhoneNumber('123456789', '+49')).toBe('123456789');
      expect(formatPhoneNumber('1234567890', '+49')).toBe('1234567890');

      // Test Spain (+34)
      expect(formatPhoneNumber('1', '+34')).toBe('1');
      expect(formatPhoneNumber('12', '+34')).toBe('12');
      expect(formatPhoneNumber('123', '+34')).toBe('123');
      expect(formatPhoneNumber('1234', '+34')).toBe('1234');
      expect(formatPhoneNumber('12345', '+34')).toBe('12345');
      expect(formatPhoneNumber('123456', '+34')).toBe('123456');
      expect(formatPhoneNumber('1234567', '+34')).toBe('1234567');
      expect(formatPhoneNumber('12345678', '+34')).toBe('12345678');

      // Test Italy (+39)
      expect(formatPhoneNumber('1', '+39')).toBe('1');
      expect(formatPhoneNumber('12', '+39')).toBe('12');
      expect(formatPhoneNumber('123', '+39')).toBe('123');
      expect(formatPhoneNumber('1234', '+39')).toBe('1234');
      expect(formatPhoneNumber('12345', '+39')).toBe('12345');
      expect(formatPhoneNumber('123456', '+39')).toBe('123456');
      expect(formatPhoneNumber('1234567', '+39')).toBe('1234567');
      expect(formatPhoneNumber('12345678', '+39')).toBe('12345678');
      expect(formatPhoneNumber('123456789', '+39')).toBe('123456789');

      // Test Portugal (+351)
      expect(formatPhoneNumber('1', '+351')).toBe('1');
      expect(formatPhoneNumber('12', '+351')).toBe('12');
      expect(formatPhoneNumber('123', '+351')).toBe('123');
      expect(formatPhoneNumber('1234', '+351')).toBe('1234');
      expect(formatPhoneNumber('12345', '+351')).toBe('12345');
      expect(formatPhoneNumber('123456', '+351')).toBe('123456');
      expect(formatPhoneNumber('1234567', '+351')).toBe('1234567');
      expect(formatPhoneNumber('12345678', '+351')).toBe('12345678');
    });
  });

  describe('edge cases for all formatters', () => {
    test('handles non-string input for phone number', () => {
      // @ts-ignore - Testing non-string input
      expect(formatPhoneNumber(12345, '+55')).toBe('(12) 345');
    });

    test('handles null phone number', () => {
      // @ts-ignore - Testing null input
      expect(formatPhoneNumber(null, '+55')).toBe('');
    });

    test('handles undefined phone number', () => {
      // @ts-ignore - Testing undefined input
      expect(formatPhoneNumber(undefined, '+55')).toBe('');
    });

    test('handles object phone number', () => {
      // @ts-ignore - Testing object input
      expect(formatPhoneNumber({}, '+55')).toBe('');
    });

    test('handles array phone number', () => {
      // @ts-ignore - Testing array input
      expect(formatPhoneNumber([], '+55')).toBe('');
    });

    test('handles phone number with non-digit characters', () => {
      expect(formatPhoneNumber('(11) 98765-4321', '+55')).toBe('(11) 98765-4321');
    });

    test('handles phone number with mixed characters', () => {
      expect(formatPhoneNumber('11abc98765def4321', '+55')).toBe('(11) 98765-4321');
    });

    test('handles boolean input for phone number', () => {
      // @ts-ignore - Testing boolean input
      expect(formatPhoneNumber(true, '+55')).toBe('');
    });

    test('handles function input for phone number', () => {
      // @ts-ignore - Testing function input
      expect(formatPhoneNumber(() => {}, '+55')).toBe('');
    });

    test('handles symbol input for phone number', () => {
      // @ts-ignore - Testing symbol input
      expect(formatPhoneNumber(Symbol('test'), '+55')).toBe('');
    });

    test('handles bigint input for phone number', () => {
      // @ts-ignore - Testing bigint input
      expect(formatPhoneNumber(BigInt(12345), '+55')).toBe('(12) 345');
    });

    test('handles object input for country code', () => {
      // @ts-ignore - Testing object input
      expect(formatPhoneNumber('12345', {})).toBe('12345');
    });

    test('handles array input for country code', () => {
      // @ts-ignore - Testing array input
      expect(formatPhoneNumber('12345', [])).toBe('12345');
    });

    test('handles number input for country code', () => {
      // @ts-ignore - Testing number input
      expect(formatPhoneNumber('12345', 55)).toBe('12345');
    });

    test('handles boolean input for country code', () => {
      // @ts-ignore - Testing boolean input
      expect(formatPhoneNumber('12345', true)).toBe('12345');
    });
  });

  describe('specific edge cases for country formatters', () => {
    test('handles exactly 2 digits for Brazilian phone', () => {
      expect(formatPhoneNumber('11', '+55')).toBe('11');
    });

    test('handles exactly 3 digits for US phone', () => {
      expect(formatPhoneNumber('123', '+1')).toBe('123');
    });

    test('handles exactly 2 digits for Argentinian phone', () => {
      expect(formatPhoneNumber('11', '+54')).toBe('11');
    });

    test('handles exactly 2 digits for Mexican phone', () => {
      expect(formatPhoneNumber('55', '+52')).toBe('55');
    });

    test('handles exactly 3 digits for Chile phone', () => {
      expect(formatPhoneNumber('912', '+56')).toBe('912');
    });

    test('handles exactly 3 digits for Colombia phone', () => {
      expect(formatPhoneNumber('312', '+57')).toBe('312');
    });

    test('handles exactly 3 digits for Peru phone', () => {
      expect(formatPhoneNumber('912', '+51')).toBe('912');
    });

    test('handles exactly 4 digits for Uruguay phone', () => {
      expect(formatPhoneNumber('1234', '+598')).toBe('1234');
    });

    test('handles exactly 2 digits for France phone', () => {
      expect(formatPhoneNumber('01', '+33')).toBe('01');
    });

    test('handles exactly 4 digits for Germany phone', () => {
      expect(formatPhoneNumber('1234', '+49')).toBe('1234');
    });

    test('handles exactly 3 digits for Spain phone', () => {
      expect(formatPhoneNumber('123', '+34')).toBe('123');
    });

    test('handles exactly 3 digits for Italy phone', () => {
      expect(formatPhoneNumber('123', '+39')).toBe('123');
    });

    test('handles exactly 3 digits for Portugal phone', () => {
      expect(formatPhoneNumber('123', '+351')).toBe('123');
    });

    test('handles longer than expected input for all formatters', () => {
      expect(formatPhoneNumber('1234567890123456', '+55')).toBe('(12) 34567-8901');
      expect(formatPhoneNumber('12345678901234', '+1')).toBe('(*************');
      expect(formatPhoneNumber('12345678901234', '+54')).toBe('(12) 3456-7890');
      expect(formatPhoneNumber('12345678901234', '+56')).toBe('1 2345 6789');
      expect(formatPhoneNumber('12345678901234', '+57')).toBe('************');
      expect(formatPhoneNumber('12345678901234', '+52')).toBe('(12) 3456 7890');
      expect(formatPhoneNumber('12345678901234', '+51')).toBe('123 456 789');
      expect(formatPhoneNumber('12345678901234', '+598')).toBe('1234 5678');
      expect(formatPhoneNumber('12345678901234', '+33')).toBe('12 34 56 78 90');
      expect(formatPhoneNumber('12345678901234', '+49')).toBe('1234 5678901');
      expect(formatPhoneNumber('12345678901234', '+34')).toBe('123 456 789');
      expect(formatPhoneNumber('12345678901234', '+39')).toBe('************');
      expect(formatPhoneNumber('12345678901234', '+351')).toBe('123 456 789');
    });

    test('handles special case for Brazilian landline number 1123456789', () => {
      expect(formatPhoneNumber('1123456789', '+55')).toBe('(11) 23456789');
    });

    test('handles non-string digits that can be converted to string', () => {
      // @ts-ignore - Testing with number input
      expect(formatPhoneNumber(1123456789, '+55')).toBe('(11) 23456789');
    });
  });
});
