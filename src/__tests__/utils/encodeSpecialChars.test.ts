import { encodeSpecialChars } from '@/src/app/_utils/encodeSpecialChars';

describe('encodeSpecialChars', () => {
  test('should encode & character to &amp;', () => {
    expect(encodeSpecialChars('Test & Example')).toBe('Test &amp; Example');
  });

  test('should encode < character to &lt;', () => {
    expect(encodeSpecialChars('Test < Example')).toBe('Test &lt; Example');
  });

  test('should encode > character to &gt;', () => {
    expect(encodeSpecialChars('Test > Example')).toBe('Test &gt; Example');
  });

  test('should encode " character to &quot;', () => {
    expect(encodeSpecialChars('Test "Example"')).toBe('Test &quot;Example&quot;');
  });

  test("should encode ' character to &#39;", () => {
    expect(encodeSpecialChars("Test 'Example'")).toBe('Test &#39;Example&#39;');
  });

  test('should encode multiple special characters in a string', () => {
    expect(encodeSpecialChars('Test & <script>"alert"</script>')).toBe(
      'Test &amp; &lt;script&gt;&quot;alert&quot;&lt;/script&gt;'
    );
  });

  test('should return the original string if no special characters are present', () => {
    expect(encodeSpecialChars('Test Example')).toBe('Test Example');
  });

  test('should handle empty string', () => {
    expect(encodeSpecialChars('')).toBe('');
  });

  test('should handle string with only special characters', () => {
    expect(encodeSpecialChars('&<>"\'')).toBe('&amp;&lt;&gt;&quot;&#39;');
  });

  test('should handle default case in switch statement', () => {
    // This test is to ensure 100% branch coverage
    // We're mocking the replace method to force the default case
    const originalReplace = String.prototype.replace;

    // Mock implementation that will trigger the default case
    String.prototype.replace = function (pattern, replacer) {
      if (typeof replacer === 'function') {
        // Call the replacer with a character that's not in the pattern
        // but we'll pretend it matched
        return replacer.call(this, 'X');
      }
      return originalReplace.call(this, pattern, replacer);
    };

    try {
      // This should trigger the default case in the switch statement
      const result = encodeSpecialChars('test');
      expect(result).toBe('X');
    } finally {
      // Restore the original replace method
      String.prototype.replace = originalReplace;
    }
  });
});
