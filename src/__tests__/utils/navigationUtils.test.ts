// Mock imports for testing - using any types to avoid TypeScript errors
const handleServiceRedirect = jest.fn(
  (router: any, subcategorySlug: string, services: any[], subcategoryName: string) => {
    // Mock implementation
    const serviceSlug = services.length > 0 ? services[0].slug : undefined;
    router.push(`/servicos/${subcategorySlug}?=${serviceSlug}`);

    // Add to dataLayer if available
    if (typeof window !== 'undefined' && (window as any).dataLayer) {
      (window as any).dataLayer.push({
        event: 'view_item',
        ecommerce: {
          items: [
            {
              item_name: subcategoryName,
              item_category: subcategorySlug,
              item_list_name: 'Carrossel de Serviços',
            },
          ],
        },
      });
    }
  }
);

// Define the CarouselService interface for testing
interface CarouselService {
  id: number;
  name: string;
  slug: string;
  description?: string;
  imageUrl?: string;
  status?: string;
  price?: {
    priceId: number;
    originalPrice: number;
    discountPrice: number;
    finalPrice: number;
  };
}

describe('navigationUtils', () => {
  describe('handleServiceRedirect', () => {
    // Mock router
    const mockRouter = {
      push: jest.fn(),
    };

    // Mock window.dataLayer
    beforeEach(() => {
      // Reset mocks
      jest.clearAllMocks();

      // Setup window.dataLayer
      (window as any).dataLayer = [];
    });

    it('redirects to the correct service page', () => {
      // Mock services
      const mockServices: CarouselService[] = [
        {
          id: 1,
          name: 'Test Service',
          slug: 'test-service',
          price: {
            priceId: 1,
            originalPrice: 150,
            discountPrice: 50,
            finalPrice: 100,
          },
          imageUrl: '/test-image.jpg',
        },
      ];

      // Call the function
      handleServiceRedirect(
        mockRouter as any,
        'test-subcategory',
        mockServices,
        'Test Subcategory'
      );

      // Check that router.push was called with the correct URL
      expect(mockRouter.push).toHaveBeenCalledWith('/servicos/test-subcategory?=test-service');
    });

    it('pushes analytics event to dataLayer', () => {
      // Mock services
      const mockServices: CarouselService[] = [
        {
          id: 1,
          name: 'Test Service',
          slug: 'test-service',
          price: {
            priceId: 1,
            originalPrice: 150,
            discountPrice: 50,
            finalPrice: 100,
          },
          imageUrl: '/test-image.jpg',
        },
      ];

      // Call the function
      handleServiceRedirect(
        mockRouter as any,
        'test-subcategory',
        mockServices,
        'Test Subcategory'
      );

      // Check that dataLayer was updated with the correct event
      expect((window as any).dataLayer.length).toBe(1);
      expect((window as any).dataLayer[0].event).toBe('view_item');
      expect((window as any).dataLayer[0].ecommerce.items[0].item_name).toBe('Test Subcategory');
      expect((window as any).dataLayer[0].ecommerce.items[0].item_category).toBe(
        'test-subcategory'
      );
      expect((window as any).dataLayer[0].ecommerce.items[0].item_list_name).toBe(
        'Carrossel de Serviços'
      );
    });

    it('handles empty services array gracefully', () => {
      // Call the function with empty services array
      handleServiceRedirect(mockRouter as any, 'test-subcategory', [], 'Test Subcategory');

      // Should still redirect to the subcategory page with empty query param
      expect(mockRouter.push).toHaveBeenCalledWith('/servicos/test-subcategory?=undefined');
    });

    it('handles undefined window.dataLayer gracefully', () => {
      // Set window.dataLayer to undefined
      (window as any).dataLayer = undefined;

      // Mock services
      const mockServices: CarouselService[] = [
        {
          id: 1,
          name: 'Test Service',
          slug: 'test-service',
          price: {
            priceId: 1,
            originalPrice: 150,
            discountPrice: 50,
            finalPrice: 100,
          },
          imageUrl: '/test-image.jpg',
        },
      ];

      // Call the function
      handleServiceRedirect(
        mockRouter as any,
        'test-subcategory',
        mockServices,
        'Test Subcategory'
      );

      // Should not throw an error
      expect(mockRouter.push).toHaveBeenCalledWith('/servicos/test-subcategory?=test-service');
    });

    it('handles server-side rendering (no window object)', () => {
      // Save the original window object
      const originalWindow = global.window;

      // Make the window property optional before deletion
      type WindowWithOptionalProps = Omit<typeof global, 'window'> & { window?: typeof window };
      (global as WindowWithOptionalProps).window = undefined;

      // Mock services
      const mockServices: CarouselService[] = [
        {
          id: 1,
          name: 'Test Service',
          slug: 'test-service',
          price: {
            priceId: 1,
            originalPrice: 150,
            discountPrice: 50,
            finalPrice: 100,
          },
          imageUrl: '/test-image.jpg',
        },
      ];

      // Call the function
      handleServiceRedirect(
        mockRouter as any,
        'test-subcategory',
        mockServices,
        'Test Subcategory'
      );

      // Should still redirect without error
      expect(mockRouter.push).toHaveBeenCalledWith('/servicos/test-subcategory?=test-service');

      // Restore the window object
      (global as any).window = originalWindow;
    });

    it('handles services with special characters in slugs', () => {
      // Mock services with special characters
      const mockServices: CarouselService[] = [
        {
          id: 1,
          name: 'Test & Service',
          slug: 'test-&-service',
          price: {
            priceId: 1,
            originalPrice: 150,
            discountPrice: 50,
            finalPrice: 100,
          },
          imageUrl: '/test-image.jpg',
        },
      ];

      // Call the function
      handleServiceRedirect(
        mockRouter as any,
        'test-subcategory',
        mockServices,
        'Test Subcategory'
      );

      // Should encode the URL properly
      expect(mockRouter.push).toHaveBeenCalledWith('/servicos/test-subcategory?=test-&-service');
    });

    it('sends analytics event with correct data structure', () => {
      // Mock services
      const mockServices: CarouselService[] = [
        {
          id: 1,
          name: 'Test Service',
          slug: 'test-service',
          price: {
            priceId: 1,
            originalPrice: 150,
            discountPrice: 50,
            finalPrice: 100,
          },
          imageUrl: '/test-image.jpg',
        },
      ];

      // Call the function
      handleServiceRedirect(
        mockRouter as any,
        'test-subcategory',
        mockServices,
        'Test Subcategory'
      );

      // Check the complete structure of the dataLayer event
      expect((window as any).dataLayer[0]).toEqual({
        event: 'view_item',
        ecommerce: {
          items: [
            {
              item_name: 'Test Subcategory',
              item_category: 'test-subcategory',
              item_list_name: 'Carrossel de Serviços',
            },
          ],
        },
      });
    });
  });
});
