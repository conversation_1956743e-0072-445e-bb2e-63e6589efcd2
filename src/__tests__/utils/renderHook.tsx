import { renderHook as defaultRenderHook, RenderHookOptions } from '@testing-library/react';
import { ReactNode } from 'react';

/**
 * Wrapper personalizado para testes de hooks
 * Permite adicionar providers ou context necessários para os hooks
 */
export function TestWrapper({ children }: { children: ReactNode }) {
  // Você pode adicionar providers aqui conforme necessário
  return <>{children}</>;
}

/**
 * Função utilitária para renderizar hooks com providers necessários
 * @param callback Hook a ser renderizado
 * @param options Opções adicionais para renderização
 * @returns Resultado do renderHook
 */
export function renderHook<TResult, TProps>(
  callback: (props: TProps) => TResult,
  options?: Omit<RenderHookOptions<TProps>, 'wrapper'>
) {
  return defaultRenderHook(callback, {
    wrapper: TestWrapper,
    ...options,
  });
}
