import {
  checkApplicationHealth,
  checkDeepHealth,
  getHealthCheckTimestamp,
  getHealthStatusText,
} from '@/src/app/_utils/healthCheck';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('healthCheck utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('checkApplicationHealth', () => {
    test('returns true when API responds with success', async () => {
      mockedAxios.get.mockResolvedValueOnce({ status: 200, data: {} });

      const result = await checkApplicationHealth();
      expect(result).toBe(true);
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/health', {
        headers: { 'Cache-Control': 'no-cache' },
      });
    });

    test('uses provided baseUrl when available', async () => {
      mockedAxios.get.mockResolvedValueOnce({ status: 200, data: {} });

      const result = await checkApplicationHealth('https://example.com');
      expect(result).toBe(true);
      expect(mockedAxios.get).toHaveBeenCalledWith('https://example.com/api/health', {
        headers: { 'Cache-Control': 'no-cache' },
      });
    });

    test('returns false when API responds with error', async () => {
      mockedAxios.get.mockRejectedValueOnce({
        response: { status: 500 },
      });

      const result = await checkApplicationHealth();
      expect(result).toBe(false);
    });

    test('returns false when request throws an error', async () => {
      // Spy on console.error to suppress output
      jest.spyOn(console, 'error').mockImplementation(() => {});

      mockedAxios.get.mockRejectedValueOnce(new Error('Network error'));

      const result = await checkApplicationHealth();
      expect(result).toBe(false);
    });
  });

  describe('checkDeepHealth', () => {
    test('returns healthy: true when API responds with status ok', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: { status: 'ok', services: { db: 'ok' } },
      });

      const result = await checkDeepHealth();
      expect(result).toEqual({
        healthy: true,
        details: { status: 'ok', services: { db: 'ok' } },
      });
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/health/deep', {
        headers: { 'Cache-Control': 'no-cache' },
      });
    });

    test('uses provided baseUrl when available', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: { status: 'ok' },
      });

      const result = await checkDeepHealth('https://example.com');
      expect(result.healthy).toBe(true);
      expect(mockedAxios.get).toHaveBeenCalledWith('https://example.com/api/health/deep', {
        headers: { 'Cache-Control': 'no-cache' },
      });
    });

    test('returns healthy: false when API responds with non-ok status', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: { status: 'error', services: { db: 'error' } },
      });

      const result = await checkDeepHealth();
      expect(result).toEqual({
        healthy: false,
        details: { status: 'error', services: { db: 'error' } },
      });
    });

    test('returns healthy: false when API response is not ok', async () => {
      mockedAxios.get.mockRejectedValueOnce({
        response: { status: 500 },
      });

      const result = await checkDeepHealth();
      expect(result).toEqual({ healthy: false });
    });

    test('returns healthy: false when request throws an error', async () => {
      // Spy on console.error to suppress output
      jest.spyOn(console, 'error').mockImplementation(() => {});

      mockedAxios.get.mockRejectedValueOnce(new Error('Network error'));

      const result = await checkDeepHealth();
      expect(result).toEqual({ healthy: false });
    });
  });

  describe('getHealthStatusText', () => {
    test('returns "Operational" when isHealthy is true', () => {
      expect(getHealthStatusText(true)).toBe('Operational');
    });

    test('returns "Experiencing Issues" when isHealthy is false', () => {
      expect(getHealthStatusText(false)).toBe('Experiencing Issues');
    });
  });

  describe('getHealthCheckTimestamp', () => {
    test('returns timestamp in ISO format', () => {
      // Mock Date to have a fixed value
      const mockDate = new Date('2023-01-01T12:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate);

      expect(getHealthCheckTimestamp()).toBe('2023-01-01T12:00:00.000Z');

      // Restore Date
      jest.restoreAllMocks();
    });
  });
});
