import {
  capitalizeArrayItems,
  capitalizeDetailedItems,
  capitalizeFirstLetter,
  capitalizeSentences,
} from '@/src/app/_utils/stringUtils';

describe('capitalizeFirstLetter', () => {
  test('capitalizes the first letter of a string', () => {
    expect(capitalizeFirstLetter('hello world')).toBe('Hello world');
  });

  test('handles empty string', () => {
    expect(capitalizeFirstLetter('')).toBe('');
  });

  test('handles string with only whitespace', () => {
    expect(capitalizeFirstLetter('   ')).toBe('   ');
  });

  test('trims leading whitespace before capitalizing', () => {
    expect(capitalizeFirstLetter('  hello')).toBe('Hello');
  });

  test('returns non-string inputs unchanged', () => {
    // @ts-ignore - Testing edge case with wrong type
    expect(capitalizeFirstLetter(null)).toBeNull();
    // @ts-ignore - Testing edge case with wrong type
    expect(capitalizeFirstLetter(undefined)).toBeUndefined();
  });

  test('handles single character string', () => {
    expect(capitalizeFirstLetter('a')).toBe('A');
  });
});

describe('capitalizeArrayItems', () => {
  test('capitalizes the first letter of each string in an array', () => {
    expect(capitalizeArrayItems(['hello', 'world'])).toEqual(['Hello', 'World']);
  });

  test('handles empty array', () => {
    expect(capitalizeArrayItems([])).toEqual([]);
  });

  test('preserves leading whitespace in array items', () => {
    expect(capitalizeArrayItems(['  hello', '    world'])).toEqual(['  Hello', '    World']);
  });

  test('handles array with empty strings', () => {
    expect(capitalizeArrayItems(['', 'hello', ''])).toEqual(['', 'Hello', '']);
  });

  test('handles array with whitespace-only strings', () => {
    expect(capitalizeArrayItems(['   ', 'hello'])).toEqual(['   ', 'Hello']);
  });

  test('returns non-array inputs unchanged', () => {
    // @ts-ignore - Testing edge case with wrong type
    expect(capitalizeArrayItems(null)).toBeNull();
    // @ts-ignore - Testing edge case with wrong type
    expect(capitalizeArrayItems(undefined)).toBeUndefined();
  });

  test('handles non-string items in array', () => {
    // @ts-ignore - Testing edge case with wrong type
    expect(capitalizeArrayItems(['hello', null, 123, 'world'])).toEqual([
      'Hello',
      null,
      123,
      'World',
    ]);
  });
});

describe('capitalizeSentences', () => {
  test('capitalizes the first letter of each sentence', () => {
    expect(capitalizeSentences('hello. world! how are you? fine.')).toBe(
      'Hello. World! How are you? Fine.'
    );
  });

  test('handles empty string', () => {
    expect(capitalizeSentences('')).toBe('');
  });

  test('handles string with only one sentence', () => {
    // It actually does capitalize the first letter even for single sentences
    expect(capitalizeSentences('hello world')).toBe('Hello world');
  });

  test('capitalizes first letter if not preceded by punctuation', () => {
    // It actually does capitalize the first letter of the string
    expect(capitalizeSentences('hello')).toBe('Hello');
  });

  test('handles string with whitespace after punctuation', () => {
    // It capitalizes the first letter of the string too
    expect(capitalizeSentences('hello.   world')).toBe('Hello.   World');
  });

  test('returns non-string inputs unchanged', () => {
    // @ts-ignore - Testing edge case with wrong type
    expect(capitalizeSentences(null)).toBeNull();
    // @ts-ignore - Testing edge case with wrong type
    expect(capitalizeSentences(undefined)).toBeUndefined();
  });
});

describe('capitalizeDetailedItems', () => {
  test('capitalizes first letter of each item and all sentences within', () => {
    expect(capitalizeDetailedItems(['hello. world!', 'how are you? fine.'])).toEqual([
      'Hello. World!',
      'How are you? Fine.',
    ]);
  });

  test('handles empty array', () => {
    expect(capitalizeDetailedItems([])).toEqual([]);
  });

  test('preserves leading whitespace in array items', () => {
    expect(capitalizeDetailedItems(['  hello. world!', '    how are you?'])).toEqual([
      '  Hello. World!',
      '    How are you?',
    ]);
  });

  test('handles array with empty strings', () => {
    // The function actually capitalizes 'world' too
    expect(capitalizeDetailedItems(['', 'hello. world', ''])).toEqual(['', 'Hello. World', '']);
  });

  test('handles array with whitespace-only strings', () => {
    // The function actually capitalizes 'world' too
    expect(capitalizeDetailedItems(['   ', 'hello. world'])).toEqual(['   ', 'Hello. World']);
  });

  test('returns non-array inputs unchanged', () => {
    // @ts-ignore - Testing edge case with wrong type
    expect(capitalizeDetailedItems(null)).toBeNull();
    // @ts-ignore - Testing edge case with wrong type
    expect(capitalizeDetailedItems(undefined)).toBeUndefined();
  });

  test('handles non-string items in array', () => {
    // @ts-ignore - Testing edge case with wrong type
    // The function actually capitalizes 'world' too
    expect(capitalizeDetailedItems(['hello. world', null, 123])).toEqual([
      'Hello. World',
      null,
      123,
    ]);
  });
});
