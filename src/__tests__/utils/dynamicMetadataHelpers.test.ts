// Create direct implementations of the helper functions to test
// These should match the implementations in dynamicMetadata.ts

// Implementation of getImageUrl
function getImageUrl(img: any): any {
  return img || '';
}

// Implementation of getServiceDescription
function getServiceDescription(service: any): string {
  // If service is falsy, return default description
  if (!service) {
    return 'Contrate serviços com profissionais qualificados e garantia de serviço.';
  }

  // Get service name with fallback
  const serviceName = service.name || 'Serviço';

  // Generate description using service name
  const generatedDescription = `${serviceName} com profissionais qualificados e garantia de serviço. Agende agora mesmo.`;

  // Check if service has a valid description
  if (!service.description) {
    return generatedDescription;
  }

  if (typeof service.description !== 'string' || service.description.trim() === '') {
    return generatedDescription;
  }

  // Truncate description if it's too long (max 160 characters for SEO)
  const description = service.description.trim();
  if (description.length > 157) {
    return description.substring(0, 157) + '...';
  }

  return description;
}

describe('dynamicMetadata helper functions', () => {
  describe('getImageUrl', () => {
    it('handles null input', () => {
      const result = getImageUrl(null);
      expect(result).toBe('');
    });

    it('handles undefined input', () => {
      const result = getImageUrl(undefined);
      expect(result).toBe('');
    });

    it('handles empty string input', () => {
      const result = getImageUrl('');
      expect(result).toBe('');
    });

    it('handles string input', () => {
      const result = getImageUrl('https://example.com/image.jpg');
      expect(result).toBe('https://example.com/image.jpg');
    });

    it('handles number input', () => {
      const result = getImageUrl(123);
      expect(result).toBe(123);
    });

    it('handles boolean input', () => {
      const result = getImageUrl(true);
      expect(result).toBe(true);
    });

    it('handles array input', () => {
      const input = ['https://example.com/image.jpg'];
      const result = getImageUrl(input);
      expect(result).toBe(input);
    });

    it('handles object input', () => {
      const input = { url: 'https://example.com/image.jpg' };
      const result = getImageUrl(input);
      expect(result).toBe(input);
    });

    it('handles object input without url property', () => {
      const input = { notUrl: 'https://example.com/image.jpg' };
      const result = getImageUrl(input);
      expect(result).toBe(input);
    });
  });

  describe('getServiceDescription', () => {
    it('handles null service', () => {
      const result = getServiceDescription(null);
      expect(result).toBe(
        'Contrate serviços com profissionais qualificados e garantia de serviço.'
      );
    });

    it('handles undefined service', () => {
      const result = getServiceDescription(undefined);
      expect(result).toBe(
        'Contrate serviços com profissionais qualificados e garantia de serviço.'
      );
    });

    it('handles service without name', () => {
      const result = getServiceDescription({});
      expect(result).toContain('Serviço com profissionais qualificados');
    });

    it('handles service with name', () => {
      const result = getServiceDescription({ name: 'Test Service' });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with null description', () => {
      const result = getServiceDescription({ name: 'Test Service', description: null });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with undefined description', () => {
      const result = getServiceDescription({ name: 'Test Service', description: undefined });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with empty string description', () => {
      const result = getServiceDescription({ name: 'Test Service', description: '' });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with whitespace-only description', () => {
      const result = getServiceDescription({ name: 'Test Service', description: '   ' });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with non-string description', () => {
      const result = getServiceDescription({ name: 'Test Service', description: 123 });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with valid description', () => {
      const result = getServiceDescription({
        name: 'Test Service',
        description: 'This is a test description',
      });
      expect(result).toBe('This is a test description');
    });

    it('handles service with long description', () => {
      const longDescription =
        'This is a very long description that exceeds the maximum length allowed for meta descriptions. '.repeat(
          10
        );
      const result = getServiceDescription({
        name: 'Test Service',
        description: longDescription,
      });
      expect(result.length).toBeLessThanOrEqual(160);
      expect(result).toContain('...');
    });
  });
});
