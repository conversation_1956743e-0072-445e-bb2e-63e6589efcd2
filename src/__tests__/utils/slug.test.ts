import { createSlug, generateServiceSlug } from '@/src/app/_utils/slug';

describe('slug utils', () => {
  describe('createSlug', () => {
    test('converts text to lowercase', () => {
      expect(createSlug('TEXT')).toBe('text');
      expect(createSlug('Text With MIXED Case')).toBe('text-with-mixed-case');
    });

    test('replaces spaces with hyphens', () => {
      expect(createSlug('hello world')).toBe('hello-world');
      expect(createSlug('multiple   spaces  here')).toBe('multiple-spaces-here');
    });

    test('removes non-alphanumeric characters', () => {
      expect(createSlug('special!@#$%^&*characters')).toBe('specialcharacters');
      expect(createSlug('special (chars) here')).toBe('special-chars-here');
    });

    test('removes diacritics (accents)', () => {
      expect(createSlug('café')).toBe('cafe');
      expect(createSlug('résumé')).toBe('resume');
      expect(createSlug('São Paulo')).toBe('sao-paulo');
    });

    test('removes leading and trailing hyphens', () => {
      expect(createSlug('-leading-hyphen')).toBe('leading-hyphen');
      expect(createSlug('trailing-hyphen-')).toBe('trailing-hyphen');
      expect(createSlug('--both--')).toBe('both');
    });

    test('handles complex text scenarios', () => {
      expect(createSlug('This is a !complex! example with éáçãó accents & spaces!')).toBe(
        'this-is-a-complex-example-with-eacao-accents-spaces'
      );
    });

    test('returns empty string for empty input', () => {
      expect(createSlug('')).toBe('');
    });
  });

  describe('generateServiceSlug', () => {
    test('combines service name and provider name', () => {
      expect(generateServiceSlug('Cleaning', 'John Doe')).toBe('cleaning-john-doe');
    });

    test('handles special characters in both parts', () => {
      expect(generateServiceSlug('House Cleaning!', 'Mary & Co.')).toBe('house-cleaning-mary-co');
    });

    test('handles accented characters', () => {
      expect(generateServiceSlug('Limpeza', 'João Silva')).toBe('limpeza-joao-silva');
    });

    test('handles empty strings', () => {
      expect(generateServiceSlug('', '')).toBe('');
      expect(generateServiceSlug('Service', '')).toBe('service');
      expect(generateServiceSlug('', 'Provider')).toBe('provider');
    });
  });
});
