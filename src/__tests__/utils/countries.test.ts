import { countries } from '@/src/app/_utils/countries';

describe('countries', () => {
  test('countries array is defined and not empty', () => {
    expect(countries).toBeDefined();
    expect(Array.isArray(countries)).toBe(true);
    expect(countries.length).toBeGreaterThan(0);
  });

  test('each country has the correct structure', () => {
    countries.forEach((country) => {
      expect(country).toHaveProperty('code');
      expect(country).toHaveProperty('iso');
      expect(country).toHaveProperty('name');

      expect(typeof country.code).toBe('string');
      expect(typeof country.iso).toBe('string');
      expect(typeof country.name).toBe('string');
    });
  });

  test('country codes start with "+" character', () => {
    countries.forEach((country) => {
      expect(country.code.startsWith('+')).toBe(true);
    });
  });

  test('country ISO codes are exactly 2 characters', () => {
    countries.forEach((country) => {
      expect(country.iso.length).toBe(2);
      expect(country.iso).toMatch(/^[A-Z]{2}$/);
    });
  });

  test('Brazil is included in the countries list', () => {
    const brazil = countries.find((country) => country.iso === 'BR');
    expect(brazil).toBeDefined();
    expect(brazil).toEqual({
      code: '+55',
      iso: 'BR',
      name: 'Brasil',
    });
  });

  test('country names are in Portuguese', () => {
    const us = countries.find((country) => country.iso === 'US');
    const germany = countries.find((country) => country.iso === 'DE');

    expect(us?.name).toBe('Estados Unidos');
    expect(germany?.name).toBe('Alemanha');
  });

  test('no duplicate ISO codes exist', () => {
    const isoCodes = countries.map((country) => country.iso);
    const uniqueIsoCodes = [...new Set(isoCodes)];

    expect(isoCodes.length).toBe(uniqueIsoCodes.length);
  });
});
