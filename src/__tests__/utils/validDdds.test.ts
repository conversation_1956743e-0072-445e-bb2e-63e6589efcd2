import {
  getValidBrazilianDDD,
  isValidBrazilianDDD,
  validBrazilianDDDs,
} from '@/src/app/_utils/validDdds';

describe('validDdds', () => {
  describe('validBrazilianDDDs array', () => {
    test('contains all expected DDDs', () => {
      expect(validBrazilianDDDs).toBeDefined();
      expect(Array.isArray(validBrazilianDDDs)).toBe(true);
      expect(validBrazilianDDDs.length).toBeGreaterThan(0);

      // Check for specific DDDs from different regions
      expect(validBrazilianDDDs).toContain('11'); // São Paulo
      expect(validBrazilianDDDs).toContain('21'); // Rio de Janeiro
      expect(validBrazilianDDDs).toContain('31'); // Minas Gerais
      expect(validBrazilianDDDs).toContain('41'); // Paraná
      expect(validBrazilianDDDs).toContain('51'); // Rio Grande do Sul
      expect(validBrazilianDDDs).toContain('61'); // Brasília
      expect(validBrazilianDDDs).toContain('71'); // Bahia
      expect(validBrazilianDDDs).toContain('81'); // Pernambuco
      expect(validBrazilianDDDs).toContain('91'); // Pará
    });

    test('all entries are 2-digit strings', () => {
      validBrazilianDDDs.forEach((ddd) => {
        expect(typeof ddd).toBe('string');
        expect(ddd.length).toBe(2);
        expect(ddd).toMatch(/^\d{2}$/);
      });
    });

    test('does not contain invalid DDDs', () => {
      // Non-existent Brazilian DDDs
      expect(validBrazilianDDDs).not.toContain('20');
      expect(validBrazilianDDDs).not.toContain('23');
      expect(validBrazilianDDDs).not.toContain('25');
      expect(validBrazilianDDDs).not.toContain('26');
      expect(validBrazilianDDDs).not.toContain('29');
      expect(validBrazilianDDDs).not.toContain('30');
      expect(validBrazilianDDDs).not.toContain('36');
      expect(validBrazilianDDDs).not.toContain('39');
      expect(validBrazilianDDDs).not.toContain('50');
      expect(validBrazilianDDDs).not.toContain('52');
      expect(validBrazilianDDDs).not.toContain('56');
      expect(validBrazilianDDDs).not.toContain('58');
      expect(validBrazilianDDDs).not.toContain('59');
      expect(validBrazilianDDDs).not.toContain('70');
      expect(validBrazilianDDDs).not.toContain('76');
      expect(validBrazilianDDDs).not.toContain('78');
      expect(validBrazilianDDDs).not.toContain('90');
    });
  });

  describe('isValidBrazilianDDD', () => {
    test('returns true for valid DDDs', () => {
      expect(isValidBrazilianDDD('11')).toBe(true);
      expect(isValidBrazilianDDD('21')).toBe(true);
      expect(isValidBrazilianDDD('51')).toBe(true);
      expect(isValidBrazilianDDD('85')).toBe(true);
    });

    test('returns false for invalid DDDs', () => {
      expect(isValidBrazilianDDD('00')).toBe(false);
      expect(isValidBrazilianDDD('01')).toBe(false);
      expect(isValidBrazilianDDD('10')).toBe(false);
      expect(isValidBrazilianDDD('20')).toBe(false);
      expect(isValidBrazilianDDD('100')).toBe(false);
    });

    test('returns false for non-numeric inputs', () => {
      expect(isValidBrazilianDDD('aa')).toBe(false);
      expect(isValidBrazilianDDD('1a')).toBe(false);
      expect(isValidBrazilianDDD('a1')).toBe(false);
    });

    test('handles empty string', () => {
      expect(isValidBrazilianDDD('')).toBe(false);
    });
  });

  describe('getValidBrazilianDDD', () => {
    test('returns the same DDD if valid', () => {
      expect(getValidBrazilianDDD('11')).toBe('11');
      expect(getValidBrazilianDDD('21')).toBe('21');
      expect(getValidBrazilianDDD('71')).toBe('71');
    });

    test('returns default DDD (11) if the provided DDD is invalid', () => {
      expect(getValidBrazilianDDD('00')).toBe('11');
      expect(getValidBrazilianDDD('99999')).toBe('11');
      expect(getValidBrazilianDDD('abc')).toBe('11');
      expect(getValidBrazilianDDD('')).toBe('11');
    });

    test('returns custom default DDD if provided', () => {
      expect(getValidBrazilianDDD('00', '21')).toBe('21');
      expect(getValidBrazilianDDD('invalid', '51')).toBe('51');
      expect(getValidBrazilianDDD('', '41')).toBe('41');
    });

    test('validates custom default DDD', () => {
      // If both the DDD and the custom default are invalid, it should still return the custom default
      expect(getValidBrazilianDDD('00', '00')).toBe('00');
    });
  });
});
