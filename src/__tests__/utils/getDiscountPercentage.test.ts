import { getDiscountPercentage } from '@/src/app/_utils/getDiscountPercentage';

describe('getDiscountPercentage', () => {
  test('should calculate 0% discount when prices are equal', () => {
    expect(getDiscountPercentage(100, 100)).toBe(0);
  });

  test('should calculate 10% discount correctly', () => {
    expect(getDiscountPercentage(100, 90)).toBe(10);
  });

  test('should calculate 25% discount correctly', () => {
    expect(getDiscountPercentage(100, 75)).toBe(25);
  });

  test('should calculate 50% discount correctly', () => {
    expect(getDiscountPercentage(100, 50)).toBe(50);
  });

  test('should round 33.33% discount to 33%', () => {
    expect(getDiscountPercentage(100, 66.67)).toBe(33);
  });

  test('should round 66.66% discount to 67%', () => {
    expect(getDiscountPercentage(100, 33.34)).toBe(67);
  });

  test('should handle 100% discount', () => {
    expect(getDiscountPercentage(100, 0)).toBe(100);
  });

  test('should handle non-integer original prices', () => {
    expect(getDiscountPercentage(99.99, 49.99)).toBe(50);
  });

  test('should handle discount on already discounted price', () => {
    // 10% off the already discounted price
    expect(getDiscountPercentage(90, 81)).toBe(10);
  });

  test('should handle small price values', () => {
    expect(getDiscountPercentage(1, 0.5)).toBe(50);
  });
});
