import { Category, Service, Subcategory } from '@/src/app/_context/ServiceContext';
import {
  convertToCarouselCategories,
  convertToCarouselCategory,
  convertToCarouselService,
  convertToCarouselSubcategory,
  convertToServiceType,
  convertToServiceTypes,
} from '@/src/app/_utils/serviceUtils';

describe('serviceUtils', () => {
  // Mock service with optional properties missing
  const mockService: Service = {
    id: 1,
    name: 'Test Service',
    slug: 'test-service',
    description: 'Test description',
    status: 'active',
    price: {
      priceId: 1,
      originalPrice: 100,
      discountPrice: 10,
      finalPrice: 90,
    },
    provider: {
      id: 1,
      name: 'Test Provider',
      providerUrl: 'https://example.com',
    },
    availableIn: ['São Paulo'],
    details: ['Detail 1'],
    keywords: ['keyword1'],
    categoryId: 1,
    categoryName: 'Test Category',
    categorySlug: 'test-category',
    subcategoryId: 1,
    subcategoryName: 'Test Subcategory',
    subcategorySlug: 'test-subcategory',
  };

  const mockSubcategory: Subcategory = {
    id: 1,
    name: 'Test Subcategory',
    slug: 'test-subcategory',
    services: [mockService],
  };

  const mockCategory: Category = {
    id: 1,
    name: 'Test Category',
    slug: 'test-category',
    subcategories: [mockSubcategory],
  };

  describe('convertToCarouselService', () => {
    it('should convert Service to CarouselService with all required properties', () => {
      const result = convertToCarouselService(mockService);

      expect(result.id).toBe(mockService.id);
      expect(result.name).toBe(mockService.name);
      expect(result.slug).toBe(mockService.slug);
      expect(result.description).toBe(mockService.description);
      expect(result.status).toBe(mockService.status);
      expect(result.price).toEqual(mockService.price);

      // Check that optional properties are handled correctly
      expect(result.provider?.imageUrl).toBe('');
      expect(result.provider?.description).toBe('');
    });

    it('should handle service with imageUrl', () => {
      const serviceWithImage = {
        ...mockService,
        imageUrl: 'https://example.com/image.jpg',
        provider: {
          ...mockService.provider,
          imageUrl: 'https://example.com/provider.jpg',
          description: 'Provider description',
        },
      };

      const result = convertToCarouselService(serviceWithImage);

      expect(result.imageUrl).toBe(serviceWithImage.imageUrl);
      expect(result.provider?.imageUrl).toBe(serviceWithImage.provider.imageUrl);
      expect(result.provider?.description).toBe(serviceWithImage.provider.description);
    });
  });

  describe('convertToCarouselSubcategory', () => {
    it('should convert Subcategory to CarouselSubcategory', () => {
      const result = convertToCarouselSubcategory(mockSubcategory);

      expect(result.id).toBe(mockSubcategory.id);
      expect(result.name).toBe(mockSubcategory.name);
      expect(result.slug).toBe(mockSubcategory.slug);
      expect(result.services.length).toBe(mockSubcategory.services.length);

      // Check that services are converted correctly
      const convertedService = result.services[0];
      expect(convertedService.id).toBe(mockService.id);
      expect(convertedService.provider?.imageUrl).toBe('');
    });
  });

  describe('convertToCarouselCategory', () => {
    it('should convert Category to CarouselCategory', () => {
      const result = convertToCarouselCategory(mockCategory);

      expect(result.id).toBe(mockCategory.id);
      expect(result.name).toBe(mockCategory.name);
      expect(result.slug).toBe(mockCategory.slug);
      expect(result.subcategories.length).toBe(mockCategory.subcategories.length);

      // Check that subcategories are converted correctly
      const convertedSubcategory = result.subcategories[0];
      expect(convertedSubcategory.id).toBe(mockSubcategory.id);
      expect(convertedSubcategory.services.length).toBe(mockSubcategory.services.length);
    });
  });

  describe('convertToCarouselCategories', () => {
    it('should convert an array of Categories to an array of CarouselCategories', () => {
      const categories: Category[] = [mockCategory];
      const result = convertToCarouselCategories(categories);

      expect(result.length).toBe(categories.length);
      expect(result[0].id).toBe(categories[0].id);
      expect(result[0].subcategories.length).toBe(categories[0].subcategories.length);

      // Check that nested objects are converted correctly
      const convertedSubcategory = result[0].subcategories[0];
      const convertedService = convertedSubcategory.services[0];

      expect(convertedService.id).toBe(mockService.id);
      expect(convertedService.provider?.imageUrl).toBe('');
    });

    it('should handle empty array', () => {
      const result = convertToCarouselCategories([]);
      expect(result).toEqual([]);
    });
  });

  describe('convertToServiceType', () => {
    it('should convert Service to ServiceType with all properties', () => {
      const result = convertToServiceType(mockService);

      expect(result.id).toBe(mockService.id);
      expect(result.name).toBe(mockService.name);
      expect(result.slug).toBe(mockService.slug);
      expect(result.description).toBe(mockService.description);
      expect(result.status).toBe(mockService.status);
      expect(result.price).toEqual(mockService.price);
      // Provider object is transformed, so we need to check individual properties
      expect(result.provider.id).toEqual(mockService.provider.id);
      expect(result.provider.name).toEqual(mockService.provider.name);
      expect(result.provider.providerUrl).toEqual(mockService.provider.providerUrl);
      expect(result.availableIn).toEqual(mockService.availableIn);
      expect(result.details).toEqual(mockService.details);
      expect(result.keywords).toEqual(mockService.keywords);
      expect(result.categoryName).toBe(mockService.categoryName);
      expect(result.categorySlug).toBe(mockService.categorySlug);
      expect(result.subcategoryName).toBe(mockService.subcategoryName);
      expect(result.subcategorySlug).toBe(mockService.subcategorySlug);
    });

    it('should handle service with optional fields', () => {
      const serviceWithOptionalFields = {
        ...mockService,
        imageUrl: 'https://example.com/image.jpg',
        serviceLimits: 'Service limits',
        preparations: 'Preparation instructions',
        termsConditionsUrl: 'https://example.com/terms',
      };

      const result = convertToServiceType(serviceWithOptionalFields);

      expect(result.imageUrl).toBe(serviceWithOptionalFields.imageUrl);
      expect(result.serviceLimits).toBe(serviceWithOptionalFields.serviceLimits);
      expect(result.preparations).toBe(serviceWithOptionalFields.preparations);
      expect(result.termsConditionsUrl).toBe(serviceWithOptionalFields.termsConditionsUrl);
    });

    it('should handle missing optional fields', () => {
      const result = convertToServiceType(mockService);

      expect(result.imageUrl).toBe('');
      expect(result.serviceLimits).toBe('');
      expect(result.preparations).toBe('');
      expect(result.termsConditionsUrl).toBe('');
    });
  });

  describe('convertToServiceTypes', () => {
    it('should convert an array of Services to an array of ServiceTypes', () => {
      const services: Service[] = [mockService];
      const result = convertToServiceTypes(services);

      expect(result.length).toBe(services.length);
      expect(result[0].id).toBe(services[0].id);
      expect(result[0].name).toBe(services[0].name);
      expect(result[0].slug).toBe(services[0].slug);
    });

    it('should handle empty array', () => {
      const result = convertToServiceTypes([]);
      expect(result).toEqual([]);
    });
  });
});
