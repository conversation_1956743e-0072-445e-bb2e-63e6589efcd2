import { createFallbackMetadata } from '@/src/app/_utils/dynamicMetadata';

// We need to export the function to test it
// Add this to dynamicMetadata.ts:
// export { createFallbackMetadata };

describe('createFallbackMetadata', () => {
  it('creates metadata with all parameters provided', () => {
    const result = createFallbackMetadata(
      'Test Service',
      'test-service',
      'subcategory',
      'https://example.com'
    );

    expect(result).toBeDefined();
    expect(result.title).toBe('Test Service | GetNinjas + Europ Assistance');
    expect(result.description).toBe(
      'Contrate Test Service com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
    );
    expect(result.keywords).toBe(
      'test, service, europ, assistance, getninjas, serviço, profissional'
    );
    expect(result.openGraph?.url).toBe('https://example.com/servicos/subcategory?=test-service');
    expect(result.alternates?.canonical).toBe(
      'https://example.com/servicos/subcategory?=test-service'
    );
  });

  it('handles empty service name', () => {
    const result = createFallbackMetadata('', 'test-service', 'subcategory', 'https://example.com');

    expect(result).toBeDefined();
    expect(result.title).toBe('Serviço | GetNinjas + Europ Assistance');
    expect(result.description).toBe(
      'Contrate Serviço com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
    );
  });

  it('handles null service name', () => {
    const result = createFallbackMetadata(
      null as any,
      'test-service',
      'subcategory',
      'https://example.com'
    );

    expect(result).toBeDefined();
    expect(result.title).toBe('Serviço | GetNinjas + Europ Assistance');
    expect(result.description).toBe(
      'Contrate Serviço com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
    );
  });

  it('handles empty slug', () => {
    const result = createFallbackMetadata('Test Service', '', 'subcategory', 'https://example.com');

    expect(result).toBeDefined();
    expect(result.keywords).toBe('servico, europ, assistance, getninjas, serviço, profissional');
    expect(result.openGraph?.url).toBe('https://example.com/servicos/subcategory?=servico');
  });

  it('handles null slug', () => {
    const result = createFallbackMetadata(
      'Test Service',
      null as any,
      'subcategory',
      'https://example.com'
    );

    expect(result).toBeDefined();
    expect(result.keywords).toBe('servico, europ, assistance, getninjas, serviço, profissional');
    expect(result.openGraph?.url).toBe('https://example.com/servicos/subcategory?=servico');
  });

  it('handles empty subcategory', () => {
    const result = createFallbackMetadata(
      'Test Service',
      'test-service',
      '',
      'https://example.com'
    );

    expect(result).toBeDefined();
    expect(result.openGraph?.url).toBe('https://example.com/servicos/categoria?=test-service');
  });

  it('handles null subcategory', () => {
    const result = createFallbackMetadata(
      'Test Service',
      'test-service',
      null as any,
      'https://example.com'
    );

    expect(result).toBeDefined();
    expect(result.openGraph?.url).toBe('https://example.com/servicos/categoria?=test-service');
  });

  it('handles empty baseUrl', () => {
    const result = createFallbackMetadata('Test Service', 'test-service', 'subcategory', '');

    expect(result).toBeDefined();
    expect(result.openGraph?.url).toBe(
      'https://europ.getninjas.com.br/servicos/subcategory?=test-service'
    );
  });

  it('handles null baseUrl', () => {
    const result = createFallbackMetadata(
      'Test Service',
      'test-service',
      'subcategory',
      null as any
    );

    expect(result).toBeDefined();
    expect(result.openGraph?.url).toBe(
      'https://europ.getninjas.com.br/servicos/subcategory?=test-service'
    );
  });

  it('handles all parameters being empty', () => {
    const result = createFallbackMetadata('', '', '', '');

    expect(result).toBeDefined();
    expect(result.title).toBe('Serviço | GetNinjas + Europ Assistance');
    expect(result.description).toBe(
      'Contrate Serviço com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
    );
    expect(result.keywords).toBe('servico, europ, assistance, getninjas, serviço, profissional');
    expect(result.openGraph?.url).toBe(
      'https://europ.getninjas.com.br/servicos/categoria?=servico'
    );
  });

  it('handles all parameters being null', () => {
    const result = createFallbackMetadata(null as any, null as any, null as any, null as any);

    expect(result).toBeDefined();
    expect(result.title).toBe('Serviço | GetNinjas + Europ Assistance');
    expect(result.description).toBe(
      'Contrate Serviço com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
    );
    expect(result.keywords).toBe('servico, europ, assistance, getninjas, serviço, profissional');
    expect(result.openGraph?.url).toBe(
      'https://europ.getninjas.com.br/servicos/categoria?=servico'
    );
  });
});
