import {
  TIME_PERIODS,
  createDateWithTime,
  getDisabledDates,
  getMinSchedulingDate,
  getTwentyFourHoursLater,
  isAfternoonPeriodAvailable,
  isDateAtLeast24HoursAhead,
  isMorningPeriodAvailable,
} from '@/src/app/_utils/dateUtils';
import { addDays } from 'date-fns';

// Mock current date for consistent testing
const mockDate = new Date('2023-05-15T10:00:00Z');

describe('dateUtils', () => {
  beforeEach(() => {
    // Reset Date mock before each test
    jest.useFakeTimers();
    jest.setSystemTime(mockDate);
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('TIME_PERIODS', () => {
    test('has correct morning period configuration', () => {
      expect(TIME_PERIODS.MORNING).toEqual({
        id: 'morning',
        label: 'Manhã',
        startHour: 8,
        endHour: 12,
        displayTime: '8h - 12h',
      });
    });

    test('has correct afternoon period configuration', () => {
      expect(TIME_PERIODS.AFTERNOON).toEqual({
        id: 'afternoon',
        label: 'Tarde',
        startHour: 13,
        endHour: 18,
        displayTime: '13h - 18h',
      });
    });
  });

  describe('createDateWithTime', () => {
    test('creates a date with specified time', () => {
      const baseDate = new Date('2023-05-15T00:00:00Z');
      const result = createDateWithTime(baseDate, 14, 30, 45, 500);

      expect(result.getHours()).toBe(14);
      expect(result.getMinutes()).toBe(30);
      expect(result.getSeconds()).toBe(45);
      expect(result.getMilliseconds()).toBe(500);
      expect(result.toDateString()).toBe(baseDate.toDateString());
    });

    test('creates a date with default values except hours', () => {
      const baseDate = new Date('2023-05-15T00:00:00Z');
      const result = createDateWithTime(baseDate, 9);

      expect(result.getHours()).toBe(9);
      expect(result.getMinutes()).toBe(0);
      expect(result.getSeconds()).toBe(0);
      expect(result.getMilliseconds()).toBe(0);
    });
  });

  describe('getMinSchedulingDate', () => {
    test('returns a date that is 1 day ahead of current date', () => {
      const result = getMinSchedulingDate();
      const expected = addDays(mockDate, 1);

      expect(result.getFullYear()).toBe(expected.getFullYear());
      expect(result.getMonth()).toBe(expected.getMonth());
      expect(result.getDate()).toBe(expected.getDate());
    });
  });

  describe('isDateAtLeast24HoursAhead', () => {
    test('returns true for dates more than 24 hours ahead', () => {
      const twoDaysLater = addDays(mockDate, 2);
      expect(isDateAtLeast24HoursAhead(twoDaysLater)).toBe(true);
    });

    test('returns false for dates less than 24 hours ahead', () => {
      const sameDay = new Date(mockDate);
      expect(isDateAtLeast24HoursAhead(sameDay)).toBe(false);

      const lessThan24Hours = addDays(mockDate, 1);
      lessThan24Hours.setHours(lessThan24Hours.getHours() - 1);
      expect(isDateAtLeast24HoursAhead(lessThan24Hours)).toBe(false);
    });
  });

  describe('getTwentyFourHoursLater', () => {
    test('returns timestamp 24 hours from now', () => {
      const expected = mockDate.getTime() + 24 * 60 * 60 * 1000;
      expect(getTwentyFourHoursLater()).toBe(expected);
    });
  });

  describe('isMorningPeriodAvailable', () => {
    test('returns true for dates more than 1 day ahead', () => {
      const twoDaysLater = addDays(mockDate, 2);
      expect(isMorningPeriodAvailable(twoDaysLater)).toBe(true);
    });

    test('returns false for tomorrow if less than 24 hours until morning period ends', () => {
      const tomorrow = addDays(mockDate, 1);
      jest.setSystemTime(new Date(tomorrow).setHours(13)); // After morning period ends
      expect(isMorningPeriodAvailable(tomorrow)).toBe(false);
    });
  });

  describe('isAfternoonPeriodAvailable', () => {
    test('returns true for dates more than 1 day ahead', () => {
      const twoDaysLater = addDays(mockDate, 2);
      expect(isAfternoonPeriodAvailable(twoDaysLater)).toBe(true);
    });

    test('returns false for tomorrow if less than 24 hours until afternoon period ends', () => {
      const tomorrow = addDays(mockDate, 1);
      jest.setSystemTime(new Date(tomorrow).setHours(19)); // After afternoon period ends
      expect(isAfternoonPeriodAvailable(tomorrow)).toBe(false);
    });
  });

  describe('getDisabledDates', () => {
    test('returns true for dates in the past', () => {
      const yesterday = addDays(mockDate, -1);
      expect(getDisabledDates(yesterday)).toBe(true);
    });

    test('returns true for today', () => {
      const today = new Date(mockDate);
      expect(getDisabledDates(today)).toBe(true);
    });

    test('returns true for tomorrow if both periods are unavailable', () => {
      const tomorrow = addDays(mockDate, 1);
      jest.setSystemTime(new Date(tomorrow).setHours(19)); // After both periods end
      expect(getDisabledDates(tomorrow)).toBe(true);
    });

    test('returns false for tomorrow if only morning is available', () => {
      const tomorrow = addDays(mockDate, 1);
      const isMorning = () => true;
      const isAfternoon = () => false;
      expect(getDisabledDates(tomorrow, isMorning, isAfternoon)).toBe(false);
    });

    test('returns false for tomorrow if only afternoon is available', () => {
      const tomorrow = addDays(mockDate, 1);
      const isMorning = () => false;
      const isAfternoon = () => true;
      expect(getDisabledDates(tomorrow, isMorning, isAfternoon)).toBe(false);
    });

    test('returns false for tomorrow if both periods are available', () => {
      const tomorrow = addDays(mockDate, 1);
      const isMorning = () => true;
      const isAfternoon = () => true;
      expect(getDisabledDates(tomorrow, isMorning, isAfternoon)).toBe(false);
    });

    test('returns false for dates more than 1 day ahead', () => {
      const twoDaysLater = addDays(mockDate, 2);
      // Custom period checkers to ensure it falls through to the final branch
      const isMorning = () => false;
      const isAfternoon = () => false;
      expect(getDisabledDates(twoDaysLater, isMorning, isAfternoon)).toBe(false);
    });

    test('explicitly covers final return false for non-past, non-today, non-tomorrow', () => {
      const threeDaysLater = addDays(mockDate, 3);
      // Custom period checkers (should not matter)
      const isMorning = () => false;
      const isAfternoon = () => false;
      expect(getDisabledDates(threeDaysLater, isMorning, isAfternoon)).toBe(false);
    });

    test('covers final return false for more than one day ahead with default functions', () => {
      const threeDaysLater = addDays(mockDate, 3);
      expect(getDisabledDates(threeDaysLater)).toBe(false);
    });
  });
});
