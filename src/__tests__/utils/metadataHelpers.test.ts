import { getImageUrl, getServiceDescription } from '@/src/app/_utils/metadataHelpers';

describe('metadataHelpers', () => {
  describe('getImageUrl', () => {
    it('handles null input', () => {
      const result = getImageUrl(null);
      expect(result).toBe('');
    });

    it('handles undefined input', () => {
      const result = getImageUrl(undefined);
      expect(result).toBe('');
    });

    it('handles empty string input', () => {
      const result = getImageUrl('');
      expect(result).toBe('');
    });

    it('handles string input', () => {
      const result = getImageUrl('https://example.com/image.jpg');
      expect(result).toBe('https://example.com/image.jpg');
    });

    it('handles number input', () => {
      const result = getImageUrl(123);
      expect(result).toBe(123);
    });

    it('handles boolean input', () => {
      const result = getImageUrl(true);
      expect(result).toBe(true);
    });

    it('handles array input', () => {
      const input = ['https://example.com/image.jpg'];
      const result = getImageUrl(input);
      expect(result).toBe(input);
    });

    it('handles object input', () => {
      const input = { url: 'https://example.com/image.jpg' };
      const result = getImageUrl(input);
      expect(result).toBe(input);
    });

    it('handles object input without url property', () => {
      const input = { notUrl: 'https://example.com/image.jpg' };
      const result = getImageUrl(input);
      expect(result).toBe(input);
    });
  });

  describe('getServiceDescription', () => {
    // Tests specifically targeting the branch at line 24 where service is falsy
    it('handles null service', () => {
      const result = getServiceDescription(null);
      expect(result).toBe(
        'Contrate serviços com profissionais qualificados e garantia de serviço.'
      );
    });

    it('handles falsy service values (0)', () => {
      // Testing with 0 as a falsy value
      const result = getServiceDescription(0);
      expect(result).toBe(
        'Contrate serviços com profissionais qualificados e garantia de serviço.'
      );
    });

    it('handles falsy service values (false)', () => {
      // Testing with false as a falsy value
      const result = getServiceDescription(false);
      expect(result).toBe(
        'Contrate serviços com profissionais qualificados e garantia de serviço.'
      );
    });

    it('handles null service with defaultDesc overriding', () => {
      // This tests the branch at line 24 where service is falsy and we return the default description
      const result = getServiceDescription(null);

      // The default description should be returned for null service
      expect(result).toBe(
        'Contrate serviços com profissionais qualificados e garantia de serviço.'
      );

      // Make sure it's not the same as the non-null default desc (different test case)
      expect(result).not.toBe(
        'Serviço com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
      );
    });
    it('handles empty object as service', () => {
      const result = getServiceDescription({});
      expect(result).toBe(
        'Serviço com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
      );
    });

    it('handles undefined service', () => {
      const result = getServiceDescription(undefined);
      expect(result).toBe(
        'Contrate serviços com profissionais qualificados e garantia de serviço.'
      );
    });

    it('handles service without name', () => {
      const result = getServiceDescription({});
      expect(result).toContain('Serviço com profissionais qualificados');
    });

    it('handles service with name', () => {
      const result = getServiceDescription({ name: 'Test Service' });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with null description', () => {
      const result = getServiceDescription({ name: 'Test Service', description: null });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with undefined description', () => {
      const result = getServiceDescription({ name: 'Test Service', description: undefined });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with empty string description', () => {
      const result = getServiceDescription({ name: 'Test Service', description: '' });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with whitespace-only description', () => {
      const result = getServiceDescription({ name: 'Test Service', description: '   ' });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with non-string description', () => {
      const result = getServiceDescription({ name: 'Test Service', description: 123 });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with valid description', () => {
      const result = getServiceDescription({
        name: 'Test Service',
        description: 'This is a test description',
      });
      expect(result).toBe('This is a test description');
    });

    it('handles service with long description', () => {
      const longDescription =
        'This is a very long description that exceeds the maximum length allowed for meta descriptions. '.repeat(
          10
        );
      const result = getServiceDescription({
        name: 'Test Service',
        description: longDescription,
      });
      expect(result.length).toBeLessThanOrEqual(160);
      expect(result).toContain('...');
    });

    it('handles service with description exactly at max length', () => {
      // Create a string that's exactly 157 characters long
      const exactLengthDescription = 'A'.repeat(157);
      const result = getServiceDescription({
        name: 'Test Service',
        description: exactLengthDescription,
      });
      expect(result.length).toBe(157);
      expect(result).not.toContain('...');
    });

    it('handles service with description one character over max length', () => {
      // Create a string that's 158 characters long (one over the limit)
      const overLengthDescription = 'A'.repeat(158);
      const result = getServiceDescription({
        name: 'Test Service',
        description: overLengthDescription,
      });
      expect(result.length).toBe(160); // 157 + 3 for '...'
      expect(result).toContain('...');
    });
    it('handles edge case with non-object service', () => {
      // This should trigger the default service name path
      const result = getServiceDescription('This is a string, not an object');
      expect(result).toBe(
        'Serviço com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
      );
    });

    it('handles edge case with malformed service', () => {
      // Test the branch where service is neither null nor has expected properties
      const malformedService = { randomProp: 'value' };
      const result = getServiceDescription(malformedService);
      expect(result).toBe(
        'Serviço com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
      );
    });

    it('handles service with object description', () => {
      // Test where description exists but is an object type (not a string)
      // This will test the branch on line 25 where typeof check fails
      const result = getServiceDescription({
        name: 'Test Service',
        description: { text: 'This is inside an object' },
      });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with array description', () => {
      // Test where description exists but is an array type (not a string)
      // This will test another case of the branch on line 25 where typeof check fails
      const result = getServiceDescription({
        name: 'Test Service',
        description: ['This is inside an array'],
      });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with null prototype object', () => {
      // Test where service is Object.create(null)
      // This tests edge cases with objects that don't have standard prototypes
      const serviceWithNullProto = Object.create(null);
      serviceWithNullProto.name = 'Test Service';
      const result = getServiceDescription(serviceWithNullProto);
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with boolean description', () => {
      const result = getServiceDescription({
        name: 'Test Service',
        description: true,
      });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with object description', () => {
      const result = getServiceDescription({
        name: 'Test Service',
        description: { text: 'This should not be used' },
      });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with array description', () => {
      const result = getServiceDescription({
        name: 'Test Service',
        description: ['This', 'should', 'not', 'be', 'used'],
      });
      expect(result).toContain('Test Service com profissionais qualificados');
    });

    it('handles service with description that needs trimming', () => {
      const result = getServiceDescription({
        name: 'Test Service',
        description: '  This description has whitespace around it  ',
      });
      expect(result).toBe('This description has whitespace around it');
    });

    it('handles service with description exactly at max length without ellipsis', () => {
      // Create string exactly at the cutoff length (157 chars)
      const exactLengthDesc = 'X'.repeat(157);
      const result = getServiceDescription({
        name: 'Test Service',
        description: exactLengthDesc,
      });

      // Should be kept as-is without truncation
      expect(result).toBe(exactLengthDesc);
      expect(result.length).toBe(157);
      expect(result).not.toContain('...');
    });

    it('handles service with description one character over max length with ellipsis', () => {
      // Create string one character over the cutoff length (158 chars)
      const overLengthDesc = 'X'.repeat(158);
      const result = getServiceDescription({
        name: 'Test Service',
        description: overLengthDesc,
      });

      // Should be truncated with ellipsis
      expect(result.length).toBe(160); // 157 chars + '...' (3 chars)
      expect(result).toContain('...');
      expect(result).toBe(overLengthDesc.substring(0, 157) + '...');
    });
  });
});
