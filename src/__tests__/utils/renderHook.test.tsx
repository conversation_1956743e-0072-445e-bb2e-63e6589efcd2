import { renderHook, TestWrapper } from './renderHook';
import { render } from '@testing-library/react';

describe('renderHook utility', () => {
  test('TestWrapper should render children correctly', () => {
    const { container } = render(<TestWrapper>Test content</TestWrapper>);
    expect(container.textContent).toBe('Test content');
  });

  test('renderHook should return hook result', () => {
    const useTestHook = () => ({ value: 'test result' });
    const { result } = renderHook(() => useTestHook());
    expect(result.current.value).toBe('test result');
  });
});
