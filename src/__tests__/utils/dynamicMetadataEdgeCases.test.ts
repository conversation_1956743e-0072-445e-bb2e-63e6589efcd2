import { serviceTypeApi } from '@/src/app/_services/serviceTypeApi';
import { createFallbackMetadata, generateDynamicMetadata } from '@/src/app/_utils/dynamicMetadata';

// Mock the serviceTypeApi
jest.mock('@/src/app/_services/serviceTypeApi', () => ({
  serviceTypeApi: {
    getServiceTypeBySlug: jest.fn(),
  },
}));

describe('dynamicMetadata edge cases', () => {
  const mockBaseUrl = 'https://test.example.com';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createFallbackMetadata', () => {
    it('handles all parameters being null', () => {
      const result = createFallbackMetadata(null as any, null as any, null as any, null as any);

      expect(result).toBeDefined();
      expect(result.title).toBe('Serviço | GetNinjas + Europ Assistance');
      expect(result.description).toBe(
        'Contrate Serviço com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
      );
      expect(result.keywords).toBe('servico, europ, assistance, getninjas, serviço, profissional');
      expect(result.openGraph?.url).toBe(
        'https://europ.getninjas.com.br/servicos/categoria?=servico'
      );
    });

    it('handles all parameters being empty strings', () => {
      const result = createFallbackMetadata('', '', '', '');

      expect(result).toBeDefined();
      expect(result.title).toBe('Serviço | GetNinjas + Europ Assistance');
      expect(result.description).toBe(
        'Contrate Serviço com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
      );
      expect(result.keywords).toBe('servico, europ, assistance, getninjas, serviço, profissional');
      expect(result.openGraph?.url).toBe(
        'https://europ.getninjas.com.br/servicos/categoria?=servico'
      );
    });

    it('handles serviceName being null', () => {
      const result = createFallbackMetadata(
        null as any,
        'test-service',
        'subcategory',
        mockBaseUrl
      );

      expect(result).toBeDefined();
      expect(result.title).toBe('Serviço | GetNinjas + Europ Assistance');
      expect(result.description).toBe(
        'Contrate Serviço com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
      );
    });

    it('handles slug being null', () => {
      const result = createFallbackMetadata(
        'Test Service',
        null as any,
        'subcategory',
        mockBaseUrl
      );

      expect(result).toBeDefined();
      expect(result.keywords).toBe('servico, europ, assistance, getninjas, serviço, profissional');
      expect(result.openGraph?.url).toBe('https://test.example.com/servicos/subcategory?=servico');
    });

    it('handles subcategorySlug being null', () => {
      const result = createFallbackMetadata(
        'Test Service',
        'test-service',
        null as any,
        mockBaseUrl
      );

      expect(result).toBeDefined();
      expect(result.openGraph?.url).toBe(
        'https://test.example.com/servicos/categoria?=test-service'
      );
    });

    it('handles baseUrl being null', () => {
      const result = createFallbackMetadata(
        'Test Service',
        'test-service',
        'subcategory',
        null as any
      );

      expect(result).toBeDefined();
      expect(result.openGraph?.url).toBe(
        'https://europ.getninjas.com.br/servicos/subcategory?=test-service'
      );
    });
  });

  describe('generateDynamicMetadata edge cases', () => {
    it('handles service with null name in defaultImage', async () => {
      const mockService = {
        // No name property
        description: 'This is a test service description',
        // No imageUrl
        price: {
          finalPrice: 100,
        },
        provider: {
          name: 'Test Provider',
          providerUrl: 'https://provider.com',
        },
      };

      (serviceTypeApi.getServiceTypeBySlug as jest.Mock).mockResolvedValueOnce({
        service: mockService,
      });

      const result = await generateDynamicMetadata('test-service', 'subcategory', mockBaseUrl);

      // Check that default image is used with fallback alt text
      const images = result.openGraph?.images;
      if (Array.isArray(images)) {
        expect(images[0]).toHaveProperty(
          'url',
          'https://test.example.com/images/getninjas_europ_logo.svg'
        );
        expect(images[0]).toHaveProperty('alt', 'Serviço profissional');
      }
    });

    it('handles service with null name in serviceName variable', async () => {
      const mockService = {
        name: null, // Null name
        description: 'This is a test service description',
        imageUrl: 'https://example.com/image.jpg',
        price: {
          finalPrice: 100,
        },
        provider: {
          name: 'Test Provider',
          providerUrl: 'https://provider.com',
        },
      };

      (serviceTypeApi.getServiceTypeBySlug as jest.Mock).mockResolvedValueOnce({
        service: mockService,
      });

      const result = await generateDynamicMetadata('test-service', 'subcategory', mockBaseUrl);

      // Check that fallback name is used
      // The title is set from the formatted slug, not the service.name
      expect(result.title).toBe('Test Service | GetNinjas + Europ Assistance');
      // The twitter title is also set from the formatted slug
      expect(result.twitter?.title).toBe('Test Service | GetNinjas + Europ Assistance');
    });

    it('handles service with empty string name', async () => {
      const mockService = {
        name: '', // Empty string name
        description: 'This is a test service description',
        imageUrl: 'https://example.com/image.jpg',
        price: {
          finalPrice: 100,
        },
        provider: {
          name: 'Test Provider',
          providerUrl: 'https://provider.com',
        },
      };

      (serviceTypeApi.getServiceTypeBySlug as jest.Mock).mockResolvedValueOnce({
        service: mockService,
      });

      const result = await generateDynamicMetadata('test-service', 'subcategory', mockBaseUrl);

      // Check that fallback name is used
      // The title is set from the formatted slug, not the service.name
      expect(result.openGraph?.title).toBe('Test Service | GetNinjas + Europ Assistance');
    });

    it('handles service with null description in serviceDesc variable', async () => {
      const mockService = {
        name: 'Test Service',
        description: null, // Null description
        imageUrl: 'https://example.com/image.jpg',
        price: {
          finalPrice: 100,
        },
        provider: {
          name: 'Test Provider',
          providerUrl: 'https://provider.com',
        },
      };

      (serviceTypeApi.getServiceTypeBySlug as jest.Mock).mockResolvedValueOnce({
        service: mockService,
      });

      const result = await generateDynamicMetadata('test-service', 'subcategory', mockBaseUrl);

      // Check that fallback description is used
      expect(result.description).toBe(
        'Test Service com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
      );
    });

    it('handles service with empty string description in serviceDesc variable', async () => {
      const mockService = {
        name: 'Test Service',
        description: '', // Empty string description
        imageUrl: 'https://example.com/image.jpg',
        price: {
          finalPrice: 100,
        },
        provider: {
          name: 'Test Provider',
          providerUrl: 'https://provider.com',
        },
      };

      (serviceTypeApi.getServiceTypeBySlug as jest.Mock).mockResolvedValueOnce({
        service: mockService,
      });

      const result = await generateDynamicMetadata('test-service', 'subcategory', mockBaseUrl);

      // Check that fallback description is used
      expect(result.description).toBe(
        'Test Service com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
      );
    });

    it('handles service with non-string description in serviceDesc variable', async () => {
      const mockService = {
        name: 'Test Service',
        description: 123, // Non-string description
        imageUrl: 'https://example.com/image.jpg',
        price: {
          finalPrice: 100,
        },
        provider: {
          name: 'Test Provider',
          providerUrl: 'https://provider.com',
        },
      };

      (serviceTypeApi.getServiceTypeBySlug as jest.Mock).mockResolvedValueOnce({
        service: mockService,
      });

      const result = await generateDynamicMetadata('test-service', 'subcategory', mockBaseUrl);

      // Check that fallback description is used
      expect(result.description).toBe(
        'Test Service com profissionais qualificados e garantia de serviço. Agende agora mesmo.'
      );
    });

    it('handles service with long description in serviceDesc variable', async () => {
      const longDescription =
        'This is a very long description that exceeds the maximum length allowed for meta descriptions. '.repeat(
          10
        );
      const mockService = {
        name: 'Test Service',
        description: longDescription, // Long description
        imageUrl: 'https://example.com/image.jpg',
        price: {
          finalPrice: 100,
        },
        provider: {
          name: 'Test Provider',
          providerUrl: 'https://provider.com',
        },
      };

      (serviceTypeApi.getServiceTypeBySlug as jest.Mock).mockResolvedValueOnce({
        service: mockService,
      });

      const result = await generateDynamicMetadata('test-service', 'subcategory', mockBaseUrl);

      // Check that description is truncated
      expect(result.description?.length).toBeLessThanOrEqual(160);
      expect(result.description?.endsWith('...')).toBe(true);
    });

    it('handles service with null name in defaultImage alt text', async () => {
      const mockService = {
        name: null, // Null name
        description: 'This is a test service description',
        // No imageUrl
        price: {
          finalPrice: 100,
        },
        provider: {
          name: 'Test Provider',
          providerUrl: 'https://provider.com',
        },
      };

      (serviceTypeApi.getServiceTypeBySlug as jest.Mock).mockResolvedValueOnce({
        service: mockService,
      });

      const result = await generateDynamicMetadata('test-service', 'subcategory', mockBaseUrl);

      // Check that default image is used with fallback alt text
      const images = result.openGraph?.images;
      if (Array.isArray(images)) {
        expect(images[0]).toHaveProperty(
          'url',
          'https://test.example.com/images/getninjas_europ_logo.svg'
        );
        expect(images[0]).toHaveProperty('alt', 'Serviço profissional');
      }
    });

    it('handles service with empty string name in defaultImage alt text', async () => {
      const mockService = {
        name: '', // Empty string name
        description: 'This is a test service description',
        // No imageUrl
        price: {
          finalPrice: 100,
        },
        provider: {
          name: 'Test Provider',
          providerUrl: 'https://provider.com',
        },
      };

      (serviceTypeApi.getServiceTypeBySlug as jest.Mock).mockResolvedValueOnce({
        service: mockService,
      });

      const result = await generateDynamicMetadata('test-service', 'subcategory', mockBaseUrl);

      // Check that default image is used with fallback alt text
      const images = result.openGraph?.images;
      if (Array.isArray(images)) {
        expect(images[0]).toHaveProperty(
          'url',
          'https://test.example.com/images/getninjas_europ_logo.svg'
        );
        expect(images[0]).toHaveProperty('alt', 'Serviço profissional');
      }
    });
  });

  // This targets the null/empty check in baseUrl at line 26
  describe('generateDynamicMetadata baseUrl parameter', () => {
    it('handles baseUrl that is a literal undefined value', async () => {
      const mockService = {
        name: 'Test Service',
        description: 'Test description',
        provider: {
          name: 'Test Provider',
          providerUrl: 'https://provider.com',
        },
      };

      (serviceTypeApi.getServiceTypeBySlug as jest.Mock).mockResolvedValueOnce({
        service: mockService,
      });

      // Explicitly pass undefined for baseUrl parameter
      const result = await generateDynamicMetadata('test-service', 'subcategory', undefined);

      // Verify fallback URL was used
      expect(result.openGraph?.url).toContain('https://europ.getninjas.com.br');
    });
  });
});
