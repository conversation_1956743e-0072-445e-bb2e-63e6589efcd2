import { formatCPF, formatDate, formatPeriod, truncateText } from '@/src/app/_utils/formatters';

describe('formatDate', () => {
  test('formats date string correctly in Brazilian format', () => {
    expect(formatDate('2023-05-15')).toMatch(/15 de maio de 2023/i);
    expect(formatDate('2024-01-01')).toMatch(/01 de janeiro de 2024/i);
    expect(formatDate('2022-12-31')).toMatch(/31 de dezembro de 2022/i);
  });
});

describe('formatPeriod', () => {
  test('formats morning period correctly', () => {
    expect(formatPeriod('morning')).toBe('Manhã (8h - 12h)');
  });

  test('formats afternoon period correctly', () => {
    expect(formatPeriod('afternoon')).toBe('Tarde (13h - 18h)');
  });
});

describe('truncateText', () => {
  test('returns empty string for undefined input', () => {
    expect(truncateText(undefined)).toBe('');
  });

  test('returns the original text if shorter than maxChars', () => {
    const shortText = 'This is a short text';
    expect(truncateText(shortText)).toBe(shortText);
  });

  test('truncates text and adds ellipsis if longer than maxChars', () => {
    const longText =
      'This is a very long text that should be truncated because it exceeds the maximum number of characters allowed';
    const truncated = truncateText(longText, 20);
    expect(truncated).toBe('This is a very long ...');
    expect(truncated.length).toBe(23); // 20 chars + 3 for ellipsis
  });

  test('handles array of strings by joining them with commas', () => {
    const textArray = ['Item 1', 'Item 2', 'Item 3'];
    expect(truncateText(textArray)).toBe('Item 1, Item 2, Item 3');
  });

  test('truncates joined array if exceeds maxChars', () => {
    const longArray = ['Very long item 1', 'Very long item 2', 'Very long item 3'];
    expect(truncateText(longArray, 25)).toBe('Very long item 1, Very lo...');
  });
});

describe('formatCPF', () => {
  test('returns empty string for empty input', () => {
    expect(formatCPF('')).toBe('');
  });

  test('formats CPF digits with proper formatting', () => {
    expect(formatCPF('12345678901')).toBe('123.456.789-01');
  });

  test('removes non-digit characters before formatting', () => {
    expect(formatCPF('123.456.789-01')).toBe('123.456.789-01');
    expect(formatCPF('123abc456def789ghi01')).toBe('123.456.789-01');
  });
});
