import { cn } from '@/src/app/_utils/utils';
import { type ClassValue } from 'clsx';

// Create a more type-safe implementation
jest.mock('clsx', () => {
  return {
    clsx: jest.fn((...inputs: ClassValue[]) => {
      // Convert inputs to a flattened array of valid class names
      const classes: string[] = [];

      const extractClasses = (input: any): void => {
        if (!input) return;

        if (typeof input === 'string') {
          classes.push(input);
        } else if (Array.isArray(input)) {
          input.forEach((item: any) => extractClasses(item));
        } else if (typeof input === 'object') {
          Object.entries(input).forEach(([className, condition]) => {
            if (condition) {
              classes.push(className);
            }
          });
        }
      };

      // Process all inputs
      for (const input of inputs) {
        extractClasses(input);
      }

      return classes.filter(Boolean);
    }),
  };
});

jest.mock('tailwind-merge', () => {
  return {
    twMerge: jest.fn((classes: string | string[]) => {
      // If classes is an array (from clsx), join it into a string
      if (Array.isArray(classes)) {
        return classes.join(' ');
      }
      return classes;
    }),
  };
});

describe('utils', () => {
  describe('cn', () => {
    test('merges class names correctly', () => {
      expect(cn('class1', 'class2')).toBe('class1 class2');
    });

    test('handles conditional classes', () => {
      expect(cn('base-class', true && 'conditional-class', false && 'not-included')).toBe(
        'base-class conditional-class'
      );
    });

    test('handles array of classes', () => {
      expect(cn(['class1', 'class2', null, undefined, false, ''])).toBe('class1 class2');
    });

    test('handles object notation', () => {
      expect(cn({ class1: true, class2: false, class3: true })).toBe('class1 class3');
    });

    test('handles mixed inputs', () => {
      expect(
        cn('base-class', ['array-class1', 'array-class2'], {
          'object-class1': true,
          'object-class2': false,
        })
      ).toBe('base-class array-class1 array-class2 object-class1');
    });
  });
});
