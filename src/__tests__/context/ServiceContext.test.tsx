import { ServiceProvider, useServiceContext } from '@/src/app/_context/ServiceContext';
import { render, screen, cleanup } from '@testing-library/react';

// Mock data
const mockServices = [
  {
    id: 1,
    name: 'Assistência Técnica',
    slug: 'assistencia-tecnica',
    subcategories: [
      {
        id: 11,
        name: 'Ce<PERSON><PERSON><PERSON>',
        slug: 'celulares',
        services: [
          {
            id: 111,
            name: 'Conserto de Celular',
            slug: 'conserto-celular',
            description: 'Serviço de conserto de celular',
            status: 'active',
            price: {
              priceId: 1111,
              originalPrice: 100,
              discountPrice: 0,
              finalPrice: 100,
            },
            provider: {
              id: 11111,
              name: 'Técnico de Celular',
              providerUrl: 'https://example.com',
            },
            availableIn: ['São Paulo'],
            details: ['Conserto de tela', 'Troca de bateria'],
            keywords: ['celular', 'conserto'],
            categoryId: 1,
            categoryName: 'Assistência Técnica',
            categorySlug: 'assistencia-tecnica',
            subcategoryId: 11,
            subcategoryName: 'Celulares',
            subcategorySlug: 'celulares',
          },
        ],
      },
    ],
  },
];

// Test component that uses the context
const TestComponent = () => {
  const { services } = useServiceContext();
  return (
    <div>
      <h1>Services</h1>
      <ul>
        {services.map((category) => (
          <li key={category.id} data-testid="category">
            {category.name}
            <ul>
              {category.subcategories.map((subcategory) => (
                <li key={subcategory.id} data-testid="subcategory">
                  {subcategory.name}
                </li>
              ))}
            </ul>
          </li>
        ))}
      </ul>
    </div>
  );
};

describe('ServiceContext', () => {
  it('provides service data to components', () => {
    render(
      <ServiceProvider services={mockServices}>
        <TestComponent />
      </ServiceProvider>
    );

    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.getByText('Assistência Técnica')).toBeInTheDocument();
    expect(screen.getByText('Celulares')).toBeInTheDocument();
  });

  it('handles empty services array', () => {
    render(
      <ServiceProvider services={[]}>
        <TestComponent />
      </ServiceProvider>
    );

    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.queryByTestId('category')).not.toBeInTheDocument();
  });

  it('handles null services', () => {
    // Using as any to bypass type checking for testing purposes
    // This is intentional as we want to test the ServiceProvider's handling of invalid inputs
    render(
      <ServiceProvider services={null as any}>
        <TestComponent />
      </ServiceProvider>
    );

    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.queryByTestId('category')).not.toBeInTheDocument();
  });

  it('handles undefined services', () => {
    // Using as any to bypass type checking for testing purposes
    // This is intentional as we want to test the ServiceProvider's handling of invalid inputs
    render(
      <ServiceProvider services={undefined as any}>
        <TestComponent />
      </ServiceProvider>
    );

    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.queryByTestId('category')).not.toBeInTheDocument();
  });

  it('handles non-array services input', () => {
    // Using as any to bypass type checking for testing purposes
    // This is intentional as we want to test the ServiceProvider's handling of invalid inputs
    render(
      <ServiceProvider services={{ id: 1, name: 'Not an array' } as any}>
        <TestComponent />
      </ServiceProvider>
    );

    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.queryByTestId('category')).not.toBeInTheDocument();
  });

  it('handles number as non-array services input', () => {
    // Test with a number value
    // Using as any to bypass type checking for testing purposes
    render(
      <ServiceProvider services={123 as any}>
        <TestComponent />
      </ServiceProvider>
    );
    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.queryByTestId('category')).not.toBeInTheDocument();
    cleanup();
  });

  it('handles string as non-array services input', () => {
    // Test with a string value
    render(
      <ServiceProvider services={'string value' as any}>
        <TestComponent />
      </ServiceProvider>
    );
    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.queryByTestId('category')).not.toBeInTheDocument();
    cleanup();
  });

  it('handles boolean as non-array services input', () => {
    // Test with a boolean value
    render(
      <ServiceProvider services={true as any}>
        <TestComponent />
      </ServiceProvider>
    );
    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.queryByTestId('category')).not.toBeInTheDocument();
    cleanup();
  });

  it('handles function as non-array services input', () => {
    // Test with a function value
    render(
      <ServiceProvider services={(() => {}) as any}>
        <TestComponent />
      </ServiceProvider>
    );
    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.queryByTestId('category')).not.toBeInTheDocument();
    cleanup();
  });

  it('throws error when used outside provider', () => {
    // Suppress console.error for this test
    const originalConsoleError = console.error;
    console.error = jest.fn();

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useServiceContext deve ser usado dentro de um ServiceProvider');

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should directly use the useServiceContext hook', () => {
    // Create a component that uses the hook directly
    const HookTestComponent = () => {
      const { services } = useServiceContext();
      return (
        <div data-testid="hook-test">
          {services.map((category) => (
            <div key={category.id} data-testid="hook-category">
              {category.name}
            </div>
          ))}
        </div>
      );
    };

    render(
      <ServiceProvider services={mockServices}>
        <HookTestComponent />
      </ServiceProvider>
    );

    // Verify the hook was used successfully
    expect(screen.getByTestId('hook-test')).toBeInTheDocument();
    expect(screen.getByTestId('hook-category')).toHaveTextContent('Assistência Técnica');
  });

  // Directly test the Array.isArray branch in ServiceContext.tsx
  it('directly tests the Array.isArray branch by accessing the context value', () => {
    // Create a component that captures the context value
    const ContextCapturingComponent = () => {
      const contextValue = useServiceContext();
      // Expose the context value for test assertions
      (window as any).capturedContextValue = contextValue;
      return <div>Context Captured</div>;
    };

    // Test with a non-array value
    render(
      <ServiceProvider services={'not an array' as any}>
        <ContextCapturingComponent />
      </ServiceProvider>
    );

    // Check that the services value is an empty array
    expect((window as any).capturedContextValue.services).toEqual([]);
    expect(Array.isArray((window as any).capturedContextValue.services)).toBe(true);
    expect((window as any).capturedContextValue.services.length).toBe(0);

    // Clean up
    delete (window as any).capturedContextValue;
  });
});
