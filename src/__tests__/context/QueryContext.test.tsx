import { QueryProvider } from '@/src/app/_context/QueryContext';
import { QueryClient } from '@tanstack/react-query';
import { render } from '@testing-library/react';

// Mock react-query dependencies
jest.mock('@tanstack/react-query', () => ({
  QueryClient: jest.fn().mockImplementation(() => ({
    // Mock QueryClient methods as needed
  })),
  QueryClientProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="mock-query-client-provider">{children}</div>
  ),
}));

jest.mock('@tanstack/react-query-devtools', () => ({
  ReactQueryDevtools: () => <div data-testid="mock-react-query-devtools" />,
}));

describe('QueryProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('creates QueryClient with correct default options', () => {
    render(<QueryProvider>Test</QueryProvider>);

    // Check that QueryClient was initialized with the correct options
    expect(QueryClient).toHaveBeenCalledWith({
      defaultOptions: {
        queries: {
          staleTime: 60 * 1000, // 1 minute
          gcTime: 5 * 60 * 1000, // 5 minutes
          retry: 1,
          refetchOnWindowFocus: false,
        },
      },
    });
  });

  test('renders QueryClientProvider with QueryClient instance', () => {
    const { getByTestId } = render(<QueryProvider>Test</QueryProvider>);

    // Check that QueryClientProvider was rendered
    expect(getByTestId('mock-query-client-provider')).toBeInTheDocument();
  });

  test('renders children correctly', () => {
    const { getByText } = render(
      <QueryProvider>
        <div>Child Component</div>
      </QueryProvider>
    );

    expect(getByText('Child Component')).toBeInTheDocument();
  });

  test('includes ReactQueryDevtools in the render output', () => {
    const { getByTestId } = render(<QueryProvider>Test</QueryProvider>);

    // Check that ReactQueryDevtools was rendered
    expect(getByTestId('mock-react-query-devtools')).toBeInTheDocument();
  });

  test('uses the same QueryClient instance for multiple renders', () => {
    const { rerender } = render(<QueryProvider>First Render</QueryProvider>);
    const initialCallCount = QueryClient.mock.calls.length;

    rerender(<QueryProvider>Second Render</QueryProvider>);

    // Check that QueryClient wasn't called again after rerender
    expect(QueryClient).toHaveBeenCalledTimes(initialCallCount);
  });
});
