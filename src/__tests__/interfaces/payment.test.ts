import { validatePaymentResponse, validateOrderPayload } from '@/src/app/_interfaces/payment';

test('validatePaymentResponse valida corretamente com errors null', () => {
  const validPayment = {
    orderId: '123456',
    secureUrl: 'https://payment-gateway.com/checkout/123456',
    errors: null,
    secureId: 'sec-123456',
  };
  expect(validatePaymentResponse(validPayment)).toBe(true);
});

test('validatePaymentResponse valida corretamente com errors array', () => {
  const validPayment = {
    orderId: '123456',
    secureUrl: 'https://payment-gateway.com/checkout/123456',
    errors: ['Error 1', 'Error 2'],
    secureId: 'sec-123456',
  };
  expect(validatePaymentResponse(validPayment)).toBe(true);
});

test('validatePaymentResponse rejeita objeto inválido', () => {
  const invalidPayment = {
    orderId: '123456',
    secureUrl: 'https://payment-gateway.com/checkout/123456',
    // errors está faltando
    secureId: 'sec-123456',
  };
  expect(validatePaymentResponse(invalidPayment)).toBe(false);
});

test('validatePaymentResponse rejeita errors como objeto', () => {
  const invalidPayment = {
    orderId: '123456',
    secureUrl: 'https://payment-gateway.com/checkout/123456',
    errors: { error: 'Invalid payment' }, // errors deve ser array ou null
    secureId: 'sec-123456',
  };
  expect(validatePaymentResponse(invalidPayment)).toBe(false);
});

test('validateOrderPayload valida corretamente com serviceId number', () => {
  const validPayload = {
    priceId: 1,
    phoneCode: '11',
    phoneNumber: '999998888',
    email: '<EMAIL>',
    complement: 'Apt 101',
    modality: 'online',
    orderDate: '2023-05-15T10:00:00Z',
    scheduledDate: '2023-05-20',
    scheduledPeriod: 'morning',
    personTypeId: 1,
    firstName: 'John',
    lastName: 'Doe',
    socialName: 'John Doe',
    identificationNumber: '123.456.789-00',
    numberAd: '123',
    successUrl: 'https://example.com/success',
    zipCode: '01001-000',
    serviceId: 1,
  };
  expect(validateOrderPayload(validPayload)).toBe(true);
});

test('validateOrderPayload valida corretamente com serviceId string', () => {
  const validPayload = {
    priceId: 1,
    phoneCode: '11',
    phoneNumber: '999998888',
    email: '<EMAIL>',
    complement: 'Apt 101',
    modality: 'online',
    orderDate: '2023-05-15T10:00:00Z',
    scheduledDate: '2023-05-20',
    scheduledPeriod: 'morning',
    personTypeId: 1,
    firstName: 'John',
    lastName: 'Doe',
    socialName: 'John Doe',
    identificationNumber: '123.456.789-00',
    numberAd: '123',
    successUrl: 'https://example.com/success',
    zipCode: '01001-000',
    serviceId: '1',
  };
  expect(validateOrderPayload(validPayload)).toBe(true);
});

test('validateOrderPayload valida corretamente sem serviceId', () => {
  const validPayload = {
    priceId: 1,
    phoneCode: '11',
    phoneNumber: '999998888',
    email: '<EMAIL>',
    complement: 'Apt 101',
    modality: 'online',
    orderDate: '2023-05-15T10:00:00Z',
    scheduledDate: '2023-05-20',
    scheduledPeriod: 'morning',
    personTypeId: 1,
    firstName: 'John',
    lastName: 'Doe',
    socialName: 'John Doe',
    identificationNumber: '123.456.789-00',
    numberAd: '123',
    successUrl: 'https://example.com/success',
    zipCode: '01001-000',
  };
  expect(validateOrderPayload(validPayload)).toBe(true);
});

test('validateOrderPayload rejeita objeto inválido', () => {
  const invalidPayload = {
    priceId: 1,
    phoneCode: '11',
    phoneNumber: '999998888',
    // email está faltando
    complement: 'Apt 101',
    modality: 'online',
    orderDate: '2023-05-15T10:00:00Z',
    scheduledDate: '2023-05-20',
    scheduledPeriod: 'morning',
    personTypeId: 1,
    firstName: 'John',
    lastName: 'Doe',
    socialName: 'John Doe',
    identificationNumber: '123.456.789-00',
    numberAd: '123',
    successUrl: 'https://example.com/success',
    zipCode: '01001-000',
  };
  expect(validateOrderPayload(invalidPayload)).toBe(false);
});
