import {
  Service,
  ServicePrice,
  ServiceProvider,
  validateService,
  validateServicePrice,
  validateServiceProvider,
} from '@/src/app/_interfaces/service-type';

describe('Service Type Validation', () => {
  describe('validateServiceProvider', () => {
    it('should validate a valid service provider', () => {
      const validProvider: ServiceProvider = {
        id: 1,
        name: 'Test Provider',
        imageUrl: 'https://example.com/image.jpg',
        providerUrl: 'https://example.com',
        description: 'Test description',
      };

      expect(validateServiceProvider(validProvider)).toBe(true);
    });

    it('should reject null or undefined', () => {
      expect(validateServiceProvider(null)).toBe(false);
      expect(validateServiceProvider(undefined)).toBe(false);
    });

    it('should reject non-object values', () => {
      expect(validateServiceProvider('string')).toBe(false);
      expect(validateServiceProvider(123)).toBe(false);
      expect(validateServiceProvider(true)).toBe(false);
      expect(validateServiceProvider([])).toBe(false);
    });

    it('should reject objects missing required properties', () => {
      // Missing id
      expect(
        validateServiceProvider({
          name: 'Test Provider',
          imageUrl: 'https://example.com/image.jpg',
          providerUrl: 'https://example.com',
          description: 'Test description',
        })
      ).toBe(false);

      // Missing name
      expect(
        validateServiceProvider({
          id: 1,
          imageUrl: 'https://example.com/image.jpg',
          providerUrl: 'https://example.com',
          description: 'Test description',
        })
      ).toBe(false);

      // Missing imageUrl
      expect(
        validateServiceProvider({
          id: 1,
          name: 'Test Provider',
          providerUrl: 'https://example.com',
          description: 'Test description',
        })
      ).toBe(false);

      // Missing providerUrl
      expect(
        validateServiceProvider({
          id: 1,
          name: 'Test Provider',
          imageUrl: 'https://example.com/image.jpg',
          description: 'Test description',
        })
      ).toBe(false);

      // Missing description
      expect(
        validateServiceProvider({
          id: 1,
          name: 'Test Provider',
          imageUrl: 'https://example.com/image.jpg',
          providerUrl: 'https://example.com',
        })
      ).toBe(false);
    });

    it('should reject objects with properties of wrong type', () => {
      // id is not a number
      expect(
        validateServiceProvider({
          id: '1',
          name: 'Test Provider',
          imageUrl: 'https://example.com/image.jpg',
          providerUrl: 'https://example.com',
          description: 'Test description',
        })
      ).toBe(false);

      // name is not a string
      expect(
        validateServiceProvider({
          id: 1,
          name: 123,
          imageUrl: 'https://example.com/image.jpg',
          providerUrl: 'https://example.com',
          description: 'Test description',
        })
      ).toBe(false);

      // imageUrl is not a string
      expect(
        validateServiceProvider({
          id: 1,
          name: 'Test Provider',
          imageUrl: 123,
          providerUrl: 'https://example.com',
          description: 'Test description',
        })
      ).toBe(false);

      // providerUrl is not a string
      expect(
        validateServiceProvider({
          id: 1,
          name: 'Test Provider',
          imageUrl: 'https://example.com/image.jpg',
          providerUrl: 123,
          description: 'Test description',
        })
      ).toBe(false);

      // description is not a string
      expect(
        validateServiceProvider({
          id: 1,
          name: 'Test Provider',
          imageUrl: 'https://example.com/image.jpg',
          providerUrl: 'https://example.com',
          description: 123,
        })
      ).toBe(false);
    });

    // Test each branch of the validation function
    it('should test each branch of the validation function', () => {
      // Test with null
      expect(validateServiceProvider(null)).toBe(false);

      // Test with non-object
      expect(validateServiceProvider('string')).toBe(false);

      // Test with missing properties
      expect(validateServiceProvider({})).toBe(false);

      // Test with invalid id
      expect(
        validateServiceProvider({
          id: 'invalid',
          name: 'Test Provider',
          imageUrl: 'https://example.com/image.jpg',
          providerUrl: 'https://example.com',
          description: 'Test description',
        })
      ).toBe(false);

      // Test with invalid name
      expect(
        validateServiceProvider({
          id: 1,
          name: 123,
          imageUrl: 'https://example.com/image.jpg',
          providerUrl: 'https://example.com',
          description: 'Test description',
        })
      ).toBe(false);

      // Test with invalid imageUrl
      expect(
        validateServiceProvider({
          id: 1,
          name: 'Test Provider',
          imageUrl: 123,
          providerUrl: 'https://example.com',
          description: 'Test description',
        })
      ).toBe(false);

      // Test with invalid providerUrl
      expect(
        validateServiceProvider({
          id: 1,
          name: 'Test Provider',
          imageUrl: 'https://example.com/image.jpg',
          providerUrl: 123,
          description: 'Test description',
        })
      ).toBe(false);

      // Test with invalid description
      expect(
        validateServiceProvider({
          id: 1,
          name: 'Test Provider',
          imageUrl: 'https://example.com/image.jpg',
          providerUrl: 'https://example.com',
          description: 123,
        })
      ).toBe(false);

      // Test with valid object
      expect(
        validateServiceProvider({
          id: 1,
          name: 'Test Provider',
          imageUrl: 'https://example.com/image.jpg',
          providerUrl: 'https://example.com',
          description: 'Test description',
        })
      ).toBe(true);

      // Test with undefined
      expect(validateServiceProvider(undefined)).toBe(false);
    });
  });

  describe('validateServicePrice', () => {
    it('should validate a valid service price', () => {
      const validPrice: ServicePrice = {
        priceId: 1,
        originalPrice: 100,
        discountPrice: 20,
        finalPrice: 80,
      };

      expect(validateServicePrice(validPrice)).toBe(true);
    });

    it('should reject null or undefined', () => {
      expect(validateServicePrice(null)).toBe(false);
      expect(validateServicePrice(undefined)).toBe(false);
    });

    it('should reject non-object values', () => {
      expect(validateServicePrice('string')).toBe(false);
      expect(validateServicePrice(123)).toBe(false);
      expect(validateServicePrice(true)).toBe(false);
      expect(validateServicePrice([])).toBe(false);
    });

    it('should reject objects missing required properties', () => {
      // Missing priceId
      expect(
        validateServicePrice({
          originalPrice: 100,
          discountPrice: 20,
          finalPrice: 80,
        })
      ).toBe(false);

      // Missing originalPrice
      expect(
        validateServicePrice({
          priceId: 1,
          discountPrice: 20,
          finalPrice: 80,
        })
      ).toBe(false);

      // Missing discountPrice
      expect(
        validateServicePrice({
          priceId: 1,
          originalPrice: 100,
          finalPrice: 80,
        })
      ).toBe(false);

      // Missing finalPrice
      expect(
        validateServicePrice({
          priceId: 1,
          originalPrice: 100,
          discountPrice: 20,
        })
      ).toBe(false);
    });

    it('should reject objects with properties of wrong type', () => {
      // priceId is not a number
      expect(
        validateServicePrice({
          priceId: '1',
          originalPrice: 100,
          discountPrice: 20,
          finalPrice: 80,
        })
      ).toBe(false);

      // originalPrice is not a number
      expect(
        validateServicePrice({
          priceId: 1,
          originalPrice: '100',
          discountPrice: 20,
          finalPrice: 80,
        })
      ).toBe(false);

      // discountPrice is not a number
      expect(
        validateServicePrice({
          priceId: 1,
          originalPrice: 100,
          discountPrice: '20',
          finalPrice: 80,
        })
      ).toBe(false);

      // finalPrice is not a number
      expect(
        validateServicePrice({
          priceId: 1,
          originalPrice: 100,
          discountPrice: 20,
          finalPrice: '80',
        })
      ).toBe(false);
    });

    // Test each branch of the validation function
    it('should test each branch of the validation function', () => {
      // Test with null
      expect(validateServicePrice(null)).toBe(false);

      // Test with non-object
      expect(validateServicePrice('string')).toBe(false);

      // Test with missing properties
      expect(validateServicePrice({})).toBe(false);

      // Test with invalid priceId
      expect(
        validateServicePrice({
          priceId: 'invalid',
          originalPrice: 100,
          discountPrice: 20,
          finalPrice: 80,
        })
      ).toBe(false);

      // Test with invalid originalPrice
      expect(
        validateServicePrice({
          priceId: 1,
          originalPrice: 'invalid',
          discountPrice: 20,
          finalPrice: 80,
        })
      ).toBe(false);

      // Test with invalid discountPrice
      expect(
        validateServicePrice({
          priceId: 1,
          originalPrice: 100,
          discountPrice: 'invalid',
          finalPrice: 80,
        })
      ).toBe(false);

      // Test with invalid finalPrice
      expect(
        validateServicePrice({
          priceId: 1,
          originalPrice: 100,
          discountPrice: 20,
          finalPrice: 'invalid',
        })
      ).toBe(false);

      // Test with valid object
      expect(
        validateServicePrice({
          priceId: 1,
          originalPrice: 100,
          discountPrice: 20,
          finalPrice: 80,
        })
      ).toBe(true);

      // Test with undefined
      expect(validateServicePrice(undefined)).toBe(false);
    });
  });

  describe('validateService', () => {
    const validProvider: ServiceProvider = {
      id: 1,
      name: 'Test Provider',
      imageUrl: 'https://example.com/image.jpg',
      providerUrl: 'https://example.com',
      description: 'Test description',
    };

    const validPrice: ServicePrice = {
      priceId: 1,
      originalPrice: 100,
      discountPrice: 20,
      finalPrice: 80,
    };

    const validService: Service = {
      id: 1,
      slug: 'test-service',
      name: 'Test Service',
      description: 'Test description',
      imageUrl: 'https://example.com/image.jpg',
      status: 'active',
      provider: validProvider,
      price: validPrice,
      availableIn: ['São Paulo', 'Rio de Janeiro'],
      details: ['Detail 1', 'Detail 2'],
      serviceLimits: 'No limits',
      keywords: ['test', 'service'],
      termsConditionsUrl: 'https://example.com/terms',
      preparations: 'No preparations needed',
    };

    it('should validate a valid service', () => {
      expect(validateService(validService)).toBe(true);
    });

    it('should reject null or undefined', () => {
      expect(validateService(null)).toBe(false);
      expect(validateService(undefined)).toBe(false);
    });

    it('should reject non-object values', () => {
      expect(validateService('string')).toBe(false);
      expect(validateService(123)).toBe(false);
      expect(validateService(true)).toBe(false);
      expect(validateService([])).toBe(false);
    });

    it('should reject objects missing required properties', () => {
      // Test a few key properties

      // Missing id
      const missingId = {
        ...validService,
        id: undefined as unknown as number,
      };
      expect(validateService(missingId)).toBe(false);

      // Missing name
      const missingName = {
        ...validService,
        name: undefined as unknown as string,
      };
      expect(validateService(missingName)).toBe(false);

      // Missing provider
      const missingProvider = {
        ...validService,
        provider: undefined as unknown as ServiceProvider,
      };
      expect(validateService(missingProvider)).toBe(false);

      // Missing price
      const missingPrice = {
        ...validService,
        price: undefined as unknown as ServicePrice,
      };
      expect(validateService(missingPrice)).toBe(false);
    });

    it('should reject objects with properties of wrong type', () => {
      // id is not a number
      const invalidId = { ...validService, id: '1' };
      expect(validateService(invalidId)).toBe(false);

      // name is not a string
      const invalidName = { ...validService, name: 123 };
      expect(validateService(invalidName)).toBe(false);

      // availableIn is not an array
      const invalidAvailableIn = { ...validService, availableIn: 'São Paulo' };
      expect(validateService(invalidAvailableIn)).toBe(false);

      // details is not an array
      const invalidDetails = { ...validService, details: 'Detail 1' };
      expect(validateService(invalidDetails)).toBe(false);

      // keywords is not an array
      const invalidKeywords = { ...validService, keywords: 'test' };
      expect(validateService(invalidKeywords)).toBe(false);
    });

    it('should validate a service with valid optional properties', () => {
      const serviceWithOptionalProps = {
        ...validService,
        categoryName: 'Test Category',
        subcategoryName: 'Test Subcategory',
        categorySlug: 'test-category',
        subcategorySlug: 'test-subcategory',
      };

      expect(validateService(serviceWithOptionalProps)).toBe(true);

      // Test with undefined optional properties explicitly
      const serviceWithUndefinedProps = {
        ...validService,
        categoryName: undefined,
        subcategoryName: undefined,
        categorySlug: undefined,
        subcategorySlug: undefined,
      };

      expect(validateService(serviceWithUndefinedProps)).toBe(true);
    });

    it('should reject a service with invalid optional properties', () => {
      // categoryName is not a string
      const invalidCategoryName = {
        ...validService,
        categoryName: 123,
      };
      expect(validateService(invalidCategoryName)).toBe(false);

      // subcategoryName is not a string
      const invalidSubcategoryName = {
        ...validService,
        subcategoryName: 123,
      };
      expect(validateService(invalidSubcategoryName)).toBe(false);

      // categorySlug is not a string
      const invalidCategorySlug = {
        ...validService,
        categorySlug: 123,
      };
      expect(validateService(invalidCategorySlug)).toBe(false);

      // subcategorySlug is not a string
      const invalidSubcategorySlug = {
        ...validService,
        subcategorySlug: 123,
      };
      expect(validateService(invalidSubcategorySlug)).toBe(false);
    });

    it('should reject a service with invalid provider', () => {
      const invalidProvider = {
        ...validService,
        provider: {
          // Missing required properties
          id: 1,
          name: 'Test Provider',
        },
      };
      expect(validateService(invalidProvider)).toBe(false);
    });

    it('should reject a service with non-object provider', () => {
      const invalidProvider = {
        ...validService,
        provider: 'not an object',
      };
      expect(validateService(invalidProvider)).toBe(false);
    });

    it('should reject a service with null provider', () => {
      const invalidProvider = {
        ...validService,
        provider: null,
      };
      expect(validateService(invalidProvider)).toBe(false);
    });

    it('should reject a service with invalid price', () => {
      const invalidPrice = {
        ...validService,
        price: {
          // Missing required properties
          priceId: 1,
          originalPrice: 100,
        },
      };
      expect(validateService(invalidPrice)).toBe(false);
    });

    it('should reject a service with non-object price', () => {
      const invalidPrice = {
        ...validService,
        price: 'not an object',
      };
      expect(validateService(invalidPrice)).toBe(false);
    });

    it('should reject a service with null price', () => {
      const invalidPrice = {
        ...validService,
        price: null,
      };
      expect(validateService(invalidPrice)).toBe(false);
    });

    it('should reject a service with non-array availableIn', () => {
      const invalidAvailableIn = {
        ...validService,
        availableIn: 'São Paulo', // Not an array
      };
      expect(validateService(invalidAvailableIn)).toBe(false);
    });

    it('should reject a service with non-array details', () => {
      const invalidDetails = {
        ...validService,
        details: 'Detail 1', // Not an array
      };
      expect(validateService(invalidDetails)).toBe(false);
    });

    it('should reject a service with non-array keywords', () => {
      const invalidKeywords = {
        ...validService,
        keywords: 'test', // Not an array
      };
      expect(validateService(invalidKeywords)).toBe(false);
    });

    it('should reject a service with non-string serviceLimits', () => {
      const invalidServiceLimits = {
        ...validService,
        serviceLimits: 123, // Not a string
      };
      expect(validateService(invalidServiceLimits)).toBe(false);
    });

    it('should reject a service with non-string termsConditionsUrl', () => {
      const invalidTermsConditionsUrl = {
        ...validService,
        termsConditionsUrl: 123, // Not a string
      };
      expect(validateService(invalidTermsConditionsUrl)).toBe(false);
    });

    it('should reject a service with non-string preparations', () => {
      const invalidPreparations = {
        ...validService,
        preparations: 123, // Not a string
      };
      expect(validateService(invalidPreparations)).toBe(false);
    });

    it('should reject a service with non-string slug', () => {
      const invalidSlug = {
        ...validService,
        slug: 123, // Not a string
      };
      expect(validateService(invalidSlug)).toBe(false);
    });

    it('should reject a service with non-string description', () => {
      const invalidDescription = {
        ...validService,
        description: 123, // Not a string
      };
      expect(validateService(invalidDescription)).toBe(false);
    });

    it('should reject a service with non-string imageUrl', () => {
      const invalidImageUrl = {
        ...validService,
        imageUrl: 123, // Not a string
      };
      expect(validateService(invalidImageUrl)).toBe(false);
    });

    it('should reject a service with non-string status', () => {
      const invalidStatus = {
        ...validService,
        status: 123, // Not a string
      };
      expect(validateService(invalidStatus)).toBe(false);
    });

    // Test each branch of the validation function
    it('should test each branch of the validation function', () => {
      // Test with null
      expect(validateService(null)).toBe(false);

      // Test with undefined
      expect(validateService(undefined)).toBe(false);

      // Test with non-object
      expect(validateService('string')).toBe(false);
      expect(validateService(42)).toBe(false);
      expect(validateService(true)).toBe(false);
      expect(validateService([])).toBe(false);

      // Test with missing properties
      expect(validateService({})).toBe(false);

      // Test with invalid id
      expect(
        validateService({
          ...validService,
          id: 'invalid',
        })
      ).toBe(false);

      // Test with invalid slug
      expect(
        validateService({
          ...validService,
          slug: 123,
        })
      ).toBe(false);

      // Test with invalid name
      expect(
        validateService({
          ...validService,
          name: 123,
        })
      ).toBe(false);

      // Test with invalid description
      expect(
        validateService({
          ...validService,
          description: 123,
        })
      ).toBe(false);

      // Test with invalid imageUrl
      expect(
        validateService({
          ...validService,
          imageUrl: 123,
        })
      ).toBe(false);

      // Test with invalid status
      expect(
        validateService({
          ...validService,
          status: 123,
        })
      ).toBe(false);

      // Test with invalid availableIn
      expect(
        validateService({
          ...validService,
          availableIn: 'invalid',
        })
      ).toBe(false);

      // Test with invalid details
      expect(
        validateService({
          ...validService,
          details: 'invalid',
        })
      ).toBe(false);

      // Test with invalid serviceLimits
      expect(
        validateService({
          ...validService,
          serviceLimits: 123,
        })
      ).toBe(false);

      // Test with invalid keywords
      expect(
        validateService({
          ...validService,
          keywords: 'invalid',
        })
      ).toBe(false);

      // Test with invalid termsConditionsUrl
      expect(
        validateService({
          ...validService,
          termsConditionsUrl: 123,
        })
      ).toBe(false);

      // Test with invalid preparations
      expect(
        validateService({
          ...validService,
          preparations: 123,
        })
      ).toBe(false);

      // Test with invalid provider
      expect(
        validateService({
          ...validService,
          provider: 'invalid',
        })
      ).toBe(false);

      // Test with invalid price
      expect(
        validateService({
          ...validService,
          price: 'invalid',
        })
      ).toBe(false);

      // Test with null provider
      expect(
        validateService({
          ...validService,
          provider: null,
        })
      ).toBe(false);

      // Test with null price
      expect(
        validateService({
          ...validService,
          price: null,
        })
      ).toBe(false);

      // Test with invalid optional properties
      expect(
        validateService({
          ...validService,
          categoryName: 123,
        })
      ).toBe(false);

      expect(
        validateService({
          ...validService,
          subcategoryName: 123,
        })
      ).toBe(false);

      // Test with invalid categorySlug
      expect(
        validateService({
          ...validService,
          categorySlug: 123,
        })
      ).toBe(false);

      // Test with invalid subcategorySlug
      expect(
        validateService({
          ...validService,
          subcategorySlug: 123,
        })
      ).toBe(false);

      // Test with valid service
      expect(validateService(validService)).toBe(true);

      expect(
        validateService({
          ...validService,
          categorySlug: 123,
        })
      ).toBe(false);

      expect(
        validateService({
          ...validService,
          subcategorySlug: 123,
        })
      ).toBe(false);

      // Test with valid object
      expect(validateService(validService)).toBe(true);
    });

    it('should test validateServiceProvider with invalid provider', () => {
      // Create a provider that will fail validation
      const invalidProvider = {
        id: 1,
        name: 'Test Provider',
        imageUrl: 'https://example.com/image.jpg',
        providerUrl: 'https://example.com',
        // Missing description
      };

      // Create a service with this invalid provider
      const serviceWithInvalidProvider = {
        ...validService,
        provider: invalidProvider,
      };

      expect(validateService(serviceWithInvalidProvider)).toBe(false);
    });

    it('should test validateServicePrice with invalid price', () => {
      // Create a price that will fail validation
      const invalidPrice = {
        priceId: 1,
        originalPrice: 100,
        discountPrice: 20,
        // Missing finalPrice
      };

      // Create a service with this invalid price
      const serviceWithInvalidPrice = {
        ...validService,
        price: invalidPrice,
      };

      expect(validateService(serviceWithInvalidPrice)).toBe(false);
    });

    it('should handle edge cases in validateService', () => {
      // Test with empty arrays
      const serviceWithEmptyArrays = {
        ...validService,
        availableIn: [],
        details: [],
        keywords: [],
      };
      expect(validateService(serviceWithEmptyArrays)).toBe(true);

      // Test with empty strings
      const serviceWithEmptyStrings = {
        ...validService,
        slug: '',
        name: '',
        description: '',
        imageUrl: '',
        status: '',
        serviceLimits: '',
        termsConditionsUrl: '',
        preparations: '',
      };
      expect(validateService(serviceWithEmptyStrings)).toBe(true);

      // Test with zero values
      const serviceWithZeroValues = {
        ...validService,
        id: 0,
        provider: {
          ...validProvider,
          id: 0,
        },
        price: {
          ...validPrice,
          priceId: 0,
          originalPrice: 0,
          discountPrice: 0,
          finalPrice: 0,
        },
      };
      expect(validateService(serviceWithZeroValues)).toBe(true);
    });
  });
});
