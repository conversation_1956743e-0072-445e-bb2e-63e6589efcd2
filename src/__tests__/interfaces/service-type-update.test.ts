import { ServiceType } from '@/src/app/_interfaces/service';
import { Service, ServiceCategory } from '@/src/app/_interfaces/service-type';

describe('Updated Service Interfaces', () => {
  it('should correctly handle details as string array in Service interface', () => {
    const service: Service = {
      id: 1,
      slug: 'test-service',
      name: 'Test Service',
      description: 'Test description',
      imageUrl: 'https://example.com/image.jpg',
      status: 'ATIVO',
      provider: {
        id: 1,
        name: 'Test Provider',
        imageUrl: 'https://example.com/provider.jpg',
        providerUrl: 'https://example.com/provider',
        description: 'Provider description',
      },
      price: {
        priceId: 1,
        originalPrice: 100,
        discountPrice: 10,
        finalPrice: 90,
      },
      availableIn: [],
      details: ['Detail 1', 'Detail 2'],
      serviceLimits: 'Service limits',
      keywords: ['keyword1', 'keyword2'],
      termsConditionsUrl: 'https://example.com/terms',
      preparations: 'Preparations',
    };

    // If this compiles, the interface is correct
    expect(service.details).toEqual(['Detail 1', 'Detail 2']);
    expect(Array.isArray(service.details)).toBe(true);
  });

  it('should correctly handle details as string array in ServiceType interface', () => {
    const serviceType: ServiceType = {
      id: 1,
      slug: 'test-service',
      name: 'Test Service',
      description: 'Test description',
      imageUrl: 'https://example.com/image.jpg',
      status: 'ATIVO',
      provider: {
        id: 1,
        name: 'Test Provider',
        imageUrl: 'https://example.com/provider.jpg',
        providerUrl: 'https://example.com/provider',
        description: 'Provider description',
      },
      price: {
        priceId: 1,
        originalPrice: 100,
        discountPrice: 10,
        finalPrice: 90,
      },
      availableIn: [],
      details: ['Detail 1', 'Detail 2'],
      serviceLimits: 'Service limits',
      keywords: ['keyword1', 'keyword2'],
      termsConditionsUrl: 'https://example.com/terms',
      preparations: 'Preparations',
      categoryName: 'Category',
      categorySlug: 'category',
      subcategoryName: 'Subcategory',
      subcategorySlug: 'subcategory',
    };

    // If this compiles, the interface is correct
    expect(serviceType.details).toEqual(['Detail 1', 'Detail 2']);
    expect(Array.isArray(serviceType.details)).toBe(true);
  });

  it('should correctly handle the new response structure', () => {
    const response: { categories: ServiceCategory[] } = {
      categories: [
        {
          id: 6,
          name: 'Chaveiro',
          slug: 'chaveiro',
          subcategories: [
            {
              id: 3,
              name: 'Chaveiro',
              slug: 'chaveiro',
              services: [
                {
                  id: 2,
                  slug: 'chaveiro-abertura-fechadura-europ-assistance',
                  name: 'Abertura de fechadura simples ou tetra',
                  description:
                    'Abertura profissional de fechaduras simples ou tetra com segurança e eficiência. Serviço rápido, especializado e sem complicação!',
                  imageUrl:
                    '/images/servicos/europ/chaveiro/chaveiro_abertura_fechadura_simples_tetra.webp',
                  status: 'ATIVO',
                  provider: {
                    id: 2,
                    name: 'EUROP ASSISTANCE',
                    imageUrl: '/images/logo_europ.png',
                    providerUrl: 'https://www.europ-assistance.com.br',
                    description:
                      'Conte com profissionais especializados e mais de 60 anos de experiência.',
                  },
                  price: {
                    priceId: 82,
                    originalPrice: 320.0,
                    discountPrice: 63.51,
                    finalPrice: 256.49,
                  },
                  availableIn: [],
                  details: [
                    '- O serviço inclui a preparação do local abertura de uma (1) fechadura do tipo simples ou tetra teste de funcionamento e organização do espaço.',
                  ],
                  serviceLimits:
                    '- O serviço **não inclui** a abertura, cópia ou confecção de chaves dos tipos _Gorja_, _Multiponto_, _MulT-Lock_, nem de _fechaduras de cartão, digitais ou tubular_; \r\n- Também não cobre a _troca de segredo do cilindro_, _abertura de cofres_, _fechaduras blindadas (simples ou tetra)_;\r\n- Casos de força maior ou gastos não previstos nesta cobertura também não estão incluídos.',
                  keywords: ['fechadura', 'troca', 'chaveiro'],
                  termsConditionsUrl:
                    'https://gn-ecommerce-files-smoke.s3.us-east-1.amazonaws.com/general_conditions/chaveiro/abertura_de_fechadura/abertura_de_fechadura_simples_ou_tetra.pdf',
                  preparations:
                    '- **Necessária a presença de um responsável maior de 18 anos.** Caso contrário, será preciso reagendar pelo telefone _0800 202 4011_ da **_Central de Atendimento da Europ Assistance_**; \r\n- Caso seja necessária a compra de peças, elas devem ser providenciadas ou pagas diretamente ao prestador. A _Europ Assistance_ não se responsabiliza por esses custos nem pela garantia das peças, apenas pela execução do serviço.',
                },
              ],
            },
          ],
        },
      ],
    };

    // If this compiles, the interface is correct
    expect(response.categories[0].subcategories[0].services[0].details).toEqual([
      '- O serviço inclui a preparação do local abertura de uma (1) fechadura do tipo simples ou tetra teste de funcionamento e organização do espaço.',
    ]);
    expect(Array.isArray(response.categories[0].subcategories[0].services[0].details)).toBe(true);
  });
});
