import {
  validatePaymentResult,
  validateCheckoutFormProps,
  validateAddressFormProps,
  validatePersonalInfoFormProps,
  validateScheduleFormProps,
  validateServiceSummaryProps,
  validateCheckoutPageProps,
  validateCheckoutPageClientProps,
} from '@/src/app/_interfaces/checkout';

// Mock para CheckoutFormSchema
jest.mock('@/src/app/_utils/formValidation', () => ({
  CheckoutFormSchema: {},
}));

test('validatePaymentResult valida corretamente', () => {
  const validResult = {
    success: true,
    transactionId: 'tx-123456',
  };
  expect(validatePaymentResult(validResult)).toBe(true);
});

test('validatePaymentResult rejeita objeto inválido', () => {
  const invalidResult = {
    success: true,
    // transactionId está faltando
  };
  expect(validatePaymentResult(invalidResult)).toBe(false);
});

test('validateCheckoutFormProps valida corretamente com provider', () => {
  const validProps = {
    service: {
      /* mock de ServiceType */
    },
    provider: {
      /* mock de Provider */
    },
    onSubmit: async () => {},
  };
  expect(validateCheckoutFormProps(validProps)).toBe(true);
});

test('validateCheckoutFormProps valida corretamente com provider null', () => {
  const validProps = {
    service: {
      /* mock de ServiceType */
    },
    provider: null,
    onSubmit: async () => {},
  };
  expect(validateCheckoutFormProps(validProps)).toBe(true);
});

test('validateCheckoutFormProps rejeita objeto inválido', () => {
  const invalidProps = {
    service: {
      /* mock de ServiceType */
    },
    // provider está faltando
    onSubmit: async () => {},
  };
  expect(validateCheckoutFormProps(invalidProps)).toBe(false);
});

test('validateAddressFormProps valida corretamente com focusedBlock string', () => {
  const validProps = {
    service: {
      /* mock de ServiceType */
    },
    isStateSelectVisible: true,
    setIsStateSelectOpen: () => {},
    focusedBlock: 'address',
    setFocusedBlock: () => {},
  };
  expect(validateAddressFormProps(validProps)).toBe(true);
});

test('validateAddressFormProps valida corretamente com focusedBlock null', () => {
  const validProps = {
    service: {
      /* mock de ServiceType */
    },
    isStateSelectVisible: true,
    setIsStateSelectOpen: () => {},
    focusedBlock: null,
    setFocusedBlock: () => {},
  };
  expect(validateAddressFormProps(validProps)).toBe(true);
});

test('validateAddressFormProps rejeita objeto inválido', () => {
  const invalidProps = {
    service: {
      /* mock de ServiceType */
    },
    isStateSelectVisible: true,
    // setIsStateSelectOpen está faltando
    focusedBlock: 'address',
    setFocusedBlock: () => {},
  };
  expect(validateAddressFormProps(invalidProps)).toBe(false);
});

test('validatePersonalInfoFormProps valida corretamente com focusedBlock string', () => {
  const validProps = {
    focusedBlock: 'personal',
    setFocusedBlock: () => {},
  };
  expect(validatePersonalInfoFormProps(validProps)).toBe(true);
});

test('validatePersonalInfoFormProps valida corretamente com focusedBlock null', () => {
  const validProps = {
    focusedBlock: null,
    setFocusedBlock: () => {},
  };
  expect(validatePersonalInfoFormProps(validProps)).toBe(true);
});

test('validatePersonalInfoFormProps rejeita objeto inválido', () => {
  const invalidProps = {
    focusedBlock: 'personal',
    // setFocusedBlock está faltando
  };
  expect(validatePersonalInfoFormProps(invalidProps)).toBe(false);
});

test('validateScheduleFormProps valida corretamente', () => {
  const validProps = {
    isDatePickerVisible: true,
    setIsDatePickerVisible: () => {},
    focusedBlock: 'schedule',
    setFocusedBlock: () => {},
  };
  expect(validateScheduleFormProps(validProps)).toBe(true);
});

test('validateScheduleFormProps valida corretamente com focusedBlock null', () => {
  const validProps = {
    isDatePickerVisible: true,
    setIsDatePickerVisible: () => {},
    focusedBlock: null,
    setFocusedBlock: () => {},
  };
  expect(validateScheduleFormProps(validProps)).toBe(true);
});

test('validateScheduleFormProps rejeita objeto inválido', () => {
  const invalidProps = {
    // isDatePickerVisible está faltando
    setIsDatePickerVisible: () => {},
    focusedBlock: 'schedule',
    setFocusedBlock: () => {},
  };
  expect(validateScheduleFormProps(invalidProps)).toBe(false);
});

test('validateServiceSummaryProps valida corretamente com provider', () => {
  const validProps = {
    service: {
      /* mock de ServiceType */
    },
    provider: {
      /* mock de Provider */
    },
    className: 'summary-class',
  };
  expect(validateServiceSummaryProps(validProps)).toBe(true);
});

test('validateServiceSummaryProps valida corretamente com provider null', () => {
  const validProps = {
    service: {
      /* mock de ServiceType */
    },
    provider: null,
    className: 'summary-class',
  };
  expect(validateServiceSummaryProps(validProps)).toBe(true);
});

test('validateServiceSummaryProps valida corretamente sem className', () => {
  const validProps = {
    service: {
      /* mock de ServiceType */
    },
    provider: {
      /* mock de Provider */
    },
  };
  expect(validateServiceSummaryProps(validProps)).toBe(true);
});

test('validateServiceSummaryProps rejeita objeto inválido', () => {
  const invalidProps = {
    service: {
      /* mock de ServiceType */
    },
    // provider está faltando
    className: 'summary-class',
  };
  expect(validateServiceSummaryProps(invalidProps)).toBe(false);
});

test('validateCheckoutPageProps valida corretamente', () => {
  const validProps = {
    params: {
      slug: 'test-service',
    },
  };
  expect(validateCheckoutPageProps(validProps)).toBe(true);
});

test('validateCheckoutPageProps rejeita objeto inválido', () => {
  const invalidProps = {
    params: {
      // slug está faltando
    },
  };
  expect(validateCheckoutPageProps(invalidProps)).toBe(false);
});

test('validateCheckoutPageClientProps valida corretamente', () => {
  const validProps = {
    slug: 'test-service',
    initialServiceData: {
      /* mock de ServiceType */
    },
  };
  expect(validateCheckoutPageClientProps(validProps)).toBe(true);
});

test('validateCheckoutPageClientProps rejeita objeto inválido', () => {
  const invalidProps = {
    slug: 'test-service',
    // initialServiceData está faltando
  };
  expect(validateCheckoutPageClientProps(invalidProps)).toBe(false);
});
