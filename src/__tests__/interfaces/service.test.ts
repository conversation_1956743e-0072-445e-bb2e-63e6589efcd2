import type { ServiceType } from '@/src/app/_interfaces/service';
import {
  validateService,
  validateServicePrice,
  validateServiceProvider,
} from '@/src/app/_interfaces/service-type';

test('validateServiceProvider valida corretamente', () => {
  const validProvider = {
    id: 1,
    name: 'Provider Test',
    imageUrl: 'https://example.com/image.jpg',
    providerUrl: 'https://example.com/provider',
    description: 'Test description',
  };
  expect(validateServiceProvider(validProvider)).toBe(true);
});

test('validateServiceProvider rejeita objeto inválido', () => {
  const invalidProvider = {
    id: 1,
    name: 'Provider Test',
    // imageUrl está faltando
    providerUrl: 'https://example.com/provider',
    description: 'Test description',
  };
  expect(validateServiceProvider(invalidProvider)).toBe(false);
});

test('validateServicePrice valida corretamente', () => {
  const validPrice = {
    priceId: 1,
    originalPrice: 100,
    discountPrice: 20,
    finalPrice: 80,
  };
  expect(validateServicePrice(validPrice)).toBe(true);
});

test('validateServicePrice rejeita objeto inválido', () => {
  const invalidPrice = {
    priceId: 1,
    originalPrice: 100,
    // discountPrice está faltando
    finalPrice: 80,
  };
  expect(validateServicePrice(invalidPrice)).toBe(false);
});

test('ServiceType interface can be used for a service object', () => {
  // Create an object that conforms to the ServiceType interface
  const service: ServiceType = {
    id: 1,
    slug: 'test-service',
    name: 'Test Service',
    description: 'This is a test service',
    imageUrl: 'https://example.com/image.jpg',
    status: 'active',
    provider: {
      id: 1,
      name: 'Provider Test',
      imageUrl: 'https://example.com/provider.jpg',
      providerUrl: 'https://example.com/provider',
      description: 'Provider description',
    },
    price: {
      priceId: 1,
      originalPrice: 100,
      discountPrice: 20,
      finalPrice: 80,
    },
    availableIn: ['SP', 'RJ'],
    details: ['Detail 1', 'Detail 2'],
    serviceLimits: 'No limits',
    keywords: ['keyword1', 'keyword2'],
    termsConditionsUrl: 'https://example.com/terms',
    preparations: 'Preparation instructions',
  };

  // If the object conforms to the interface, this will pass
  expect(service).toBeTruthy();
  expect(service.id).toBe(1);
  expect(service.slug).toBe('test-service');
  expect(service.price.finalPrice).toBe(80);
});

test('validateService valida corretamente', () => {
  const validService = {
    id: 1,
    slug: 'test-service',
    name: 'Test Service',
    description: 'This is a test service',
    imageUrl: 'https://example.com/image.jpg',
    status: 'active',
    provider: {
      id: 1,
      name: 'Provider Test',
      imageUrl: 'https://example.com/provider.jpg',
      providerUrl: 'https://example.com/provider',
      description: 'Provider description',
    },
    price: {
      priceId: 1,
      originalPrice: 100,
      discountPrice: 20,
      finalPrice: 80,
    },
    availableIn: ['SP', 'RJ'],
    details: ['Detail 1', 'Detail 2'],
    serviceLimits: 'No limits',
    keywords: ['keyword1', 'keyword2'],
    termsConditionsUrl: 'https://example.com/terms',
    preparations: 'Preparation instructions',
  };
  expect(validateService(validService)).toBe(true);
});

test('validateService rejeita objeto inválido', () => {
  const invalidService = {
    id: 1,
    slug: 'test-service',
    name: 'Test Service',
    // description está faltando
    imageUrl: 'https://example.com/image.jpg',
    status: 'active',
    provider: {
      id: 1,
      name: 'Provider Test',
      imageUrl: 'https://example.com/provider.jpg',
      providerUrl: 'https://example.com/provider',
      description: 'Provider description',
    },
    price: {
      priceId: 1,
      originalPrice: 100,
      discountPrice: 20,
      finalPrice: 80,
    },
    availableIn: ['SP', 'RJ'],
    details: ['Detail 1', 'Detail 2'],
    serviceLimits: 'No limits',
    keywords: ['keyword1', 'keyword2'],
    termsConditionsUrl: 'https://example.com/terms',
    preparations: 'Preparation instructions',
  };
  expect(validateService(invalidService)).toBe(false);
});
