import { validateProvider, validateTestimonial } from '@/src/app/_interfaces/provider';

test('validateTestimonial valida corretamente', () => {
  const validTestimonial = {
    id: '1',
    author: '<PERSON>',
    location: 'São Paulo, SP',
    content: 'Great service, very professional!',
    rating: 5,
    order: 1,
  };
  expect(validateTestimonial(validTestimonial)).toBe(true);
});

test('validateTestimonial rejeita objeto inválido', () => {
  const invalidTestimonial = {
    id: '1',
    author: '<PERSON>',
    // location está faltando
    content: 'Great service, very professional!',
    rating: 5,
    order: 1,
  };
  expect(validateTestimonial(invalidTestimonial)).toBe(false);
});

test('validateProvider valida corretamente', () => {
  const validProvider = {
    id: '1',
    name: 'Test Provider',
    description: 'This is a test provider',
    imageUrl: 'https://example.com/provider.jpg',
    testimonials: [
      {
        id: '1',
        author: '<PERSON>',
        location: 'São Paulo, SP',
        content: 'Great service!',
        rating: 5,
        order: 1,
      },
    ],
    providerUrl: 'https://example.com/provider',
  };
  expect(validateProvider(validProvider)).toBe(true);
});

test('validateProvider valida corretamente com array vazio de testimonials', () => {
  const validProvider = {
    id: '1',
    name: 'Test Provider',
    description: 'This is a test provider',
    imageUrl: 'https://example.com/provider.jpg',
    testimonials: [],
    providerUrl: 'https://example.com/provider',
  };
  expect(validateProvider(validProvider)).toBe(true);
});

test('validateProvider rejeita objeto inválido', () => {
  const invalidProvider = {
    id: '1',
    name: 'Test Provider',
    description: 'This is a test provider',
    // imageUrl está faltando
    testimonials: [],
    providerUrl: 'https://example.com/provider',
  };
  expect(validateProvider(invalidProvider)).toBe(false);
});

test('validateProvider rejeita testimonial inválido', () => {
  const invalidProvider = {
    id: '1',
    name: 'Test Provider',
    description: 'This is a test provider',
    imageUrl: 'https://example.com/provider.jpg',
    testimonials: [
      {
        id: '1',
        // author está faltando
        location: 'São Paulo, SP',
        content: 'Great service!',
        rating: 5,
        order: 1,
      },
    ],
    providerUrl: 'https://example.com/provider',
  };
  expect(validateProvider(invalidProvider)).toBe(false);
});
