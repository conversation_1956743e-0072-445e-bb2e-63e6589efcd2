import * as interfaces from '@/src/app/_interfaces';
import * as checkoutInterfaces from '@/src/app/_interfaces/checkout';
import * as serviceInterfaces from '@/src/app/_interfaces/service';

describe('Interface exports', () => {
  it('should export all interfaces', () => {
    // Check if all interfaces are exported from index.ts
    expect(Object.keys(interfaces).length).toBeGreaterThan(0);

    // Verify that interfaces from each module are included

    for (const key in checkoutInterfaces) {
      if (Object.prototype.hasOwnProperty.call(checkoutInterfaces, key)) {
        expect(interfaces).toHaveProperty(key);
      }
    }

    for (const key in serviceInterfaces) {
      if (Object.prototype.hasOwnProperty.call(serviceInterfaces, key)) {
        expect(interfaces).toHaveProperty(key);
      }
    }

    // We would check all the other modules similarly, but this is enough to test the exports
  });
});
