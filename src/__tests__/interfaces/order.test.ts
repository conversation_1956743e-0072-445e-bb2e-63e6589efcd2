import {
  validateApiOrderResponse,
  validateOrderService,
  validateOrderServiceProvider,
  validateOrderServicePrice,
  validateOrderAppointment,
  validateOrderPayment,
  validateOrderCustomer,
  validateOrderAddress,
} from '@/src/app/_interfaces/order';

test('validateOrderServiceProvider valida corretamente', () => {
  const validProvider = {
    id: 1,
    name: 'Test Provider',
    imageUrl: 'https://example.com/provider.jpg',
    providerUrl: 'https://example.com/provider',
    description: 'Provider description',
  };
  expect(validateOrderServiceProvider(validProvider)).toBe(true);
});

test('validateOrderServicePrice valida corretamente', () => {
  const validPrice = {
    priceId: 1,
    originalPrice: 100,
    discountPrice: 20,
    finalPrice: 80,
  };
  expect(validateOrderServicePrice(validPrice)).toBe(true);
});

test('validateOrderService valida corretamente', () => {
  const validService = {
    id: 1,
    slug: 'test-service',
    name: 'Test Service',
    description: 'This is a test service',
    imageUrl: 'https://example.com/image.jpg',
    status: 'active',
    termsConditionsUrl: 'https://example.com/terms',
    preparations: 'Preparation instructions',
    provider: {
      id: 1,
      name: 'Test Provider',
      imageUrl: 'https://example.com/provider.jpg',
      providerUrl: 'https://example.com/provider',
      description: 'Provider description',
    },
    price: {
      priceId: 1,
      originalPrice: 100,
      discountPrice: 20,
      finalPrice: 80,
    },
    availableIn: ['SP', 'RJ'],
    details: ['Detail 1', 'Detail 2'],
    serviceLimits: 'No limits',
    keywords: ['keyword1', 'keyword2'],
  };
  expect(validateOrderService(validService)).toBe(true);
});

test('validateOrderAppointment valida corretamente', () => {
  const validAppointment = {
    date: '2023-05-15',
    period: 'morning',
  };
  expect(validateOrderAppointment(validAppointment)).toBe(true);
});

test('validateOrderPayment valida corretamente', () => {
  const validPayment = {
    method: 'credit_card',
    totalPaid: 80,
  };
  expect(validateOrderPayment(validPayment)).toBe(true);
});

test('validateOrderCustomer valida corretamente', () => {
  const validCustomer = {
    customerId: 123,
    fullName: 'John Doe',
    phone: '11999998888',
    email: '<EMAIL>',
    document: '123.456.789-00',
  };
  expect(validateOrderCustomer(validCustomer)).toBe(true);
});

test('validateOrderAddress valida corretamente', () => {
  const validAddress = {
    numberAd: 123,
    street: 'Test Street',
    neighborhood: 'Test Neighborhood',
    cityName: 'São Paulo',
    uf: 'SP',
    zipCode: '01001-000',
    complement: 'Apt 101',
  };
  expect(validateOrderAddress(validAddress)).toBe(true);
});

test('validateApiOrderResponse valida corretamente', () => {
  const validOrder = {
    service: {
      id: 1,
      slug: 'test-service',
      name: 'Test Service',
      description: 'This is a test service',
      imageUrl: 'https://example.com/image.jpg',
      status: 'active',
      termsConditionsUrl: 'https://example.com/terms',
      preparations: 'Preparation instructions',
      provider: {
        id: 1,
        name: 'Test Provider',
        imageUrl: 'https://example.com/provider.jpg',
        providerUrl: 'https://example.com/provider',
        description: 'Provider description',
      },
      price: {
        priceId: 1,
        originalPrice: 100,
        discountPrice: 20,
        finalPrice: 80,
      },
      availableIn: ['SP', 'RJ'],
      details: ['Detail 1', 'Detail 2'],
      serviceLimits: 'No limits',
      keywords: ['keyword1', 'keyword2'],
    },
    appointment: {
      date: '2023-05-15',
      period: 'morning',
    },
    payment: {
      method: 'credit_card',
      totalPaid: 80,
    },
    customer: {
      customerId: 123,
      fullName: 'John Doe',
      phone: '11999998888',
      email: '<EMAIL>',
      document: '123.456.789-00',
    },
    address: {
      numberAd: 123,
      street: 'Test Street',
      neighborhood: 'Test Neighborhood',
      cityName: 'São Paulo',
      uf: 'SP',
      zipCode: '01001-000',
      complement: 'Apt 101',
    },
  };
  expect(validateApiOrderResponse(validOrder)).toBe(true);
});

test('validateApiOrderResponse rejeita objeto inválido', () => {
  const invalidOrder = {
    service: {
      id: 1,
      slug: 'test-service',
      name: 'Test Service',
      description: 'This is a test service',
      imageUrl: 'https://example.com/image.jpg',
      status: 'active',
      termsConditionsUrl: 'https://example.com/terms',
      preparations: 'Preparation instructions',
      provider: {
        id: 1,
        name: 'Test Provider',
        imageUrl: 'https://example.com/provider.jpg',
        providerUrl: 'https://example.com/provider',
        description: 'Provider description',
      },
      price: {
        priceId: 1,
        originalPrice: 100,
        discountPrice: 20,
        finalPrice: 80,
      },
      availableIn: ['SP', 'RJ'],
      details: ['Detail 1', 'Detail 2'],
      serviceLimits: 'No limits',
      keywords: ['keyword1', 'keyword2'],
    },
    // appointment está faltando
    payment: {
      method: 'credit_card',
      totalPaid: 80,
    },
    customer: {
      customerId: 123,
      fullName: 'John Doe',
      phone: '11999998888',
      email: '<EMAIL>',
      document: '123.456.789-00',
    },
    address: {
      numberAd: 123,
      street: 'Test Street',
      neighborhood: 'Test Neighborhood',
      cityName: 'São Paulo',
      uf: 'SP',
      zipCode: '01001-000',
      complement: 'Apt 101',
    },
  };
  expect(validateApiOrderResponse(invalidOrder)).toBe(false);
});
