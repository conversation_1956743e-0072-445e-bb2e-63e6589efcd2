import { middleware } from '@/src/middleware';

// Mock de next/server
jest.mock('next/server', () => ({
  NextRequest: jest.fn().mockImplementation((url) => ({
    nextUrl: {
      pathname: url || '/',
    },
    headers: {
      get: jest.fn(),
      set: jest.fn(),
    },
  })),
  NextResponse: {
    next: jest.fn().mockImplementation(() => ({
      headers: {
        set: jest.fn(),
      },
    })),
  },
}));

// Importar depois do mock para usar a versão mockada
import { NextRequest, NextResponse } from 'next/server';

// Mock do process.env para testes
const originalEnv = process.env;

describe('Middleware', () => {
  let mockRequest: any;
  let mockResponse: any;

  beforeEach(() => {
    // Restaurar mocks antes de cada teste
    jest.clearAllMocks();

    // Configurar o mock do request
    mockRequest = {
      nextUrl: {
        pathname: '/',
      },
      headers: {
        get: jest.fn(),
        set: jest.fn(),
      },
    };

    // Configurar o mock da resposta
    mockResponse = {
      headers: {
        set: jest.fn(),
      },
    };

    // Configurar NextResponse.next para retornar o mockResponse
    (NextResponse.next as jest.Mock).mockReturnValue(mockResponse);

    // Definir NODE_ENV para development por padrão
    process.env = { ...originalEnv };
    Object.defineProperty(process.env, 'NODE_ENV', { value: 'development' });
  });

  afterAll(() => {
    // Restaurar process.env original
    process.env = originalEnv;
  });

  it('should set security headers for all requests', () => {
    middleware(mockRequest as NextRequest);

    // Verificar se os headers de segurança foram definidos
    expect(mockResponse.headers.set).toHaveBeenCalledWith('X-Content-Type-Options', 'nosniff');
    expect(mockResponse.headers.set).toHaveBeenCalledWith('X-Frame-Options', 'DENY');
    expect(mockResponse.headers.set).toHaveBeenCalledWith('X-XSS-Protection', '1; mode=block');
  });

  it('should set no-cache headers for API routes in development', () => {
    // Configurar pathname para uma rota de API
    mockRequest.nextUrl.pathname = '/api/services';

    middleware(mockRequest as NextRequest);

    // Verificar headers de cache para API
    expect(mockResponse.headers.set).toHaveBeenCalledWith(
      'Cache-Control',
      'no-store, must-revalidate'
    );
    expect(mockResponse.headers.set).toHaveBeenCalledWith('Pragma', 'no-cache');
    expect(mockResponse.headers.set).toHaveBeenCalledWith('Expires', '0');
  });

  it('should set no-cache headers for API routes in production', () => {
    // Configurar NODE_ENV para production
    Object.defineProperty(process.env, 'NODE_ENV', { value: 'production' });

    // Configurar pathname para uma rota de API
    mockRequest.nextUrl.pathname = '/api/services';

    middleware(mockRequest as NextRequest);

    // Verificar headers de cache para API
    expect(mockResponse.headers.set).toHaveBeenCalledWith(
      'Cache-Control',
      'no-store, must-revalidate'
    );
    expect(mockResponse.headers.set).toHaveBeenCalledWith('Pragma', 'no-cache');
    expect(mockResponse.headers.set).toHaveBeenCalledWith('Expires', '0');
  });

  it('should set no-cache headers for never-cached paths', () => {
    // Testar cada caminho que nunca deve ser cacheado
    const neverCachedPaths = ['/api/health', '/api/checkout', '/api/auth', '/api/user'];

    for (const path of neverCachedPaths) {
      jest.clearAllMocks(); // Limpar mocks entre iterações
      mockRequest.nextUrl.pathname = path;

      middleware(mockRequest as NextRequest);

      // Verificar headers para caminhos que nunca devem ser cacheados
      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'Cache-Control',
        'no-store, must-revalidate'
      );
      expect(mockResponse.headers.set).toHaveBeenCalledWith('Pragma', 'no-cache');
      expect(mockResponse.headers.set).toHaveBeenCalledWith('Expires', '0');
    }
  });

  it('should set appropriate cache headers for static assets in development', () => {
    // Testar cada rota estática
    const staticRoutes = [
      '/static/image.jpg',
      '/_next/static/css/style.css',
      '/images/logo.png',
      '/fonts/font.woff2',
    ];

    for (const route of staticRoutes) {
      jest.clearAllMocks(); // Limpar mocks entre iterações
      mockRequest.nextUrl.pathname = route;

      middleware(mockRequest as NextRequest);

      // Verificar headers para assets estáticos em desenvolvimento
      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'Cache-Control',
        'public, max-age=60, must-revalidate'
      );
    }
  });

  it('should set long cache headers for static assets in production', () => {
    // Configurar NODE_ENV para production
    Object.defineProperty(process.env, 'NODE_ENV', { value: 'production' });

    // Testar cada rota estática
    const staticRoutes = [
      '/static/image.jpg',
      '/_next/static/css/style.css',
      '/images/logo.png',
      '/fonts/font.woff2',
    ];

    for (const route of staticRoutes) {
      jest.clearAllMocks(); // Limpar mocks entre iterações
      mockRequest.nextUrl.pathname = route;

      middleware(mockRequest as NextRequest);

      // Verificar headers para assets estáticos em produção
      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'Cache-Control',
        'public, max-age=60, must-revalidate'
      );
    }
  });

  it('should set appropriate cache headers for file extensions in development', () => {
    // Testar cada extensão de arquivo
    const fileExtensions = [
      'jpg',
      'jpeg',
      'png',
      'gif',
      'ico',
      'svg',
      'webp',
      'css',
      'js',
      'woff',
      'woff2',
      'ttf',
      'eot',
    ];

    for (const ext of fileExtensions) {
      jest.clearAllMocks(); // Limpar mocks entre iterações
      mockRequest.nextUrl.pathname = `/file.${ext}`;

      middleware(mockRequest as NextRequest);

      // Verificar headers para arquivos estáticos em desenvolvimento
      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'Cache-Control',
        'public, max-age=60, must-revalidate'
      );
    }
  });

  it('should set long cache headers for file extensions in production', () => {
    // Configurar NODE_ENV para production
    Object.defineProperty(process.env, 'NODE_ENV', { value: 'production' });

    // Testar cada extensão de arquivo
    const fileExtensions = [
      'jpg',
      'jpeg',
      'png',
      'gif',
      'ico',
      'svg',
      'webp',
      'css',
      'js',
      'woff',
      'woff2',
      'ttf',
      'eot',
    ];

    for (const ext of fileExtensions) {
      jest.clearAllMocks(); // Limpar mocks entre iterações
      mockRequest.nextUrl.pathname = `/file.${ext}`;

      middleware(mockRequest as NextRequest);

      // Verificar headers para arquivos estáticos em produção
      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'Cache-Control',
        'public, max-age=60, must-revalidate'
      );
    }
  });

  it('should set short cache headers for HTML pages in development', () => {
    // Configurar pathname para uma página HTML normal
    mockRequest.nextUrl.pathname = '/about';

    middleware(mockRequest as NextRequest);

    // Verificar headers para páginas HTML em desenvolvimento
    expect(mockResponse.headers.set).toHaveBeenCalledWith(
      'Cache-Control',
      'no-store, must-revalidate'
    );
  });

  it('should set special cache headers for sitemap.xml and robots.txt', () => {
    // Test sitemap.xml
    mockRequest.nextUrl.pathname = '/sitemap.xml';
    middleware(mockRequest as NextRequest);

    expect(mockResponse.headers.set).toHaveBeenCalledWith(
      'Cache-Control',
      'public, max-age=60, s-maxage=300, must-revalidate'
    );

    // Clear mocks and test robots.txt
    jest.clearAllMocks();
    mockRequest.nextUrl.pathname = '/robots.txt';
    middleware(mockRequest as NextRequest);

    expect(mockResponse.headers.set).toHaveBeenCalledWith(
      'Cache-Control',
      'public, max-age=60, s-maxage=300, must-revalidate'
    );
  });

  it('should set revalidation cache headers for HTML pages in production', () => {
    // Configurar NODE_ENV para production
    Object.defineProperty(process.env, 'NODE_ENV', { value: 'production' });

    // Configurar pathname para uma página HTML normal
    mockRequest.nextUrl.pathname = '/about';

    middleware(mockRequest as NextRequest);

    // Verificar headers para páginas HTML em produção
    expect(mockResponse.headers.set).toHaveBeenCalledWith(
      'Cache-Control',
      'no-store, must-revalidate'
    );
  });
});
