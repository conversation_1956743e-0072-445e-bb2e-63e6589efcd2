import { getServices } from '@/src/app/(pages)/layout';
import { axiosInstance } from '@/src/app/_utils';
import { render, screen } from '@testing-library/react';
import { AxiosError } from 'axios';

// Mock the axiosInstance
jest.mock('@/src/app/_utils', () => ({
  axiosInstance: {
    get: jest.fn(),
  },
}));

// Mock the components used in the RootLayout
jest.mock('@/src/app/_components', () => ({
  ClientLayoutWrapper: ({
    children,
    logoPath,
  }: {
    children: React.ReactNode;
    logoPath: string;
  }) => (
    <div data-testid="client-layout-wrapper" data-logo-path={logoPath}>
      {children}
    </div>
  ),
  ErrorDisplay: ({ fullPage, message }: { fullPage?: boolean; message: string }) => (
    <div data-testid="error-display" data-full-page={fullPage}>
      {message}
    </div>
  ),
}));

// Mock the context providers
jest.mock('@/src/app/_context/AnalyticsContext', () => ({
  AnalyticsProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="analytics-provider">{children}</div>
  ),
}));

jest.mock('@/src/app/_context/QueryContext', () => ({
  QueryProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="query-provider">{children}</div>
  ),
}));

jest.mock('@/src/app/_context/ServiceContext', () => ({
  ServiceProvider: ({ children, services }: { children: React.ReactNode; services: any[] }) => (
    <div data-testid="service-provider" data-services={JSON.stringify(services)}>
      {children}
    </div>
  ),
}));

// Mock the Inter font
jest.mock('next/font/google', () => ({
  Inter: jest.fn().mockReturnValue({
    className: 'mocked-inter-class',
  }),
}));

// Mock console.error
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
});

describe('RootLayout', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch services successfully', async () => {
    // Mock successful API response
    const mockServices = [
      { id: 1, name: 'Category 1' },
      { id: 2, name: 'Category 2' },
    ];

    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: { categories: mockServices },
    });

    // Call the getServices function directly
    const result = await getServices();

    // Verify the API was called with the correct parameters
    expect(axiosInstance.get).toHaveBeenCalledWith('/service-type/list', {
      headers: {
        'service-provider': 'EUR',
      },
    });

    // Verify the result
    expect(result).toEqual(mockServices);
  });

  it('should handle API errors correctly', async () => {
    // Mock API error
    const axiosError = new AxiosError('Network Error');
    (axiosInstance.get as jest.Mock).mockRejectedValueOnce(axiosError);

    // Call the getServices function and expect it to throw
    await expect(getServices()).rejects.toThrow('API error: Network Error');

    // Verify the API was called
    expect(axiosInstance.get).toHaveBeenCalled();
  });

  it('should handle non-Axios errors correctly', async () => {
    // Mock a generic error
    const genericError = new Error('Generic Error');
    (axiosInstance.get as jest.Mock).mockRejectedValueOnce(genericError);

    // Call the getServices function and expect it to throw
    await expect(getServices()).rejects.toThrow('Generic Error');

    // Verify the API was called
    expect(axiosInstance.get).toHaveBeenCalled();
  });

  it('should handle empty categories in API response', async () => {
    // Mock API response with no categories
    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: {}, // No categories field
    });

    // Call the getServices function
    const result = await getServices();

    // Verify an empty array is returned
    expect(result).toEqual([]);
  });

  it('should render error display when services fetch fails', async () => {
    // Mock API error
    const axiosError = new AxiosError('Network Error');
    (axiosInstance.get as jest.Mock).mockRejectedValueOnce(axiosError);

    // Create a test component that simulates the RootLayout behavior
    const TestErrorLayout = async () => {
      try {
        await getServices();
        return <div>Success</div>;
      } catch (error) {
        console.error('Failed to fetch services:', error);
        return <div data-testid="error-display">Erro ao carregar serviços</div>;
      }
    };

    // Render the test component
    render(await TestErrorLayout());

    // Verify the error display is shown
    expect(screen.getByTestId('error-display')).toBeInTheDocument();
    expect(screen.getByText('Erro ao carregar serviços')).toBeInTheDocument();
  });
});
