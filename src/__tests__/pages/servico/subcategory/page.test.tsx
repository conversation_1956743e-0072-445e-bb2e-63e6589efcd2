import MyComponent, { generateMetadata } from '@/src/app/(pages)/servicos/[subcategory]/page';
import { render } from '@testing-library/react';

// Mock the components used in the page
jest.mock('@/src/app/_components', () => ({
  ClientServicePage: ({ slug }: { slug: string }) => (
    <div data-testid="client-service-page">Service slug: {slug}</div>
  ),
  ClientRedirectFallback: () => <div data-testid="service-not-found">Service not found</div>,
}));

// Mock the utils for metadata generation
jest.mock('@/src/app/_utils/dynamicMetadata', () => ({
  generateDynamicMetadata: jest.fn().mockImplementation((slug) => {
    if (slug === 'test-service') {
      return {
        title: 'Test Service | GetNinjas + Europ',
        description: 'Test Service Description',
      };
    } else if (slug === '') {
      return {
        title: 'Serviço não encontrado | GetNinjas + Europ',
        description: 'O serviço que você está procurando não foi encontrado.',
      };
    } else {
      return {
        title: 'Test Service | GetNinjas + Europ',
        description: 'Encontre os melhores serviços no GetNinjas.',
      };
    }
  }),
}));

// Mock the serviceTypeApi
jest.mock('@/src/app/_services/serviceTypeApi', () => ({
  serviceTypeApi: {
    getSubcategories: jest
      .fn()
      .mockResolvedValue([
        { subcategory: 'test-subcategory' },
        { subcategory: 'another-subcategory' },
      ]),
  },
}));

describe('Subcategory Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('MyComponent', () => {
    it('renders the ClientServicePage when slug is provided', async () => {
      // Create props with searchParams containing slug
      const props = {
        params: Promise.resolve({ subcategory: 'test-subcategory' }),
        searchParams: Promise.resolve({ slug: 'test-service' }),
      };

      // Render the component
      const { findByTestId } = render(await MyComponent(props));

      // Verify that the ClientServicePage is rendered with the service slug
      const clientPage = await findByTestId('client-service-page');
      expect(clientPage).toBeInTheDocument();
      expect(clientPage.textContent).toContain('Service slug: test-service');
    });

    it('renders the ClientRedirectFallback when no slug is provided', async () => {
      // Create props with empty searchParams
      const props = {
        params: Promise.resolve({ subcategory: 'test-subcategory' }),
        searchParams: Promise.resolve({}),
      };

      // Render the component
      const { findByTestId } = render(await MyComponent(props));

      // Verify that the ClientRedirectFallback component is rendered
      const notFoundComponent = await findByTestId('service-not-found');
      expect(notFoundComponent).toBeInTheDocument();
    });

    it('handles array of slugs correctly', async () => {
      // Create props with searchParams containing an array of slugs
      const props = {
        params: Promise.resolve({ subcategory: 'test-subcategory' }),
        searchParams: Promise.resolve({ slug: ['test-service', 'another-slug'] }),
      };

      // Render the component
      const { findByTestId } = render(await MyComponent(props));

      // Verify that the ClientServicePage is rendered with the first slug
      const clientPage = await findByTestId('client-service-page');
      expect(clientPage).toBeInTheDocument();
      expect(clientPage.textContent).toContain('Service slug: test-service');
    });
  });

  describe('generateMetadata', () => {
    it('returns correct metadata when service is found', async () => {
      // Create props with searchParams containing slug
      const props = {
        params: Promise.resolve({ subcategory: 'test-subcategory' }),
        searchParams: Promise.resolve({ slug: 'test-service' }),
      };

      // Call generateMetadata
      const metadata = await generateMetadata(props);

      // Verify the metadata
      expect(metadata).toEqual({
        title: 'Test Service | GetNinjas + Europ',
        description: 'Test Service Description',
      });
    });

    it('returns default metadata when no slug is provided', async () => {
      // Create props with empty searchParams
      const props = {
        params: Promise.resolve({ subcategory: 'test-subcategory' }),
        searchParams: Promise.resolve({}),
      };

      // Call generateMetadata
      const metadata = await generateMetadata(props);

      // Verify the metadata
      expect(metadata).toEqual({
        title: 'Serviço não encontrado | GetNinjas + Europ',
        description: 'O serviço que você está procurando não foi encontrado.',
      });
    });

    it('handles array of slugs correctly', async () => {
      // Create props with searchParams containing an array of slugs
      const props = {
        params: Promise.resolve({ subcategory: 'test-subcategory' }),
        searchParams: Promise.resolve({ slug: ['test-service', 'another-slug'] }),
      };

      // Call generateMetadata
      const metadata = await generateMetadata(props);

      // Verify the metadata
      expect(metadata).toEqual({
        title: 'Test Service | GetNinjas + Europ',
        description: 'Test Service Description',
      });
    });
  });
});
