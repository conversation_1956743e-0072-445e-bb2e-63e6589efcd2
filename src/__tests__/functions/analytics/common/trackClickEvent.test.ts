import { trackClickEvent } from '@/src/app/_functions/analytics/common/trackClickEvent';

describe('trackClickEvent', () => {
  beforeEach(() => {
    window.cookieConsent = 'granted';
  });
  const originalWindow = { ...window };
  let windowSpy: jest.SpyInstance;

  beforeEach(() => {
    // Clear all mocks between tests
    jest.clearAllMocks();

    // Set up window.dataLayer mock
    window.dataLayer = [];

    // Spy on window property
    windowSpy = jest.spyOn(global, 'window', 'get');
  });

  afterEach(() => {
    // Restore original window object
    window = { ...originalWindow };
    windowSpy.mockRestore();
  });

  it('should push correct data to dataLayer', () => {
    const eventTitle = 'Test Button Click';

    trackClickEvent(eventTitle);

    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0]).toEqual({
      event: 'click',
      title: eventTitle,
      ep: {
        link_id: eventTitle,
      },
    });
  });

  it('should not push to dataLayer if window or dataLayer is not available', () => {
    // For testing purposes, we need to type cast to avoid type errors
    window.dataLayer = undefined as any;

    trackClickEvent('Test Button Click');

    // Should not throw an error
    expect(true).toBe(true);
  });

  it('should initialize dataLayer if not already available', () => {
    // Remove dataLayer and then re-init it as an empty array to match implementation
    delete window.dataLayer;
    window.dataLayer = [];

    const eventTitle = 'Test Button Click';
    trackClickEvent(eventTitle);

    // Should push to dataLayer
    expect(window.dataLayer).toBeDefined();
    expect(window.dataLayer).toHaveLength(1);
  });

  it('should not execute if window is undefined', () => {
    // Mock window as undefined
    windowSpy.mockImplementation(() => undefined);

    // Should not throw an error
    expect(() => trackClickEvent('Test Button Click')).not.toThrow();
  });

  it('should handle dataLayer.push errors gracefully', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Set up dataLayer with a push method that throws an error
    window.dataLayer = [];
    window.dataLayer.push = jest.fn().mockImplementationOnce(() => {
      throw new Error('DataLayer push error');
    });

    // Should not throw an error
    expect(() => trackClickEvent('Test Button Click')).not.toThrow();

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle empty title gracefully', () => {
    trackClickEvent('');

    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0]).toEqual({
      event: 'click',
      title: '',
      ep: {
        link_id: '',
      },
    });
  });

  it('should handle non-string title gracefully', () => {
    // @ts-ignore - Testing with non-string input
    trackClickEvent(123);

    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0]).toEqual({
      event: 'click',
      title: 123,
      ep: {
        link_id: 123,
      },
    });
  });

  it('should handle null title gracefully', () => {
    // @ts-ignore - Testing with null input
    trackClickEvent(null);

    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0]).toEqual({
      event: 'click',
      title: null,
      ep: {
        link_id: null,
      },
    });
  });

  it('should handle cookie_consent_accepted special case', () => {
    // Test the special case for cookie consent acceptance
    trackClickEvent('cookie_consent_accepted');

    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0]).toEqual({
      event: 'click',
      title: 'cookie_consent_accepted',
      ep: {
        link_id: 'cookie_consent_accepted',
      },
      cookieConsent: 'granted',
    });
  });

  it('should handle errors in dataLayer.push for cookie_consent_accepted', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Set up dataLayer with a push method that throws an error
    window.dataLayer = [];
    window.dataLayer.push = jest.fn().mockImplementationOnce(() => {
      throw new Error('DataLayer push error');
    });

    // Should not throw an error
    expect(() => trackClickEvent('cookie_consent_accepted')).not.toThrow();

    // Verify error was logged
    expect(console.error).toHaveBeenCalledWith('Error sending click event:', expect.any(Error));

    // Restore console.error
    console.error = originalConsoleError;
  });
});
