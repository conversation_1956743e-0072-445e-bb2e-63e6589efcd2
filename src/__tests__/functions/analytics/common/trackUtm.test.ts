import { getUtmParams, trackUtm } from '@/src/app/_functions/analytics/common/trackUtm';
import Cookies from 'js-cookie';

jest.mock('js-cookie', () => ({
  set: jest.fn(),
  get: jest.fn(),
}));

describe('trackUtm', () => {
  beforeEach(() => {
    window.cookieConsent = 'granted';
  });
  const originalWindow = { ...window };
  const originalURLSearchParams = global.URLSearchParams;
  let windowSpy: jest.SpyInstance;

  beforeEach(() => {
    // Clear all mocks between tests
    jest.clearAllMocks();

    // Set up window.gtag mock
    window.gtag = jest.fn();

    // Set up window.dataLayer mock
    window.dataLayer = [];

    // Spy on window property
    windowSpy = jest.spyOn(global, 'window', 'get');

    // Reset environment variable to ensure consistent test behavior
    delete process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT;
  });

  afterEach(() => {
    // Restore original window object
    window = { ...originalWindow };
    global.URLSearchParams = originalURLSearchParams;
    windowSpy.mockRestore();
  });

  it('should push correct UTM parameters to dataLayer', () => {
    // Mock URLSearchParams
    const mockURLParams = {
      get: jest.fn((param: string) => {
        const params: Record<string, string> = {
          utm_source: 'test-source',
          utm_medium: 'test-medium',
          utm_campaign: 'test-campaign',
          utm_content: 'test-content',
          utm_term: 'test-term',
        };
        return params[param] || null;
      }),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;

    trackUtm();

    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0]).toEqual({
      event: 'utm_captured',
      utm_data: {
        utm_source: 'test-source',
        utm_medium: 'test-medium',
        utm_campaign: 'test-campaign',
        utm_content: 'test-content',
        utm_term: 'test-term',
      },
    });

    // Test that cookie was set with correct data but don't check the exact string order
    expect(Cookies.set).toHaveBeenCalledWith(
      'utm_params',
      expect.any(String),
      expect.objectContaining({
        expires: 30,
        secure: true,
        sameSite: 'strict',
      })
    );

    // Extract the actual JSON string that was passed
    const cookieCall = (Cookies.set as jest.Mock).mock.calls[0];
    const jsonStr = cookieCall[1];

    // Parse it and verify the content
    const parsedData = JSON.parse(jsonStr);
    expect(parsedData).toEqual({
      utm_source: 'test-source',
      utm_medium: 'test-medium',
      utm_campaign: 'test-campaign',
      utm_content: 'test-content',
      utm_term: 'test-term',
    });
  });

  it('should handle missing UTM parameters', () => {
    // Mock URLSearchParams with partial UTM parameters
    const mockURLParams = {
      get: jest.fn((param: string) => {
        const params: Record<string, string> = {
          utm_source: 'test-source',
          utm_campaign: 'test-campaign',
        };
        return params[param] || null;
      }),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;

    trackUtm();

    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0].utm_data).toEqual({
      utm_source: 'test-source',
      utm_campaign: 'test-campaign',
    });

    // Check the cookie doesn't contain undefined values
    const cookieCall = (Cookies.set as jest.Mock).mock.calls[0];
    const jsonStr = cookieCall[1];

    // Parse it and verify the content
    const parsedData = JSON.parse(jsonStr);
    expect(parsedData).toEqual({
      utm_source: 'test-source',
      utm_campaign: 'test-campaign',
    });
  });

  it('should not push to dataLayer if no UTM parameters exist', () => {
    // Mock URLSearchParams with no UTM parameters
    const mockURLParams = {
      get: jest.fn(() => null),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;

    trackUtm();

    expect(window.dataLayer).toHaveLength(0);
    expect(Cookies.set).not.toHaveBeenCalled();
  });

  it('should call gtag when it exists', () => {
    // Mock URLSearchParams
    const mockURLParams = {
      get: jest.fn((param: string) => {
        const params: Record<string, string> = {
          utm_source: 'test-source',
          utm_medium: 'test-medium',
        };
        return params[param] || null;
      }),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;
    window.gtag = jest.fn();

    trackUtm();

    expect(window.gtag).toHaveBeenCalledWith('event', 'utm_captured', {
      utm_source: 'test-source',
      utm_medium: 'test-medium',
    });
  });

  it('should not execute if window is undefined', () => {
    // Mock window as undefined
    windowSpy.mockImplementation(() => undefined);

    // Should not throw an error
    expect(() => trackUtm()).not.toThrow();
    expect(Cookies.set).not.toHaveBeenCalled();
  });

  it('should return empty object from getUtmParams if no cookie exists', () => {
    (Cookies.get as jest.Mock).mockReturnValue(null);
    const result = getUtmParams();
    expect(result).toEqual({});
  });

  it('should return parsed UTM params from getUtmParams', () => {
    const mockUtmData = {
      utm_source: 'test-source',
      utm_medium: 'test-medium',
    };

    (Cookies.get as jest.Mock).mockReturnValue(JSON.stringify(mockUtmData));

    const result = getUtmParams();
    expect(result).toEqual(mockUtmData);
  });

  it('should handle invalid JSON in cookie', () => {
    (Cookies.get as jest.Mock).mockReturnValue('invalid-json');

    // Should not throw and return empty object
    const result = getUtmParams();
    expect(result).toEqual({});
  });

  it('should handle non-object JSON in cookie', () => {
    (Cookies.get as jest.Mock).mockReturnValue('"string-value"');

    // Should return empty object when parsed value is not an object
    const result = getUtmParams();
    expect(result).toEqual({});
  });

  it('should handle null JSON in cookie', () => {
    (Cookies.get as jest.Mock).mockReturnValue('null');

    // Should return empty object when parsed value is null
    const result = getUtmParams();
    expect(result).toEqual({});
  });

  it('should use homol event name in homol environment', () => {
    // Save original env
    const originalEnv = process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT;

    // Set homol environment
    process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT = 'homol';

    // Mock URLSearchParams with UTM parameters
    const mockURLParams = {
      get: jest.fn((param: string) => {
        return param === 'utm_source' ? 'test-source' : null;
      }),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;

    trackUtm();

    // Check that the homol event name was used
    expect(window.dataLayer[0].event).toBe('utm_captured-homol');

    // Restore original env
    process.env.NEXT_PUBLIC_ANALYTICS_ENVIRONMENT = originalEnv;
  });

  it('should initialize dataLayer if it does not exist', () => {
    // Delete dataLayer
    delete window.dataLayer;

    // Mock URLSearchParams with UTM parameters
    const mockURLParams = {
      get: jest.fn((param: string) => {
        return param === 'utm_source' ? 'test-source' : null;
      }),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;

    trackUtm();

    // Check that dataLayer was initialized
    expect(window.dataLayer).toBeDefined();
    expect(Array.isArray(window.dataLayer)).toBe(true);
    expect(window.dataLayer.length).toBe(1);
  });

  it('should handle case where gtag is not a function', () => {
    // Set gtag to a non-function value
    window.gtag = 'not a function' as any;

    // Mock URLSearchParams with UTM parameters
    const mockURLParams = {
      get: jest.fn((param: string) => {
        return param === 'utm_source' ? 'test-source' : null;
      }),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;

    // Should not throw
    expect(() => trackUtm()).not.toThrow();
  });

  it('should handle errors when setting cookies', () => {
    // Mock Cookies.set to throw an error
    (Cookies.set as jest.Mock).mockImplementationOnce(() => {
      throw new Error('Test cookie error');
    });

    // Mock URLSearchParams with UTM parameters
    const mockURLParams = {
      get: jest.fn((param: string) => {
        return param === 'utm_source' ? 'test-source' : null;
      }),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;

    // Should not throw
    expect(() => trackUtm()).not.toThrow();
  });

  it('should handle errors when pushing to dataLayer', () => {
    // Create a dataLayer with a push method that throws an error
    window.dataLayer = [];
    window.dataLayer.push = jest.fn().mockImplementationOnce(() => {
      throw new Error('Test dataLayer error');
    });

    // Mock URLSearchParams with UTM parameters
    const mockURLParams = {
      get: jest.fn((param: string) => {
        return param === 'utm_source' ? 'test-source' : null;
      }),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;

    // Should not throw
    expect(() => trackUtm()).not.toThrow();
  });

  it('should handle errors when sending event to gtag', () => {
    // Mock gtag to throw an error
    window.gtag = jest.fn().mockImplementationOnce(() => {
      throw new Error('Test gtag error');
    });

    // Mock URLSearchParams with UTM parameters
    const mockURLParams = {
      get: jest.fn((param: string) => {
        return param === 'utm_source' ? 'test-source' : null;
      }),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;

    // Should not throw
    expect(() => trackUtm()).not.toThrow();
  });

  it('should handle non-string values in safeJsonParse', () => {
    // Mock Cookies.get to return a non-string value
    (Cookies.get as jest.Mock).mockReturnValue(123 as any);

    const result = getUtmParams();
    expect(result).toEqual({});
  });

  it('should handle empty string in safeJsonParse', () => {
    // Mock Cookies.get to return an empty string
    (Cookies.get as jest.Mock).mockReturnValue('');

    const result = getUtmParams();
    expect(result).toEqual({});
  });

  it('should handle array value in safeJsonParse', () => {
    // Mock Cookies.get to return a JSON array
    (Cookies.get as jest.Mock).mockReturnValue('[1, 2, 3]');

    const result = getUtmParams();
    expect(result).toEqual({});
  });

  it('should handle utm_banner parameter', () => {
    // Mock URLSearchParams with utm_banner parameter
    const mockURLParams = {
      get: jest.fn((param: string) => {
        const params: Record<string, string> = {
          utm_source: 'test-source',
          utm_banner: 'test-banner',
        };
        return params[param] || null;
      }),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;

    trackUtm();

    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0].utm_data).toEqual({
      utm_source: 'test-source',
      utm_banner: 'test-banner',
    });
  });

  it('should handle dataLayer as non-array', () => {
    // Set dataLayer to a non-array value
    // @ts-ignore - Testing with invalid input
    window.dataLayer = { notAnArray: true };

    // Mock URLSearchParams with UTM parameters
    const mockURLParams = {
      get: jest.fn((param: string) => {
        return param === 'utm_source' ? 'test-source' : null;
      }),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;

    trackUtm();

    // Should reinitialize dataLayer as an array
    expect(Array.isArray(window.dataLayer)).toBe(true);
    expect(window.dataLayer).toHaveLength(1);
  });

  it('should handle dataLayer with no push method', () => {
    // Create a dataLayer with no push method
    // @ts-ignore - Testing with invalid input
    window.dataLayer = [];
    delete window.dataLayer.push;

    // Mock URLSearchParams with UTM parameters
    const mockURLParams = {
      get: jest.fn((param: string) => {
        return param === 'utm_source' ? 'test-source' : null;
      }),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;

    // Should not throw
    expect(() => trackUtm()).not.toThrow();
  });

  it('should handle URLSearchParams constructor error', () => {
    // Mock URLSearchParams constructor to throw an error
    global.URLSearchParams = jest.fn(() => {
      throw new Error('Test URLSearchParams error');
    }) as any;

    // Should not throw
    expect(() => trackUtm()).not.toThrow();
  });

  it('should handle undefined URLSearchParams', () => {
    // Save original URLSearchParams
    const originalURLSearchParams = global.URLSearchParams;

    // Set URLSearchParams to undefined
    // @ts-ignore - Testing with invalid input
    global.URLSearchParams = undefined;

    // Should not throw
    expect(() => trackUtm()).not.toThrow();

    // Restore original URLSearchParams
    global.URLSearchParams = originalURLSearchParams;
  });

  it('should handle error in forEach loop', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Mock URLSearchParams
    const mockURLParams = {
      get: jest.fn().mockImplementation((param) => {
        if (param === 'utm_source') {
          return 'test-source';
        } else if (param === 'utm_medium') {
          throw new Error('Test error in forEach loop');
        }
        return null;
      }),
    };

    global.URLSearchParams = jest.fn(() => mockURLParams) as any;

    // Should not throw
    expect(() => trackUtm()).not.toThrow();

    // Check that error was logged
    expect(console.error).toHaveBeenCalledWith(
      'Error getting UTM parameter utm_medium:',
      expect.any(Error)
    );

    // Check that dataLayer still contains the utm_source
    expect(window.dataLayer[0].utm_data).toEqual({
      utm_source: 'test-source',
    });

    // Restore console.error
    console.error = originalConsoleError;
  });
});
