import * as indexExports from '@/src/app/_functions/analytics/common';
import * as trackUtm from '@/src/app/_functions/analytics/common/trackUtm';

describe('analytics/common/index', () => {
  it('should export all functions from trackUtm', () => {
    // Get all exported functions from trackUtm
    const trackUtmExports = Object.keys(trackUtm);

    // Get all exported functions from index
    const allExports = Object.keys(indexExports);

    // Check that all trackUtm exports are included in index exports
    trackUtmExports.forEach((exportName) => {
      expect(allExports).toContain(exportName);
    });

    // Check that the functions are the same
    trackUtmExports.forEach((exportName) => {
      expect(indexExports[exportName as keyof typeof indexExports]).toBe(
        trackUtm[exportName as keyof typeof trackUtm]
      );
    });
  });
});
