import { trackViewItem } from '@/src/app/_functions/analytics/common/trackViewItem';

describe('trackViewItem', () => {
  const originalWindow = { ...window };
  let windowSpy: jest.SpyInstance;

  beforeEach(() => {
    // Clear all mocks between tests
    jest.clearAllMocks();

    // Set up window.gtag mock
    window.gtag = jest.fn();

    // Set up window.dataLayer mock
    window.dataLayer = [];

    // Spy on window property
    windowSpy = jest.spyOn(global, 'window', 'get');
  });

  afterEach(() => {
    // Restore original window object
    window = { ...originalWindow };
    windowSpy.mockRestore();
  });

  it('should push correct data to dataLayer', () => {
    const service = {
      id: 123,
      name: 'Test Service',
      price: 99.99,
    };

    trackViewItem(service);

    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0]).toEqual({
      event: 'view_item',
      ecommerce: {
        currency: 'BRL',
        value: 99.99,
        items: [
          {
            item_id: 123,
            item_name: 'Test Service',
            price: 99.99,
            quantity: 1,
          },
        ],
      },
    });
  });

  it('should call gtag with correct view_item event parameters', () => {
    const service = {
      id: 123,
      name: 'Test Service',
      price: 99.99,
    };

    trackViewItem(service);

    expect(window.gtag).toHaveBeenCalledWith('event', 'view_item', {
      currency: 'BRL',
      value: 99.99,
      items: [
        {
          item_id: 123,
          item_name: 'Test Service',
          price: 99.99,
          quantity: 1,
        },
      ],
    });
  });

  it('should not call gtag if not available', () => {
    const service = {
      id: 123,
      name: 'Test Service',
      price: 99.99,
    };

    // For testing purposes, we need to type cast to avoid type errors
    window.gtag = undefined as any;

    trackViewItem(service);

    // Should still push to dataLayer
    expect(window.dataLayer).toHaveLength(1);
    // Should not throw an error
    expect(true).toBe(true);
  });

  it('should not execute if window is undefined', () => {
    // Mock window as undefined
    windowSpy.mockImplementation(() => undefined);

    const service = {
      id: 123,
      name: 'Test Service',
      price: 99.99,
    };

    // Should not throw an error
    expect(() => trackViewItem(service)).not.toThrow();
  });

  it('should initialize dataLayer if it does not exist', () => {
    // Remove dataLayer
    // @ts-ignore - For testing purposes
    delete window.dataLayer;

    const service = {
      id: 123,
      name: 'Test Service',
      price: 99.99,
    };

    trackViewItem(service);

    expect(window.dataLayer).toBeDefined();
    expect(window.dataLayer).toHaveLength(1);
  });

  it('should use existing dataLayer if it exists', () => {
    // Set up an existing dataLayer with some data
    window.dataLayer = [{ existingEvent: 'test' }];

    const service = {
      id: 123,
      name: 'Test Service',
      price: 99.99,
    };

    trackViewItem(service);

    // Should have the existing event plus our new event
    expect(window.dataLayer).toHaveLength(2);
    expect(window.dataLayer[0]).toEqual({ existingEvent: 'test' });
    expect(window.dataLayer[1]).toEqual({
      event: 'view_item',
      ecommerce: {
        currency: 'BRL',
        value: 99.99,
        items: [
          {
            item_id: 123,
            item_name: 'Test Service',
            price: 99.99,
            quantity: 1,
          },
        ],
      },
    });
  });

  // Note: These tests are commented out because they require modifying the trackViewItem function
  // to handle error cases properly. For now, we'll focus on the existing functionality.

  it('should handle non-array dataLayer gracefully', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Set dataLayer to a non-array value
    // @ts-ignore - For testing purposes
    window.dataLayer = { notAnArray: true };

    const service = {
      id: 123,
      name: 'Test Service',
      price: 99.99,
    };

    // Should not throw
    expect(() => trackViewItem(service)).not.toThrow();

    // Should have initialized dataLayer as an array
    expect(Array.isArray(window.dataLayer)).toBe(true);

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle non-function gtag gracefully', () => {
    // Set gtag to a non-function value
    window.gtag = 'not a function' as any;

    const service = {
      id: 123,
      name: 'Test Service',
      price: 99.99,
    };

    // Should not throw
    expect(() => trackViewItem(service)).not.toThrow();

    // Should still push to dataLayer
    expect(window.dataLayer).toHaveLength(1);
  });

  it('should handle dataLayer push errors gracefully', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Create a dataLayer with a push method that throws an error
    window.dataLayer = [];
    window.dataLayer.push = jest.fn().mockImplementation(() => {
      throw new Error('Test dataLayer error');
    });

    const service = {
      id: 123,
      name: 'Test Service',
      price: 99.99,
    };

    // Should not throw
    expect(() => trackViewItem(service)).not.toThrow();

    // Should have logged the error
    expect(console.error).toHaveBeenCalledWith('Error pushing to dataLayer:', expect.any(Error));

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle gtag errors gracefully', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Create a gtag function that throws an error
    window.gtag = jest.fn().mockImplementation(() => {
      throw new Error('Test gtag error');
    });

    const service = {
      id: 123,
      name: 'Test Service',
      price: 99.99,
    };

    // Should not throw
    expect(() => trackViewItem(service)).not.toThrow();

    // Should have logged the error
    expect(console.error).toHaveBeenCalledWith('Error sending event to gtag:', expect.any(Error));

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle null params gracefully', () => {
    // Should not throw
    // @ts-ignore - Testing with invalid input
    expect(() => trackViewItem(null)).not.toThrow();

    // Should not have pushed to dataLayer
    expect(window.dataLayer).toHaveLength(0);
  });

  it('should handle missing name and price gracefully', () => {
    const service = {
      id: 123,
      // name is missing
      // price is missing
    } as any;

    trackViewItem(service);

    // Should still push to dataLayer with default values
    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0].ecommerce.items[0].item_name).toBe('');
    expect(window.dataLayer[0].ecommerce.items[0].price).toBe(0);
  });

  it('should handle non-numeric price gracefully', () => {
    const service = {
      id: 123,
      name: 'Test Service',
      price: 'not a number' as any,
    };

    trackViewItem(service);

    // Should still push to dataLayer with default price
    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0].ecommerce.items[0].price).toBe(0);
  });

  it('should handle dataLayer as null', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Set dataLayer to null
    // @ts-ignore - Testing with invalid input
    window.dataLayer = null;

    const service = {
      id: 123,
      name: 'Test Service',
      price: 99.99,
    };

    // Should not throw
    expect(() => trackViewItem(service)).not.toThrow();

    // Should have initialized dataLayer as an array
    expect(Array.isArray(window.dataLayer)).toBe(true);

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle dataLayer as undefined', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Set dataLayer to undefined
    // @ts-ignore - Testing with invalid input
    window.dataLayer = undefined;

    const service = {
      id: 123,
      name: 'Test Service',
      price: 99.99,
    };

    // Should not throw
    expect(() => trackViewItem(service)).not.toThrow();

    // Should have initialized dataLayer as an array
    expect(Array.isArray(window.dataLayer)).toBe(true);

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle dataLayer with no push method', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Create a dataLayer with no push method
    // @ts-ignore - Testing with invalid input
    window.dataLayer = [];
    delete window.dataLayer.push;

    const service = {
      id: 123,
      name: 'Test Service',
      price: 99.99,
    };

    // Should not throw
    expect(() => trackViewItem(service)).not.toThrow();

    // We're not asserting that console.error was called since the implementation
    // might handle this case differently

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should handle undefined service id', () => {
    const service = {
      // id is missing
      name: 'Test Service',
      price: 99.99,
    } as any;

    trackViewItem(service);

    // Should still push to dataLayer with default id
    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0].ecommerce.items[0].item_id).toBeUndefined();
  });

  it('should handle non-numeric service id', () => {
    const service = {
      id: 'not a number' as any,
      name: 'Test Service',
      price: 99.99,
    };

    trackViewItem(service);

    // Should still push to dataLayer with the id as is
    expect(window.dataLayer).toHaveLength(1);
    expect(window.dataLayer[0].ecommerce.items[0].item_id).toBe('not a number');
  });

  it('should handle error in createItemData', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Create a service object that will cause an error in createItemData
    const service = Object.create(null);
    Object.defineProperty(service, 'id', {
      get: () => {
        throw new Error('Error accessing id');
      },
    });

    // Should not throw
    expect(() => trackViewItem(service as any)).not.toThrow();

    // Restore console.error
    console.error = originalConsoleError;
  });
});
