// Import the exported helper functions
import {
  isWindowDefined,
  initializeDataLayer,
  createItemData,
  isGtagAvailable,
} from '@/src/app/_functions/analytics/common/trackViewItem';

describe('trackViewItem helper functions', () => {
  const originalWindow = { ...window };
  let windowSpy: jest.SpyInstance;

  beforeEach(() => {
    // Clear all mocks between tests
    jest.clearAllMocks();

    // Set up window.gtag mock
    window.gtag = jest.fn();

    // Set up window.dataLayer mock
    window.dataLayer = [];

    // Spy on window property
    windowSpy = jest.spyOn(global, 'window', 'get');
  });

  afterEach(() => {
    // Restore original window object
    window = { ...originalWindow };
    windowSpy.mockRestore();
  });

  describe('isWindowDefined', () => {
    it('should return true when window is defined', () => {
      expect(isWindowDefined()).toBe(true);
    });

    it('should return false when window is undefined', () => {
      // Mock window as undefined
      windowSpy.mockImplementation(() => undefined);
      expect(isWindowDefined()).toBe(false);
    });
  });

  describe('initializeDataLayer', () => {
    it('should initialize dataLayer as an empty array if it does not exist', () => {
      // Set dataLayer to undefined
      delete (window as any).dataLayer;

      // Call the function
      initializeDataLayer();

      // Check that dataLayer is now an array
      expect(Array.isArray(window.dataLayer)).toBe(true);
      expect(window.dataLayer.length).toBe(0);
    });

    it('should not modify dataLayer if it already exists as an array', () => {
      // Set dataLayer to an array with existing values
      window.dataLayer = [{ existingEvent: 'previous_event' }];

      // Call the function
      initializeDataLayer();

      // Check that dataLayer still has the existing value
      expect(window.dataLayer.length).toBe(1);
      expect(window.dataLayer[0]).toEqual({ existingEvent: 'previous_event' });
    });

    it('should handle dataLayer as a non-array', () => {
      // Set dataLayer to a non-array value
      window.dataLayer = null as any;

      // Call the function
      initializeDataLayer();

      // Check that dataLayer is now an array
      expect(Array.isArray(window.dataLayer)).toBe(true);
    });
  });

  describe('createItemData', () => {
    it('should create item data with correct properties', () => {
      const params = {
        id: 123,
        name: 'Test',
        price: 10.99,
      };

      const itemData = createItemData(params);

      expect(itemData).toEqual({
        item_id: 123,
        item_name: 'Test',
        price: 10.99,
        quantity: 1,
      });
    });
  });

  describe('isGtagAvailable', () => {
    it('should return true when gtag is a function', () => {
      // Set gtag to a function
      window.gtag = jest.fn();
      expect(isGtagAvailable()).toBe(true);
    });

    it('should return false when gtag is undefined', () => {
      // Set gtag to undefined
      window.gtag = undefined as any;
      expect(isGtagAvailable()).toBe(false);
    });

    it('should return false when gtag is not a function', () => {
      // Set gtag to a non-function
      window.gtag = 'not a function' as any;
      expect(isGtagAvailable()).toBe(false);
    });
  });
});
