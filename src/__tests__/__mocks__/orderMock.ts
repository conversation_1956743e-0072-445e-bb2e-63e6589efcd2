import { ApiOrderResponse } from '@/src/app/_interfaces/order';

export const orderMock: ApiOrderResponse = {
  service: {
    id: 1,
    slug: 'service-test',
    name: 'Service Test',
    description: 'Description of Service Test',
    imageUrl: 'http://example.com/image.jpg',
    status: 'active',
    termsConditionsUrl: 'http://example.com/terms',
    preparations: 'Preparation steps',
    provider: {
      id: 1,
      name: 'Provider Test',
      imageUrl: 'http://example.com/provider.jpg',
      providerUrl: 'http://provider.com',
      description: 'Provider description',
    },
    price: {
      priceId: 123,
      originalPrice: 150.0,
      discountPrice: 120.0,
      finalPrice: 120.0,
    },
    availableIn: ['BR'],
    details: ['Detail 1', 'Detail 2'],
    serviceLimits: 'No limits',
    keywords: ['test', 'service'],
  },
  appointment: {
    date: '2025-04-30',
    period: 'morning',
  },
  payment: {
    method: 'credit_card',
    totalPaid: 120.0,
  },
  customer: {
    customerId: 1,
    fullName: '<PERSON>',
    phone: '123456789',
    email: '<EMAIL>',
    document: '12345678900',
  },
  address: {
    numberAd: 123,
    street: 'Main Street',
    neighborhood: 'Downtown',
    cityName: 'Cityville',
    uf: 'SP',
    zipCode: '12345678',
    complement: 'Apt 101',
  },
};
