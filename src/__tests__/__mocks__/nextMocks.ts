// /**
//  * Mocks para componentes e hooks do Next.js
//  * <PERSON><PERSON><PERSON> mocks são reutilizáveis em diferentes testes
//  */

// // Mock para o componente Image do Next.js
// export const mockNextImage = {
//   __esModule: true,
//   default: ({ src, alt, ...props }: any) => {
//     return <img src={src} alt={alt} {...props} data-testid="next-image" />;
//   },
// };

// // Mock para o componente Link do Next.js
// export const mockNextLink = {
//   __esModule: true,
//   default: ({ href, children, ...props }: any) => (
//     <a href={href} {...props}>{children}</a>
//   ),
// };

// // Mock para o hook usePathname do Next.js
// export const mockUsePathname = () => '/test-path';

// // Mock para o dynamic import do Next.js
// export const mockDynamicImport = () => {
//   const DynamicComponent = (props: any) => {
//     const { children, ...rest } = props;
//     return <div data-testid="dynamic-component" {...rest}>{children}</div>;
//   };
//   DynamicComponent.displayName = 'MockedDynamicComponent';
//   return DynamicComponent;
// };
