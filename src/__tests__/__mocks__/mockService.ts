import { ServiceType } from '@/src/app/_interfaces';

export const mockService: ServiceType = {
  id: 1,
  slug: 'mock-service',
  name: 'Mock Service',
  description: 'Descrição do serviço mock.',
  imageUrl: 'https://example.com/image.png',
  status: 'active',
  provider: {
    id: 10,
    name: 'Mock Provider',
    imageUrl: 'https://example.com/provider.png',
    providerUrl: 'https://example.com',
    description: 'Descrição do provedor mock.',
  },
  price: {
    priceId: 100,
    originalPrice: 200,
    discountPrice: 150,
    finalPrice: 150,
  },
  availableIn: ['SP', 'RJ'],
  details: ['Detalhe 1', 'Detalhe 2'],
  serviceLimits: 'Limite do serviço mock.',
  keywords: ['mock', 'serviço'],
  termsConditionsUrl: 'https://example.com/terms',
  preparations: 'Preparações para o serviço mock.',
  categoryName: 'Saúde',
  categorySlug: 'saude',
  subcategoryName: 'Exames',
  subcategorySlug: 'exames',
};
