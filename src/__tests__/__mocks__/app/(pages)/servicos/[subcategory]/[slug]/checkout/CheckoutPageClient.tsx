'use client';

import { useServiceBySlug } from '@/src/app/_hooks';
import { CheckoutPageClientProps } from '@/src/app/_interfaces';
import { PaymentService } from '@/src/app/_services/payment';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';

export default function CheckoutPageClient({ slug, initialServiceData }: CheckoutPageClientProps) {
  const router = useRouter();
  const [showLoader, setShowLoader] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [formValues, setFormValues] = useState<any>(null);

  // Use the mocked useServiceBySlug hook
  const { service, provider, isLoading, error } = useServiceBySlug(
    slug,
    initialServiceData
      ? {
          service: initialServiceData,
          provider: initialServiceData.provider,
        }
      : undefined
  );

  const handleSubmit = useCallback(
    async (values: any) => {
      // Store form values for retry functionality
      setFormValues(values);

      // Show loader
      setShowLoader(true);

      try {
        // Add service ID and priceId to the values
        const enhancedValues = {
          ...values,
          serviceId: service?.id,
          priceId: service?.price?.priceId,
        };

        // Call the payment service
        const response = await PaymentService.createOrder(enhancedValues);

        if (response.redirectUrl || response.secureUrl) {
          // Redirect to the payment page
          router.push(response.redirectUrl || response.secureUrl);
        } else {
          setShowLoader(false);
          setShowErrorModal(true);
        }
      } catch (_error) {
        setShowLoader(false);
        setShowErrorModal(true);
      }
    },
    [service, router]
  );

  const handleRetry = useCallback(() => {
    if (formValues) {
      setShowErrorModal(false);
      handleSubmit(formValues);
    }
  }, [formValues, handleSubmit]);

  // Show error state
  if (error) {
    return (
      <div data-testid="error-state">
        <h1>Error Loading Service</h1>
        <p>{error}</p>
      </div>
    );
  }

  // Show loading state
  if (isLoading || !service) {
    return <div data-testid="checkout-page-skeleton">Loading checkout...</div>;
  }

  return (
    <div data-testid="checkout-page-client">
      {showLoader && <div data-testid="loader">Loading...</div>}

      <div data-testid="breadcrumb">
        <ol data-testid="breadcrumb-list">
          <li data-testid="breadcrumb-item">
            <a data-testid="breadcrumb-link" href="/">
              Home
            </a>
          </li>
          <span data-testid="breadcrumb-separator">/</span>
          <li data-testid="breadcrumb-item">
            <a data-testid="breadcrumb-link" href={`/servicos/${service.subcategorySlug}`}>
              {service.name}
            </a>
          </li>
          <span data-testid="breadcrumb-separator">/</span>
          <li data-testid="breadcrumb-item">
            <span data-testid="breadcrumb-page">Agendamento</span>
          </li>
        </ol>
      </div>

      <form
        data-testid="checkout-form"
        onSubmit={(e) => {
          e.preventDefault();
          handleSubmit({
            name: 'Test User',
            email: '<EMAIL>',
            phone: '11999999999',
            document: '12345678900',
            zipCode: '01234567',
            street: 'Test Street',
            numberAd: '123',
            complement: 'Apt 456',
            neighborhood: 'Test Neighborhood',
            cityName: 'Test City',
            uf: 'SP',
            scheduleDate: '2023-12-31',
            schedulePeriod: 'morning',
            acceptTerms: true,
          });
        }}
      >
        <div>Service: {service.name}</div>
        <div>Provider: {provider?.name || 'No provider'}</div>
        <button type="submit" data-testid="submit-form">
          Submit
        </button>
      </form>

      {showErrorModal && (
        <div data-testid="error-modal">
          <button onClick={() => setShowErrorModal(false)} data-testid="close-modal">
            Close
          </button>
          <button onClick={handleRetry} data-testid="retry-payment">
            Retry
          </button>
        </div>
      )}
    </div>
  );
}
