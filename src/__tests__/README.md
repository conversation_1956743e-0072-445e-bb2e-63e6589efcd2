# Estrutura de Testes

Esta pasta contém todos os testes automatizados para o projeto de e-commerce frontend. A estrutura de pastas foi organizada para facilitar a navegação, modularização e manutenção dos testes.

## Abordagem de Testes

Utilizamos uma abordagem dupla para testes:

1. **Testes Unitários**: Testam componentes e hooks isoladamente com dependências mockadas
2. **Testes de Integração**: Testam componentes e hooks com dados reais da API

Esta abordagem nos dá o melhor dos dois mundos:

- Testes unitários rápidos e confiáveis para desenvolvimento e CI
- Testes de integração abrangentes para detectar mudanças no contrato da API

## Estrutura de Diretórios

```bash
__tests__/
├── __mocks__/                      # Mocks globais compartilhados
│   ├── fileMock.js                 # Mock para arquivos estáticos
│   ├── nextMocks.ts                # Mocks para componentes/hooks do Next.js
│   └── styleMock.js                # Mock para arquivos CSS/SCSS
│
├── components/                     # Testes para componentes
│   ├── Common/                     # Espelha a estrutura de diretórios src/app/_components/Common
│   │   ├── Header/
│   │   │   ├── Header.test.tsx
│   │   │   ├── DesktopNavigation.test.tsx
│   │   │   └── MobileNavigation.test.tsx
│   │   └── ...
│   ├── Pages/                      # Testes para componentes de página
│   └── Ui/                         # Testes para componentes de UI
│
├── context/                        # Testes para contextos React
│   └── [ContextName].test.tsx
│
├── hooks/                          # Testes para hooks customizados
│   ├── __helpers__/                # Funções auxiliares para testar hooks
│   │   └── renderHook.tsx          # Utilitário para renderizar hooks em testes
│   └── [HookName].test.tsx         # Testes para hooks específicos
│
├── services/                       # Testes para serviços/chamadas de API
│   └── [ServiceName].test.ts
│
├── utils/                          # Testes para funções utilitárias
│   └── [UtilName].test.ts
│
├── fixtures/                       # Dados de teste compartilhados
│   ├── serviceData.ts              # Dados mockados para serviços
│   └── ...
│
├── integration/                    # Testes de integração
│   ├── checkout.test.tsx           # Testes que abrangem múltiplos componentes/funções
│   └── navigation.test.tsx
│
├── __test-utils__/                 # Utilitários compartilhados para testes
│   ├── renderWithProviders.tsx     # Renderização com providers necessários
│   └── testHelpers.ts              # Funções auxiliares para testes
│
└── README.md                       # Documentação sobre a estrutura de testes
```

## Convenções de Nomenclatura

1. **Arquivos de Teste**: Use a convenção `[NomeDoArquivo].test.[ts|tsx]`

   - Exemplos: `Header.test.tsx`, `formatCurrency.test.ts`

2. **Arquivos de Mock**: Use a convenção `[NomeDoMódulo].mock.[ts|js]` ou centralize no diretório `__mocks__`

   - Exemplos: `nextMocks.ts`, `fileMock.js`

3. **Fixtures de Teste**: Use nomes descritivos com o contexto
   - Exemplos: `serviceData.ts`, `orderResponses.ts`

## Boas Práticas

### Organização dos Testes

- **Modularização**: Divida os testes em arquivos separados por componente/funcionalidade
- **Espelhamento**: Mantenha a mesma estrutura de diretórios do código-fonte
- **Agrupamento Lógico**: Use `describe` para agrupar testes relacionados

### Reutilização

- Use os utilitários compartilhados em `__test-utils__/` para evitar duplicação de código
- Utilize os mocks centralizados em `__mocks__/` para elementos comuns (Next.js, imagens, estilos)
- Aproveite as fixtures em `fixtures/` para dados de teste consistentes

### Exemplo de Uso dos Utilitários

```tsx
// Exemplo de teste usando renderWithProviders
import { renderWithProviders } from '@/tests/__test-utils__/renderWithProviders';
import { screen } from '@testing-library/react';
import MyComponent from '@/src/app/_components/MyComponent';

describe('MyComponent', () => {
  it('renders correctly', () => {
    renderWithProviders(<MyComponent />);
    expect(screen.getByText('Texto esperado')).toBeInTheDocument();
  });
});

// Exemplo de teste de hook
import { renderHook, act } from '@/tests/hooks/__helpers__/renderHook';
import { useMyHook } from '@/src/app/_hooks/useMyHook';

describe('useMyHook', () => {
  it('should update state correctly', () => {
    const { result } = renderHook(() => useMyHook());

    act(() => {
      result.current.someFunction();
    });

    expect(result.current.someState).toBe(expectedValue);
  });
});
```

## Como Executar os Testes

- **Todos os testes**: `pnpm test`
- **Testes específicos**: `pnpm test [caminho/para/arquivo.test.tsx]`
- **Testes com cobertura**: `pnpm test:coverage`
- **Testes com watch mode**: `pnpm test --watch`
- **Testes de integração**: `USE_REAL_API=true pnpm test`
- **Testes de integração específicos**: `USE_REAL_API=true pnpm test [caminho/para/arquivo.integration.test.tsx]`

## Estrutura de Arquivos de Teste

Para cada componente ou hook, temos:

- `ComponentName.test.tsx` - Ponto de entrada que importa testes unitários e de integração
- `ComponentName.unit.test.tsx` - Testes unitários com dependências mockadas
- `ComponentName.integration.test.tsx` - Testes de integração com dados reais da API

## Implementação de Testes de Integração

Os testes de integração seguem este padrão:

1. Buscar dados reais da API
2. Usar dados mockados como fallback se a chamada à API falhar
3. Testar a renderização do componente com dados reais
4. Testar o tratamento de erros com erros simulados

Exemplo:

```typescript
describe('Component (Testes de Integração)', () => {
  // Esta variável armazenará nossos dados reais da API
  let dadosReaisApi = null;

  // Buscar dados reais antes de executar os testes
  beforeAll(async () => {
    try {
      console.warn('🌐 Buscando dados reais da API para testes de integração...');
      const response = await fetch('https://api.example.com/data');
      dadosReaisApi = await response.json();
      console.warn(`✅ Dados obtidos com sucesso da API`);
    } catch (error) {
      console.error('❌ Erro ao buscar dados da API:', error);
    }
  });

  it('renderiza com dados reais da API', async () => {
    // Pular teste se não houver dados da API disponíveis
    if (!dadosReaisApi) {
      console.warn('⚠️ Usando dados mockados de fallback para testes');
      dadosReaisApi = criarDadosMockados();
    }

    // Testar com dados reais ou de fallback
    render(<Component data={dadosReaisApi} />);

    // Registrar se estamos usando dados reais ou mockados
    if (dadosReaisApi.id !== 1) {
      console.warn('✅ Teste executando com dados REAIS da API');
    } else {
      console.warn('⚠️ Teste executando com dados MOCKADOS (API indisponível)');
    }
  });
});
```

## Configuração do setupTests.ts

Nosso arquivo `setupTests.ts` está configurado para:

1. Permitir chamadas reais à API para endpoints específicos quando `USE_REAL_API=true`
2. Usar o módulo HTTP nativo do Node.js para contornar restrições de CORS
3. Usar dados mockados como fallback se as chamadas reais à API falharem
4. Fornecer dados mockados realistas para todos os endpoints

## Manutenção e Evolução

Ao adicionar novos testes:

1. Identifique a categoria apropriada (componente, hook, serviço, etc.)
2. Siga a estrutura de pastas existente
3. Reutilize mocks e utilitários sempre que possível
4. Documente casos especiais ou complexos
5. Mantenha os testes focados e independentes

Esta estrutura foi projetada para ser escalável e facilitar a navegação, localização e manutenção dos testes à medida que o projeto cresce.
