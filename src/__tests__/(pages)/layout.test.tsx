/**
 * @jest-environment jsdom
 */

import { render, screen } from '@testing-library/react';
import { AxiosError } from 'axios';

// Mock the components used in the RootLayout
jest.mock('@/src/app/_components', () => ({
  ErrorDisplay: ({ fullPage, message }: { fullPage?: boolean; message: string }) => (
    <div data-testid="error-display" data-full-page={fullPage ? 'true' : 'false'}>
      {message}
    </div>
  ),
}));

// Mock the ClientLayoutWrapper component
jest.mock('@/src/app/_components/Common/DynamicImports/ClientDynamicImports', () => ({
  ClientLayoutWrapper: ({
    children,
    logoPath,
  }: {
    children: React.ReactNode;
    logoPath: string;
  }) => (
    <div data-testid="client-layout-wrapper" data-logo-path={logoPath}>
      {children}
    </div>
  ),
}));

// Mock the context providers
jest.mock('@/src/app/_context/AnalyticsContext', () => ({
  AnalyticsProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="analytics-provider">{children}</div>
  ),
}));

jest.mock('@/src/app/_context/QueryContext', () => ({
  QueryProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="query-provider">{children}</div>
  ),
}));

jest.mock('@/src/app/_context/ServiceContext', () => ({
  ServiceProvider: ({ children, services }: { children: React.ReactNode; services: any[] }) => (
    <div data-testid="service-provider" data-services={JSON.stringify(services)}>
      {children}
    </div>
  ),
}));

// Mock next/font to avoid actual font loading
jest.mock('next/font/google', () => ({
  Inter: jest.fn().mockReturnValue({
    className: 'mock-inter-font',
  }),
}));

// Mock axios for the getServices function
jest.mock('@/src/app/_utils', () => ({
  axiosInstance: {
    get: jest.fn(),
  },
}));

// Mock document.createElement to handle html and body elements
const originalCreateElement = document.createElement;
beforeAll(() => {
  document.createElement = jest.fn((tagName) => {
    if (tagName === 'html' || tagName === 'body') {
      return {
        setAttribute: jest.fn(),
        appendChild: jest.fn(),
        tagName: tagName.toUpperCase(),
      } as unknown as HTMLElement;
    }
    return originalCreateElement.call(document, tagName);
  });
});

afterAll(() => {
  document.createElement = originalCreateElement;
});

// Mock console.error to avoid noise
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
});

describe('RootLayout Full Component Test', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
  });

  it('renders the full layout with services', async () => {
    // Mock successful API response
    const mockServices = [
      { id: 1, name: 'Category 1' },
      { id: 2, name: 'Category 2' },
    ];

    const { axiosInstance } = require('@/src/app/_utils');
    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: { categories: mockServices },
    });

    // Import the RootLayout component
    const { default: RootLayout } = require('@/src/app/(pages)/layout');

    // Render the RootLayout component
    const testContent = <div data-testid="test-content">Test Content</div>;
    render(await RootLayout({ children: testContent }));

    // Check that the layout structure is correct
    expect(screen.getByTestId('query-provider')).toBeInTheDocument();
    expect(screen.getByTestId('service-provider')).toBeInTheDocument();
    expect(screen.getByTestId('client-layout-wrapper')).toBeInTheDocument();
    expect(screen.getByTestId('analytics-provider')).toBeInTheDocument();
    expect(screen.getByTestId('test-content')).toBeInTheDocument();

    // Check that the services are passed to the ServiceProvider
    const serviceProvider = screen.getByTestId('service-provider');
    expect(serviceProvider).toHaveAttribute('data-services', JSON.stringify(mockServices));

    // Check that the logo path exists
    const clientLayoutWrapper = screen.getByTestId('client-layout-wrapper');
    expect(clientLayoutWrapper).toHaveAttribute('data-logo-path');

    // The actual value might vary in different environments, so we just check it's a string
    const logoPath = clientLayoutWrapper.getAttribute('data-logo-path');
    expect(typeof logoPath).toBe('string');
    expect(logoPath?.length).toBeGreaterThan(0);

    // In the test environment, we can't directly access the html, head, body, or script elements
    // because they're mocked. Instead, we'll just check that the component structure is correct.
  });

  it('renders error display when services fetch fails', async () => {
    // Mock API error
    const { axiosInstance } = require('@/src/app/_utils');
    (axiosInstance.get as jest.Mock).mockRejectedValueOnce(new AxiosError('Network Error'));

    // Import the RootLayout component
    const { default: RootLayout } = require('@/src/app/(pages)/layout');

    // Render the RootLayout component
    const testContent = <div data-testid="test-content">Test Content</div>;
    render(await RootLayout({ children: testContent }));

    // Check that the error display is shown
    expect(screen.getByTestId('error-display')).toBeInTheDocument();
    expect(screen.getByText('Erro ao carregar serviços')).toBeInTheDocument();
    expect(screen.getByTestId('error-display')).toHaveAttribute('data-full-page', 'true');

    // Check that the layout components are not rendered
    expect(screen.queryByTestId('query-provider')).not.toBeInTheDocument();
    expect(screen.queryByTestId('service-provider')).not.toBeInTheDocument();
    expect(screen.queryByTestId('client-layout-wrapper')).not.toBeInTheDocument();
    expect(screen.queryByTestId('analytics-provider')).not.toBeInTheDocument();
    expect(screen.queryByTestId('test-content')).not.toBeInTheDocument();
  });

  it('uses custom logo path from environment variable', async () => {
    // Set environment variable
    const originalEnv = process.env;
    process.env = {
      ...originalEnv,
      NEXT_PUBLIC_LOGO_PATH: '/custom-logo.svg',
    };

    // Mock successful API response
    const mockServices = [{ id: 1, name: 'Category 1' }];
    const { axiosInstance } = require('@/src/app/_utils');
    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: { categories: mockServices },
    });

    // Import the RootLayout component
    const { default: RootLayout } = require('@/src/app/(pages)/layout');

    // Render the RootLayout component
    const testContent = <div data-testid="test-content">Test Content</div>;
    render(await RootLayout({ children: testContent }));

    // Check that the logo path exists and matches the environment variable
    const clientLayoutWrapper = screen.getByTestId('client-layout-wrapper');
    expect(clientLayoutWrapper).toHaveAttribute('data-logo-path');

    // The logo path should match the environment variable we set
    const logoPath = clientLayoutWrapper.getAttribute('data-logo-path');
    expect(logoPath).toBe('/custom-logo.svg');

    // Restore environment variables
    process.env = originalEnv;
  });

  it('handles non-Axios errors in getServices', async () => {
    // Import the getServices function
    const { getServices } = require('@/src/app/(pages)/layout');

    // Mock API to throw a non-Axios error
    const { axiosInstance } = require('@/src/app/_utils');
    const genericError = new Error('Generic error');
    (axiosInstance.get as jest.Mock).mockRejectedValueOnce(genericError);

    // Call getServices and expect it to throw the original error
    await expect(getServices()).rejects.toThrow('Generic error');
  });

  it('handles empty response in getServices', async () => {
    // Import the getServices function
    const { getServices } = require('@/src/app/(pages)/layout');

    // Mock API to return empty data
    const { axiosInstance } = require('@/src/app/_utils');
    (axiosInstance.get as jest.Mock).mockResolvedValueOnce({
      data: {},
    });

    // Call getServices and expect it to return an empty array
    const result = await getServices();
    expect(result).toEqual([]);
  });
});
