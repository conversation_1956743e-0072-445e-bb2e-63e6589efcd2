import * as dataExports from '@/src/app/_data';
import { providers } from '@/src/app/_data/providers';
import { ServiceMockData } from '@/src/app/_data/service-type';
import { states } from '@/src/app/_data/states';

describe('Data Index Exports', () => {
  it('should export providers correctly', () => {
    expect(dataExports.providers).toBeDefined();
    expect(dataExports.providers).toEqual(providers);
  });

  it('should export ServiceMockData correctly', () => {
    expect(dataExports.ServiceMockData).toBeDefined();
    expect(dataExports.ServiceMockData).toEqual(ServiceMockData);
  });

  it('should export states correctly', () => {
    expect(dataExports.states).toBeDefined();
    expect(dataExports.states).toEqual(states);
  });

  it('should have the correct number of exports', () => {
    // Count the number of exports from the index file
    const exportCount = Object.keys(dataExports).length;

    // We expect 3 exports: providers, ServiceMockData, and states
    expect(exportCount).toBe(3);
  });

  it('should have valid providers data', () => {
    expect(Array.isArray(dataExports.providers)).toBe(true);
    expect(dataExports.providers.length).toBeGreaterThan(0);

    // Check the structure of the first provider
    const firstProvider = dataExports.providers[0];
    expect(firstProvider).toHaveProperty('id');
    expect(firstProvider).toHaveProperty('name');
    expect(firstProvider).toHaveProperty('description');
    expect(firstProvider).toHaveProperty('imageUrl');
    expect(firstProvider).toHaveProperty('providerUrl');
    expect(firstProvider).toHaveProperty('testimonials');
  });

  it('should have valid ServiceMockData', () => {
    expect(dataExports.ServiceMockData).toHaveProperty('categories');
    expect(Array.isArray(dataExports.ServiceMockData.categories)).toBe(true);
    expect(dataExports.ServiceMockData.categories.length).toBeGreaterThan(0);

    // Check the structure of the first category
    const firstCategory = dataExports.ServiceMockData.categories[0];
    expect(firstCategory).toHaveProperty('id');
    expect(firstCategory).toHaveProperty('name');
    expect(firstCategory).toHaveProperty('slug');
    expect(firstCategory).toHaveProperty('subcategories');
  });

  it('should have valid states data', () => {
    expect(typeof dataExports.states).toBe('object');
    expect(Object.keys(dataExports.states).length).toBeGreaterThan(0);

    // Check some specific states
    expect(dataExports.states.SP).toBe('São Paulo');
    expect(dataExports.states.RJ).toBe('Rio de Janeiro');
    expect(dataExports.states.MG).toBe('Minas Gerais');
  });
});
