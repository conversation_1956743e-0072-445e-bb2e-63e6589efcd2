import { providers } from '@/src/app/_data/providers';

describe('Providers Data', () => {
  it('should have the correct structure', () => {
    expect(providers).toBeDefined();
    expect(Array.isArray(providers)).toBe(true);
    expect(providers.length).toBeGreaterThan(0);

    // Check the first provider
    const firstProvider = providers[0];
    expect(firstProvider).toHaveProperty('id');
    expect(firstProvider).toHaveProperty('name');
    expect(firstProvider).toHaveProperty('description');
    expect(firstProvider).toHaveProperty('imageUrl');
    expect(firstProvider).toHaveProperty('providerUrl');
    expect(firstProvider).toHaveProperty('testimonials');
  });

  it('should have valid testimonials for each provider', () => {
    providers.forEach((provider) => {
      expect(Array.isArray(provider.testimonials)).toBe(true);

      if (provider.testimonials.length > 0) {
        provider.testimonials.forEach((testimonial) => {
          expect(testimonial).toHaveProperty('id');
          expect(testimonial).toHaveProperty('author');
          expect(testimonial).toHaveProperty('location');
          expect(testimonial).toHaveProperty('content');
          expect(testimonial).toHaveProperty('rating');
          expect(testimonial).toHaveProperty('order');

          // Check that rating is between 1 and 5
          expect(testimonial.rating).toBeGreaterThanOrEqual(1);
          expect(testimonial.rating).toBeLessThanOrEqual(5);
        });
      }
    });
  });

  it('should have valid URLs for provider images and links', () => {
    providers.forEach((provider) => {
      // Check that imageUrl is a valid URL format
      expect(provider.imageUrl).toMatch(/^(https?:\/\/|\/)/);

      // Check that providerUrl is a valid URL format
      expect(provider.providerUrl).toMatch(/^(https?:\/\/|\/)/);
    });
  });

  it('should have Europ Assistance as one of the providers', () => {
    const europProvider = providers.find(
      (provider) =>
        provider.name.toLowerCase().includes('europ') ||
        provider.name.toLowerCase().includes('assistance')
    );

    expect(europProvider).toBeDefined();
    if (europProvider) {
      expect(europProvider.name).toContain('Europ');
      expect(europProvider.description).toBeTruthy();
    }
  });
});
