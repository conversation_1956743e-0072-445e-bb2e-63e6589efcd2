import { ServiceMockData } from '@/src/app/_data/service-type';

describe('ServiceMockData', () => {
  it('should have the correct structure', () => {
    expect(ServiceMockData).toBeDefined();
    expect(ServiceMockData).toHaveProperty('categories');
    expect(Array.isArray(ServiceMockData.categories)).toBe(true);
    expect(ServiceMockData.categories.length).toBeGreaterThan(0);
  });

  it('should have valid categories with subcategories', () => {
    ServiceMockData.categories.forEach((category) => {
      expect(category).toHaveProperty('id');
      expect(category).toHaveProperty('name');
      expect(category).toHaveProperty('slug');
      expect(category).toHaveProperty('subcategories');
      expect(Array.isArray(category.subcategories)).toBe(true);
      expect(category.subcategories.length).toBeGreaterThan(0);
    });
  });

  it('should have valid subcategories with services', () => {
    ServiceMockData.categories.forEach((category) => {
      category.subcategories.forEach((subcategory) => {
        expect(subcategory).toHaveProperty('id');
        expect(subcategory).toHaveProperty('name');
        expect(subcategory).toHaveProperty('slug');
        expect(subcategory).toHaveProperty('services');
        expect(Array.isArray(subcategory.services)).toBe(true);
        expect(subcategory.services.length).toBeGreaterThan(0);
      });
    });
  });

  it('should have valid services with required properties', () => {
    ServiceMockData.categories.forEach((category) => {
      category.subcategories.forEach((subcategory) => {
        subcategory.services.forEach((service) => {
          expect(service).toHaveProperty('id');
          expect(service).toHaveProperty('slug');
          expect(service).toHaveProperty('name');
          expect(service).toHaveProperty('description');
          expect(service).toHaveProperty('imageUrl');
          expect(service).toHaveProperty('status');
          expect(service).toHaveProperty('provider');
          expect(service).toHaveProperty('price');

          // Check provider properties
          expect(service.provider).toHaveProperty('id');
          expect(service.provider).toHaveProperty('name');
          expect(service.provider).toHaveProperty('imageUrl');
          expect(service.provider).toHaveProperty('providerUrl');
          expect(service.provider).toHaveProperty('description');

          // Check price properties
          expect(service.price).toHaveProperty('priceId');
          expect(service.price).toHaveProperty('originalPrice');
          expect(service.price).toHaveProperty('discountPrice');
          expect(service.price).toHaveProperty('finalPrice');

          // Check that the final price is correct (original - discount)
          expect(service.price.finalPrice).toBe(
            service.price.originalPrice - service.price.discountPrice
          );
        });
      });
    });
  });

  it('should have Europ Assistance as the provider for all services', () => {
    let hasEuropProvider = false;

    ServiceMockData.categories.forEach((category) => {
      category.subcategories.forEach((subcategory) => {
        subcategory.services.forEach((service) => {
          if (service.provider.name.includes('EUROP ASSISTANCE')) {
            hasEuropProvider = true;
          }
        });
      });
    });

    expect(hasEuropProvider).toBe(true);
  });

  it('should have valid image URLs for all services', () => {
    ServiceMockData.categories.forEach((category) => {
      category.subcategories.forEach((subcategory) => {
        subcategory.services.forEach((service) => {
          // Check that imageUrl is a valid format
          expect(service.imageUrl).toMatch(/^(https?:\/\/|\/)/);

          // Check that provider imageUrl is a valid format
          expect(service.provider.imageUrl).toMatch(/^(https?:\/\/|\/)/);
        });
      });
    });
  });
});
