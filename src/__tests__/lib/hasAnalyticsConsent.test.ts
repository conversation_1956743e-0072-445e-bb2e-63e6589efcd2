// Simple test for hasAnalyticsConsent
const mockWindow = (cookieConsent?: string, analyticsConsent?: boolean) => {
  return {
    ...global.window,
    cookieConsent,
    analyticsConsent,
  };
};

describe('hasAnalyticsConsent', () => {
  let originalWindow: typeof global.window;

  beforeAll(() => {
    // Save the original window object
    originalWindow = global.window;
  });

  afterEach(() => {
    // Restore the original window after each test
    global.window = originalWindow;
  });

  it('should return false when window is undefined (server-side)', () => {
    // @ts-expect-error - We're intentionally removing window for this test
    delete global.window;

    // Dynamically import the module to avoid hoisting issues
    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);
  });

  it('should return true when cookieConsent is "granted"', () => {
    // @ts-expect-error - We're intentionally modifying window for testing
    global.window = mockWindow('granted', false);

    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(true);
  });

  it('should return true when cookieConsent is "granted" regardless of analyticsConsent value', () => {
    // Test with various values for analyticsConsent to ensure the first part of the OR condition is evaluated correctly
    // @ts-expect-error - We're intentionally modifying window for testing
    global.window = mockWindow('granted', null);

    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(true);

    // Try with undefined analyticsConsent
    // @ts-expect-error - We're intentionally modifying window for testing
    global.window = mockWindow('granted', undefined);
    expect(hasAnalyticsConsent()).toBe(true);

    // Try with false analyticsConsent - should still return true because cookieConsent is granted
    // @ts-expect-error - We're intentionally modifying window for testing
    global.window = mockWindow('granted', false);
    expect(hasAnalyticsConsent()).toBe(true);
  });

  it('should return true when analyticsConsent is true', () => {
    // @ts-expect-error - We're intentionally modifying window for testing
    global.window = mockWindow(undefined, true);

    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(true);
  });

  it('should return true when analyticsConsent is true and cookieConsent is not "granted"', () => {
    // Test with various non-granted values for cookieConsent
    // @ts-expect-error - We're intentionally modifying window for testing
    global.window = mockWindow('denied', true);

    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(true);

    // Try with null cookieConsent
    // @ts-expect-error - We're intentionally modifying window for testing
    global.window = mockWindow(null, true);
    expect(hasAnalyticsConsent()).toBe(true);

    // Try with empty string cookieConsent
    // @ts-expect-error - We're intentionally modifying window for testing
    global.window = mockWindow('', true);
    expect(hasAnalyticsConsent()).toBe(true);
  });

  it('should return true when both cookieConsent and analyticsConsent are set', () => {
    // @ts-expect-error - We're intentionally modifying window for testing
    global.window = mockWindow('granted', true);

    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(true);
  });

  it('should return false when neither cookieConsent nor analyticsConsent are set', () => {
    // @ts-expect-error - We're intentionally modifying window for testing
    global.window = mockWindow(undefined, false);

    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);
  });

  it('should return false when cookieConsent is not "granted" and analyticsConsent is false', () => {
    // @ts-expect-error - We're intentionally modifying window for testing
    global.window = mockWindow('denied', false);

    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);
  });

  // --- Additional edge-case tests for branch coverage ---
  it('should return false when cookieConsent is null and analyticsConsent is undefined', () => {
    // @ts-expect-error
    global.window = mockWindow(null as any, undefined as any);
    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);
  });

  it('should return false when cookieConsent is number 1 and analyticsConsent is string "false"', () => {
    // @ts-expect-error
    global.window = mockWindow(1 as any, 'false' as any);
    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);
  });

  it('should return false when cookieConsent is an object and analyticsConsent is an array', () => {
    // @ts-expect-error
    global.window = mockWindow({} as any, [] as any);
    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);
  });

  it('should return false when cookieConsent is "" (empty string) and analyticsConsent is null', () => {
    // @ts-expect-error
    global.window = mockWindow('', null as any);
    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);
  });

  it('should return false when both properties are missing', () => {
    global.window = { ...global.window };
    delete (global.window as any).cookieConsent;
    delete (global.window as any).analyticsConsent;
    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);
  });

  it('should return false when both cookieConsent and analyticsConsent are falsy (0 and empty string)', () => {
    // @ts-expect-error
    global.window = { ...global.window, cookieConsent: 0 as any, analyticsConsent: '' as any };
    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);
  });

  it('should return false when window is explicitly undefined', () => {
    // Save the original window
    const originalWindow = global.window;
    // @ts-expect-error - Testing window undefined case
    global.window = undefined;

    // Dynamically import to ensure fresh module state
    jest.resetModules();
    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');

    expect(hasAnalyticsConsent()).toBe(false);

    // Restore window
    global.window = originalWindow;
  });

  it('should handle window being redefined after being undefined', () => {
    // Save the original window
    const originalWindow = global.window;

    // Set window to undefined
    // @ts-expect-error - Testing window undefined case
    global.window = undefined;

    // Verify it returns false when window is undefined
    jest.resetModules();
    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);

    // Restore window and verify it works normally
    global.window = originalWindow;
    expect(hasAnalyticsConsent()).toBe(false);
  });

  it('should handle window being redefined with cookie consent', () => {
    // Save the original window
    const originalWindow = global.window;

    // Set window to undefined
    // @ts-expect-error - Testing window undefined case
    global.window = undefined;

    // Verify it returns false when window is undefined
    jest.resetModules();
    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);

    // Restore window with cookie consent and verify it works
    global.window = {
      ...originalWindow,
      cookieConsent: 'granted',
      analyticsConsent: undefined,
    } as typeof global.window;
    expect(hasAnalyticsConsent()).toBe(true);

    // Restore original window
    global.window = originalWindow;
  });

  // Test direct assignment of window properties for more explicit branch coverage
  it('should check exactly cookieConsent === "granted" condition', () => {
    jest.resetModules();
    const originalConsole = console.error;
    console.error = jest.fn(); // Mock console.error to avoid noise in tests

    // First test that similar values aren't treated the same
    // @ts-expect-error - Testing with a value that's not 'granted'
    global.window = mockWindow('GRANTED', false); // Uppercase, should not match
    let { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);

    // @ts-expect-error - Testing with a value that's not 'granted'
    global.window = mockWindow(' granted', false); // With space, should not match
    jest.resetModules();
    ({ hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent'));
    expect(hasAnalyticsConsent()).toBe(false);

    // Now check the exact value
    // @ts-expect-error - Testing with the exact string 'granted'
    global.window = mockWindow('granted', false);
    jest.resetModules();
    ({ hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent'));
    expect(hasAnalyticsConsent()).toBe(true);

    console.error = originalConsole; // Restore console.error
  });

  it('should check exactly analyticsConsent === true condition', () => {
    jest.resetModules();

    // First test that similar values aren't treated the same
    // @ts-expect-error - Testing with a truthy value that's not true
    global.window = mockWindow(null, 1); // Truthy but not boolean true
    let { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);

    // @ts-expect-error - Testing with a truthy value that's not true
    global.window = mockWindow(null, 'true'); // String 'true', not boolean
    jest.resetModules();
    ({ hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent'));
    expect(hasAnalyticsConsent()).toBe(false);

    // Now check the exact value
    // @ts-expect-error - Testing with boolean true
    global.window = mockWindow(null, true);
    jest.resetModules();
    ({ hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent'));
    expect(hasAnalyticsConsent()).toBe(true);
  });

  // Test the entire expression logical flow
  it('should verify full logical path of the consent check expression', () => {
    jest.resetModules();

    // Set both conditions to false - should return false
    // @ts-expect-error - Testing with non-matching values
    global.window = mockWindow('denied', false);
    let { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);

    // First condition true, second false - should return true
    // @ts-expect-error - Testing first condition true
    global.window = mockWindow('granted', false);
    jest.resetModules();
    ({ hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent'));
    expect(hasAnalyticsConsent()).toBe(true);

    // First condition false, second true - should return true
    // @ts-expect-error - Testing second condition true
    global.window = mockWindow('denied', true);
    jest.resetModules();
    ({ hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent'));
    expect(hasAnalyticsConsent()).toBe(true);

    // Both conditions true - should return true
    // @ts-expect-error - Testing both conditions true
    global.window = mockWindow('granted', true);
    jest.resetModules();
    ({ hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent'));
    expect(hasAnalyticsConsent()).toBe(true);
  });

  // Test window object with properties but undefined consent values
  it('should handle window with properties but undefined consent values', () => {
    jest.resetModules();

    // Create window with empty/undefined consent values but with other properties
    const windowWithProperties = {
      ...global.window,
      location: { href: 'https://example.com' },
      document: {},
      // Both consent properties are undefined
      cookieConsent: undefined,
      analyticsConsent: undefined,
    };

    // @ts-expect-error - Intentionally modify window for testing
    global.window = windowWithProperties;

    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);
  });

  // Test different edge cases for cookieConsent
  it('should handle edge cases for cookieConsent value', () => {
    jest.resetModules();

    // Test edge case: cookieConsent is not a string but a number
    // @ts-expect-error - Testing with incorrect type
    global.window = mockWindow(123, false);
    let { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false); // Should be false as it's not 'granted'

    // Test edge case: cookieConsent is an empty object
    // @ts-expect-error - Testing with incorrect type
    global.window = mockWindow({}, false);
    jest.resetModules();
    ({ hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent'));
    expect(hasAnalyticsConsent()).toBe(false);

    // Test edge case: cookieConsent is an array
    // @ts-expect-error - Testing with incorrect type
    global.window = mockWindow(['granted'], false);
    jest.resetModules();
    ({ hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent'));
    expect(hasAnalyticsConsent()).toBe(false);
  });

  // Test different edge cases for analyticsConsent
  it('should handle edge cases for analyticsConsent value', () => {
    jest.resetModules();

    // Test edge case: analyticsConsent is not a boolean but a string
    // @ts-expect-error - Testing with incorrect type
    global.window = mockWindow('denied', 'true');
    let { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false); // Should be false as it's not === true

    // Test edge case: analyticsConsent is a truthy value but not true
    // @ts-expect-error - Testing with incorrect type
    global.window = mockWindow('denied', 1);
    jest.resetModules();
    ({ hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent'));
    expect(hasAnalyticsConsent()).toBe(false);
  });

  // Test the specific branch where the window is defined but neither condition is true
  it('should handle the case where window exists but no consent is granted', () => {
    jest.resetModules();

    // Mock window with various non-granting consent values
    // Explicitly using ts-expect-error for type safety bypassing
    // @ts-expect-error - Testing with non-standard types
    global.window = {
      ...global.window,
      cookieConsent: 'not-granted',
      analyticsConsent: false,
    };

    const { hasAnalyticsConsent } = require('@/src/app/_lib/hasAnalyticsConsent');
    expect(hasAnalyticsConsent()).toBe(false);
  });
});
