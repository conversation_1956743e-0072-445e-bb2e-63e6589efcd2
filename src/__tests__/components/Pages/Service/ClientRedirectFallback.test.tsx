import { ClientRedirectFallback } from '@/src/app/_components/Pages/Service/ClientRedirectFallback';
import { render, screen } from '@testing-library/react';

// Mock the components used in ClientRedirectFallback
jest.mock('@/src/app/_components/Common/Testimonial/TestimonialsSkeleton', () => ({
  TestimonialsSkeleton: () => <div data-testid="testimonials-skeleton">Testimonials Skeleton</div>,
}));

jest.mock('@/src/app/_components/Pages/Home/ServicePageSkeleton', () => ({
  ServicePageSkeleton: () => <div data-testid="service-page-skeleton">Service Page Skeleton</div>,
}));

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn().mockReturnValue({
    push: jest.fn(),
  }),
  usePathname: jest.fn(),
  useSearchParams: jest.fn().mockReturnValue({
    has: jest.fn(),
    get: jest.fn(),
  }),
}));

// Mock the ServiceContext
jest.mock('@/src/app/_context/ServiceContext', () => ({
  ServiceContext: {
    Provider: ({ children, _value }) => (
      <div data-testid="service-context-provider">{children}</div>
    ),
  },
  useServiceContext: jest.fn().mockImplementation(() => ({
    services: [],
    isLoading: false,
    error: null,
  })),
}));

// Mock window.location
const originalLocation = window.location;

// Create a custom render function
const renderWithServiceContext = (
  ui,
  contextValue = { services: [], isLoading: false, error: null }
) => {
  // Update the mock implementation for this test
  const { useServiceContext } = require('@/src/app/_context/ServiceContext');
  useServiceContext.mockImplementation(() => contextValue);

  return render(ui);
};

describe('ClientRedirectFallback', () => {
  beforeEach(() => {
    // Mock window.location
    delete window.location;
    window.location = {
      ...originalLocation,
      replace: jest.fn(),
    };

    // Reset mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Restore window.location
    window.location = originalLocation;
  });

  it('renders loading skeletons', () => {
    // Mock usePathname to return a path
    const { usePathname } = require('next/navigation');
    usePathname.mockReturnValue('/servicos/subcategory');

    // Mock useSearchParams to return no slug param
    const { useSearchParams } = require('next/navigation');
    useSearchParams.mockReturnValue({
      has: jest.fn().mockReturnValue(false),
      get: jest.fn(),
    });

    // Mock services context
    const mockServices = [
      {
        id: 1,
        name: 'Category 1',
        slug: 'category-1',
        subcategories: [
          {
            id: 11,
            name: 'Subcategory 1',
            slug: 'subcategory-1',
            services: [
              {
                id: 111,
                name: 'Service 1',
                slug: 'service-1',
              },
            ],
          },
        ],
      },
    ];

    renderWithServiceContext(<ClientRedirectFallback />, {
      services: mockServices,
      isLoading: false,
      error: null,
    });

    // Check that skeletons are rendered
    expect(screen.getByTestId('service-page-skeleton')).toBeInTheDocument();
    expect(screen.getByTestId('testimonials-skeleton')).toBeInTheDocument();
  });

  it('redirects to the first service when no slug param is present', () => {
    // Mock usePathname to return a path with a subcategory
    const { usePathname } = require('next/navigation');
    usePathname.mockReturnValue('/servicos/subcategory-1');

    // Mock useSearchParams to return no slug param
    const { useSearchParams } = require('next/navigation');
    useSearchParams.mockReturnValue({
      has: jest.fn().mockReturnValue(false),
      get: jest.fn(),
    });

    // Mock services context with matching subcategory
    const mockServices = [
      {
        id: 1,
        name: 'Category 1',
        slug: 'category-1',
        subcategories: [
          {
            id: 11,
            name: 'Subcategory 1',
            slug: 'subcategory-1',
            services: [
              {
                id: 111,
                name: 'Service 1',
                slug: 'service-1',
              },
            ],
          },
        ],
      },
    ];

    renderWithServiceContext(<ClientRedirectFallback />, {
      services: mockServices,
      isLoading: false,
      error: null,
    });

    // Check that window.location.replace was called with the correct URL
    expect(window.location.replace).toHaveBeenCalledWith('/servicos/subcategory-1?slug=service-1');
  });

  it('does not redirect when slug param is present', () => {
    // Mock usePathname to return a path
    const { usePathname } = require('next/navigation');
    usePathname.mockReturnValue('/servicos/subcategory');

    // Mock useSearchParams to return a slug param
    const { useSearchParams } = require('next/navigation');
    useSearchParams.mockReturnValue({
      has: jest.fn().mockReturnValue(true),
      get: jest.fn(),
    });

    renderWithServiceContext(<ClientRedirectFallback />, {
      services: [],
      isLoading: false,
      error: null,
    });

    // Check that window.location.replace was not called
    expect(window.location.replace).not.toHaveBeenCalled();
  });

  it('does not redirect when subcategory is not found', () => {
    // Mock usePathname to return a path with a non-existent subcategory
    const { usePathname } = require('next/navigation');
    usePathname.mockReturnValue('/servicos/non-existent-subcategory');

    // Mock useSearchParams to return no slug param
    const { useSearchParams } = require('next/navigation');
    useSearchParams.mockReturnValue({
      has: jest.fn().mockReturnValue(false),
      get: jest.fn(),
    });

    // Mock services context with no matching subcategory
    const mockServices = [
      {
        id: 1,
        name: 'Category 1',
        slug: 'category-1',
        subcategories: [
          {
            id: 11,
            name: 'Subcategory 1',
            slug: 'subcategory-1',
            services: [
              {
                id: 111,
                name: 'Service 1',
                slug: 'service-1',
              },
            ],
          },
        ],
      },
    ];

    renderWithServiceContext(<ClientRedirectFallback />, {
      services: mockServices,
      isLoading: false,
      error: null,
    });

    // Check that window.location.replace was not called
    expect(window.location.replace).not.toHaveBeenCalled();
  });

  it('does not redirect when services is null', () => {
    // Mock usePathname to return a path
    const { usePathname } = require('next/navigation');
    usePathname.mockReturnValue('/servicos/subcategory');

    // Mock useSearchParams to return no slug param
    const { useSearchParams } = require('next/navigation');
    useSearchParams.mockReturnValue({
      has: jest.fn().mockReturnValue(false),
      get: jest.fn(),
    });

    renderWithServiceContext(<ClientRedirectFallback />, {
      services: null,
      isLoading: true,
      error: null,
    });

    // Check that window.location.replace was not called
    expect(window.location.replace).not.toHaveBeenCalled();
  });

  it('does not redirect when pathname is null', () => {
    // Mock usePathname to return null
    const { usePathname } = require('next/navigation');
    usePathname.mockReturnValue(null);

    // Mock useSearchParams to return no slug param
    const { useSearchParams } = require('next/navigation');
    useSearchParams.mockReturnValue({
      has: jest.fn().mockReturnValue(false),
      get: jest.fn(),
    });

    renderWithServiceContext(<ClientRedirectFallback />, {
      services: [],
      isLoading: false,
      error: null,
    });

    // Check that window.location.replace was not called
    expect(window.location.replace).not.toHaveBeenCalled();
  });

  it('handles errors during redirect', () => {
    // Mock console.error
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Mock usePathname to return a path
    const { usePathname } = require('next/navigation');
    usePathname.mockReturnValue('/servicos/subcategory-1');

    // Mock useSearchParams to return no slug param
    const { useSearchParams } = require('next/navigation');
    useSearchParams.mockReturnValue({
      has: jest.fn().mockReturnValue(false),
      get: jest.fn(),
    });

    // Mock window.location.replace to throw an error
    window.location.replace = jest.fn().mockImplementation(() => {
      throw new Error('Test error');
    });

    // Mock services context with matching subcategory
    const mockServices = [
      {
        id: 1,
        name: 'Category 1',
        slug: 'category-1',
        subcategories: [
          {
            id: 11,
            name: 'Subcategory 1',
            slug: 'subcategory-1',
            services: [
              {
                id: 111,
                name: 'Service 1',
                slug: 'service-1',
              },
            ],
          },
        ],
      },
    ];

    renderWithServiceContext(<ClientRedirectFallback />, {
      services: mockServices,
      isLoading: false,
      error: null,
    });

    // Check that console.error was called
    expect(console.error).toHaveBeenCalled();

    // Restore console.error
    console.error = originalConsoleError;
  });
});
