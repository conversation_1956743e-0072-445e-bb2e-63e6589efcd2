import { ClientServicePage } from '@/src/app/_components/Pages/Service/ClientServicePage';
import { ServiceType } from '@/src/app/_interfaces';
import { render, screen, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';

// Mock the next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
  notFound: jest.fn(),
}));

// Mock the ServiceContext
jest.mock('@/src/app/_context/ServiceContext', () => ({
  useServiceContext: jest.fn().mockReturnValue({
    services: [],
  }),
}));

// Mock the useServiceBySlug hook
jest.mock('@/src/app/_hooks/useServiceBySlug', () => ({
  useServiceBySlug: jest.fn(),
}));

// Mock the components used by ClientServicePage
jest.mock('@/src/app/_components', () => ({
  Breadcrumb: ({ children }: any) => <nav data-testid="breadcrumb">{children}</nav>,
  BreadcrumbList: ({ children }: any) => <ol data-testid="breadcrumb-list">{children}</ol>,
  BreadcrumbItem: ({ children }: any) => <li data-testid="breadcrumb-item">{children}</li>,
  BreadcrumbLink: ({ children, ...props }: any) => (
    <a data-testid="breadcrumb-link" {...props}>
      {children}
    </a>
  ),
  BreadcrumbSeparator: () => <span data-testid="breadcrumb-separator">/</span>,
  BreadcrumbPage: ({ children }: any) => <span data-testid="breadcrumb-page">{children}</span>,
  ServiceNavigationMenuDesktop: () => <div data-testid="service-navigation-menu">Menu</div>,
  ServiceDetails: ({ service }: any) => (
    <div data-testid="service-details">
      <h2>{service.name}</h2>
      <p>{service.description}</p>
    </div>
  ),
  Separator: () => <hr data-testid="separator" />,
  AskForService: () => <div data-testid="ask-for-service">Ask for service</div>,
  DynamicFAQ: () => <div data-testid="dynamic-faq">FAQ</div>,
  JsonLd: () => <div data-testid="json-ld">JSON-LD</div>,
  ServicePageSkeleton: () => <div data-testid="service-page-skeleton">Loading...</div>,
  ServiceCarouselSkeleton: () => (
    <div data-testid="service-carousel-skeleton">Loading carousel...</div>
  ),
}));

// Mock the MainCard component
jest.mock('@/src/app/_components/Pages/Service/MainCard', () => ({
  __esModule: true,
  default: ({ service }: any) => (
    <div data-testid="main-card">
      <h1>{service.name}</h1>
      <p>{service.description}</p>
    </div>
  ),
}));

describe('ClientServicePage Component (Integration Tests)', () => {
  // This variable will hold our real API data
  let realApiService: ServiceType | null = null;

  // Fetch real data before running tests
  beforeAll(async () => {
    try {
      console.warn('🌐 Fetching real API data for integration tests...');

      // Make a real API call
      const apiUrl = 'https://ecommerce-bff-api-smoke.getninjas.io/api/v1/service-type/list';
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'service-provider': 'EUR',
          Accept: 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }

      const data = await response.json();

      // Process the nested API response to extract a service
      if (data && data.categories) {
        for (const category of data.categories) {
          if (category.subcategories) {
            for (const subcategory of category.subcategories) {
              if (subcategory.services && subcategory.services.length > 0) {
                // Use the first service for testing
                realApiService = {
                  ...subcategory.services[0],
                  categoryName: category.name,
                  categorySlug: category.slug,
                  subcategoryName: subcategory.name,
                  subcategorySlug: subcategory.slug,
                };
                console.warn(
                  `✅ Successfully fetched service: ${realApiService.name} (${realApiService.slug})`
                );
                break;
              }
            }
            if (realApiService) break;
          }
        }
      }
    } catch (error) {
      console.error('❌ Error fetching API data:', error);
    }
  });

  // Create fallback mock data in case API is unavailable
  const createMockService = (): ServiceType => ({
    id: 1,
    name: 'Test Service',
    slug: 'test-service',
    description: 'Description for Test Service',
    imageUrl: '/images/test-service.jpg',
    status: 'active',
    price: {
      priceId: 1,
      originalPrice: 150,
      discountPrice: 30,
      finalPrice: 120,
    },
    provider: {
      id: 1,
      name: 'Test Provider',
      imageUrl: '/provider-image.jpg',
      providerUrl: '/provider/1',
      description: 'Provider description',
    },
    availableIn: ['São Paulo', 'Rio de Janeiro'],
    details: ['Detail 1', 'Detail 2'],
    serviceLimits: 'Service limits information',
    keywords: ['test', 'service'],
    categoryName: 'Test Category',
    categorySlug: 'test-category',
    subcategoryName: 'Test Subcategory',
    subcategorySlug: 'test-subcategory',
    termsConditionsUrl: '/terms',
    preparations: 'Preparation 1, Preparation 2',
  });

  beforeEach(() => {
    // Reset mocks between tests
    jest.clearAllMocks();
  });

  it('renders with real API data', async () => {
    // Skip test if no API data is available
    if (!realApiService) {
      console.warn('⚠️ Using fallback mock data for tests');
      realApiService = createMockService();
    }

    // Mock the useServiceBySlug hook to return our real API data
    const useServiceBySlugMock = require('@/src/app/_hooks/useServiceBySlug').useServiceBySlug;
    useServiceBySlugMock.mockReturnValue({
      service: realApiService,
      provider: realApiService.provider,
      isLoading: false,
      error: null,
      isError: false,
    });

    // Render the component with real API data
    await act(async () => {
      render(<ClientServicePage slug={realApiService.slug} />);
    });

    // Wait for the component to finish loading
    await waitFor(() => {
      expect(screen.queryByTestId('service-page-skeleton')).not.toBeInTheDocument();
    });

    // Check that the component renders with real API data
    expect(screen.getByTestId('main-card')).toBeInTheDocument();

    // Use queryAllByText since the service name appears multiple times
    const nameElements = screen.queryAllByText(realApiService.name);
    expect(nameElements.length).toBeGreaterThan(0);

    // Check for description (which also appears multiple times)
    const descElements = screen.queryAllByText(realApiService.description);
    expect(descElements.length).toBeGreaterThan(0);

    // Log whether we're using real or mock data
    if (realApiService.id !== 1) {
      console.warn('✅ Test running with REAL API data');
    } else {
      console.warn('⚠️ Test running with MOCK data (API unavailable)');
    }
  });

  it('shows loading state initially', async () => {
    // Mock the useServiceBySlug hook to simulate loading
    const useServiceBySlugMock = require('@/src/app/_hooks/useServiceBySlug').useServiceBySlug;
    useServiceBySlugMock.mockReturnValue({
      service: null,
      provider: null,
      isLoading: true,
      error: null,
      isError: false,
    });

    // Render the component
    render(<ClientServicePage slug="loading-service" />);

    // Check that the loading skeleton is displayed
    expect(screen.getByTestId('service-page-skeleton')).toBeInTheDocument();
  });

  it('handles error state correctly', async () => {
    // Mock the useServiceBySlug hook to simulate an error
    const useServiceBySlugMock = require('@/src/app/_hooks/useServiceBySlug').useServiceBySlug;
    useServiceBySlugMock.mockReturnValue({
      service: null,
      provider: null,
      isLoading: false,
      error: 'Service not found',
      isError: true,
    });

    // Render the component
    render(<ClientServicePage slug="error-service" />);

    // In the mock implementation, we can't reliably test if notFound was called
    // since it's mocked at the module level. Instead, we'll check that the component
    // doesn't render the main content when there's an error.
    expect(screen.queryByTestId('main-card')).not.toBeInTheDocument();
  });
});
