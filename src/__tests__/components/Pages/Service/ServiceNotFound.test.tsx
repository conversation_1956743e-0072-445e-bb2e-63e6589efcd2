import { ServiceNotFound } from '@/src/app/_components/Pages/Service/ServiceNotFound';
import { render, screen } from '@testing-library/react';

// Mock Next/Link
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({
    href,
    children,
    className,
  }: {
    href: string;
    children: React.ReactNode;
    className?: string;
  }) => (
    <a href={href} className={className} data-testid="next-link">
      {children}
    </a>
  ),
}));

describe('ServiceNotFound', () => {
  it('renders the not found message', () => {
    render(<ServiceNotFound />);

    const heading = screen.getByRole('heading', { level: 1 });
    expect(heading).toBeInTheDocument();
    expect(heading.textContent).toBe('Serviço não encontrado');
  });

  it('renders an explanatory paragraph', () => {
    render(<ServiceNotFound />);

    const paragraph = screen.getByText(/O serviço solicitado não foi encontrado/i);
    expect(paragraph).toBeInTheDocument();
    expect(paragraph.textContent).toContain(
      'Verifique se o link está correto ou explore nossos serviços disponíveis'
    );
  });

  it('renders a link back to the homepage', () => {
    render(<ServiceNotFound />);

    const link = screen.getByTestId('next-link');
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', '/');
    expect(link.textContent).toBe('Voltar para a página inicial');
    expect(link).toHaveClass('bg-gray-900');
  });

  it('renders a container with proper styling', () => {
    render(<ServiceNotFound />);

    // Check the main container - this is the outermost div
    const container = screen.getByText(/Serviço não encontrado/).closest('div')?.parentElement;
    expect(container).toBeInTheDocument();
    expect(container).toHaveClass(
      'mt-10 flex min-h-screen flex-col items-center justify-start gap-2 px-4 text-center'
    );

    // Check the inner container - this is the div that contains the heading
    const innerContainer = screen.getByRole('heading', { level: 1 }).closest('div');
    expect(innerContainer).toBeInTheDocument();
    expect(innerContainer).toHaveClass(
      'flex max-w-lg flex-col gap-4 rounded-2xl bg-white p-6 shadow-lg'
    );
  });
});
