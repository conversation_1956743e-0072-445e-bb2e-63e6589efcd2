import MainCard from '@/src/app/_components/Pages/Service/MainCard';
import { ServiceType } from '@/src/app/_interfaces';
import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import React from 'react';

// Mock the Next.js router
const mockRouterPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockRouterPush,
    replace: mockRouterPush, // Add replace method that uses the same mock function
  }),
  useParams: () => ({
    slug: 'test-service',
  }),
}));

// Mock the ServiceContext
const mockUseServiceContext = jest.fn();
jest.mock('@/src/app/_context/ServiceContext', () => ({
  useServiceContext: () => mockUseServiceContext(),
}));

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    pathname: '/test-service',
    href: 'http://localhost/test-service',
    search: '',
    hash: '',
    host: 'localhost',
    hostname: 'localhost',
    protocol: 'http:',
    origin: 'http://localhost',
    port: '',
    assign: jest.fn(),
    replace: jest.fn(),
    reload: jest.fn(),
  },
  writable: true,
});

// Mock the Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, onLoad, fill, priority, ...props }: any) => {
    // Call onLoad immediately to simulate image loaded
    if (onLoad) setTimeout(() => onLoad({ target: { complete: true } }), 0);
    // Convert boolean props to strings to avoid React DOM warnings
    const safeProps = {
      ...props,
      'data-fill': fill ? 'true' : undefined,
      'data-priority': priority ? 'true' : undefined,
    };
    return <img src={src} alt={alt} {...safeProps} data-testid="next-image" />;
  },
}));

// Mock the Calendar icon and other Lucide icons
jest.mock('lucide-react', () => ({
  Calendar: () => <div data-testid="calendar-icon" />,
  MapPin: () => <div data-testid="map-pin-icon" />,
  AlertCircle: () => <div data-testid="alert-circle-icon" />,
  ChevronDown: ({ className }: { className?: string }) => (
    <div data-testid="chevron-down-icon" className={className} />
  ),
}));

// Mock the analytics hooks
const mockHandleAddToCart = jest.fn();
const mockSendEvent = jest.fn();
jest.mock('@/src/app/_hooks', () => ({
  useAnalyticsEventGeneric: () => ({
    sendEvent: mockSendEvent,
  }),
  useHandleAddToCart: () => ({
    handleAddToCart: mockHandleAddToCart,
  }),
}));

// Mock the Button and Icon components
jest.mock('@/src/app/_components', () => ({
  Button: ({ children, onClick, disabled, className }: any) => (
    <button onClick={onClick} disabled={disabled} className={className} data-testid="button">
      {children}
    </button>
  ),
  Icon: ({ name, className }: any) => (
    <div
      data-testid={`icon-${name.toLowerCase()}`}
      className={className}
      role="img"
      aria-label={name}
    />
  ),
}));

// Mock the formatPrice utility
const mockFormatPrice = jest.fn((price) => `R$ ${price},00`);
const mockConvertToServiceTypes = jest.fn((services) => services);
jest.mock('@/src/app/_utils', () => ({
  convertToServiceTypes: (services: any) => mockConvertToServiceTypes(services),
  formatPrice: (price: any) => mockFormatPrice(price),
}));

// Create consistent mock data for unit tests
const createMockService = (id: number, name: string, slug: string): ServiceType => ({
  id,
  name,
  slug,
  description: `Description for ${name}`,
  imageUrl: `/images/${slug}.jpg`,
  status: 'active',
  price: {
    priceId: id,
    originalPrice: 150 + id * 10,
    discountPrice: 30,
    finalPrice: 120 + id * 10,
  },
  provider: {
    id: 1,
    name: 'Test Provider',
    imageUrl: '/provider-image.jpg',
    providerUrl: '/provider/1',
    description: 'Provider description',
  },
  availableIn: ['São Paulo', 'Rio de Janeiro'],
  details: [`Detail ${id}-1`, `Detail ${id}-2`],
  serviceLimits: 'Service limits information',
  keywords: ['test', 'service'],
  categoryName: 'Test Category',
  categorySlug: 'test-category',
  subcategoryName: 'Test Subcategory',
  subcategorySlug: 'test-subcategory',
  termsConditionsUrl: '/terms',
  preparations: `Preparation ${id}-1, Preparation ${id}-2`,
});

// Create mock services for testing
const mockService = createMockService(1, 'Test Service', 'test-service');
const mockService2 = createMockService(2, 'Another Service', 'another-service');

describe('MainCard Component (Unit Tests)', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mock for ServiceContext
    mockUseServiceContext.mockReturnValue({
      services: [
        {
          id: 1,
          name: 'Test Category',
          slug: 'test-category',
          subcategories: [
            {
              id: 101,
              name: 'Test Subcategory',
              slug: 'test-subcategory',
              services: [mockService, mockService2],
            },
          ],
        },
      ],
    });
  });

  it('renders the component with basic elements', async () => {
    await act(async () => {
      render(<MainCard service={mockService} />);
    });

    // Check for the subcategory name
    expect(screen.getByText(mockService.subcategoryName || '')).toBeInTheDocument();

    // Check for the service description
    expect(screen.getByText(mockService.description || '')).toBeInTheDocument();

    // Check for the city availability and warning sections (text may be split by bold formatting)
    const cityTexts = screen.getAllByText((content, element) => {
      return (
        element?.textContent?.includes('Disponível em São Paulo, Rio de Janeiro e Minas Gerais.') ||
        false
      );
    });
    expect(cityTexts.length).toBeGreaterThan(0);

    const warningTexts = screen.getAllByText((content, element) => {
      return (
        element?.textContent?.includes(
          'A eventual aquisição das peças é de responsabilidade do cliente'
        ) || false
      );
    });
    expect(warningTexts.length).toBeGreaterThan(0);

    // Check for the button
    expect(screen.getByTestId('button')).toBeInTheDocument();
    expect(screen.getByText('Agendar agora')).toBeInTheDocument();

    // Check for the calendar icon
    expect(screen.getByTestId('calendar-icon')).toBeInTheDocument();
  });

  it('renders the service image with correct attributes', async () => {
    await act(async () => {
      render(<MainCard service={mockService} />);
    });

    const image = screen.getByTestId('next-image');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', mockService.imageUrl);
    expect(image).toHaveAttribute('alt', `Imagem do serviço ${mockService.name}`);
  });

  it('renders the pricing information correctly', async () => {
    mockFormatPrice.mockImplementation((price) => {
      if (price === mockService.price.originalPrice)
        return `R$ ${mockService.price.originalPrice},00`;
      if (price === mockService.price.finalPrice) return `R$ ${mockService.price.finalPrice},00`;
      return `R$ ${price},00`;
    });

    await act(async () => {
      render(<MainCard service={mockService} />);
    });

    // Check for the original price with strikethrough
    const originalPrice = screen.getByText(`R$ ${mockService.price.originalPrice},00`);
    expect(originalPrice).toBeInTheDocument();
    expect(originalPrice.closest('p')).toHaveClass('line-through');

    // Check for the final price
    expect(screen.getByText(`R$ ${mockService.price.finalPrice},00`)).toBeInTheDocument();
  });

  it('renders the city availability and warning sections correctly', async () => {
    await act(async () => {
      render(<MainCard service={mockService} />);
    });

    // Check for city availability section (text may be split by bold formatting)
    const cityTexts = screen.getAllByText((content, element) => {
      return (
        element?.textContent?.includes('Disponível em São Paulo, Rio de Janeiro e Minas Gerais.') ||
        false
      );
    });
    expect(cityTexts.length).toBeGreaterThan(0);
    expect(screen.getByTestId('icon-mappin')).toBeInTheDocument();

    // Check for warning alert section (text may be split by bold formatting)
    const warningTexts = screen.getAllByText((content, element) => {
      return (
        element?.textContent?.includes(
          'A eventual aquisição das peças é de responsabilidade do cliente'
        ) || false
      );
    });
    expect(warningTexts.length).toBeGreaterThan(0);
    expect(screen.getByTestId('icon-alertcircle')).toBeInTheDocument();
  });

  it('applies correct accessibility attributes', async () => {
    await act(async () => {
      render(<MainCard service={mockService} />);
    });

    // Check the section has appropriate aria attributes
    const section = screen.getByRole('region');
    expect(section).toHaveAttribute('aria-labelledby', `service-title-${mockService.id}`);
    expect(section).toHaveAttribute('aria-describedby', `service-description-${mockService.id}`);
  });

  it('renders service selection dropdown when multiple services are available', async () => {
    await act(async () => {
      render(<MainCard service={mockService} />);
    });

    // Check if the dropdown is rendered
    const dropdown = screen.getByLabelText('Qual serviço você precisa?');
    expect(dropdown).toBeInTheDocument();

    // Check if both services are in the dropdown
    const options = screen.getAllByRole('option');
    expect(options).toHaveLength(2);

    // Options should be sorted alphabetically
    const sortedServices = [mockService, mockService2].sort((a, b) =>
      (a.name || '').localeCompare(b.name || '')
    );

    expect(options[0]).toHaveTextContent(sortedServices[0].name || '');
    expect(options[1]).toHaveTextContent(sortedServices[1].name || '');
  });

  it('does not render category name when it is the same as subcategory', async () => {
    const serviceWithSameCategory = {
      ...mockService,
      categoryName: 'Same Name',
      subcategoryName: 'Same Name',
    };

    await act(async () => {
      render(<MainCard service={serviceWithSameCategory} />);
    });

    // Should only show the name once (as subcategory)
    const nameElements = screen.getAllByText('Same Name');
    expect(nameElements.length).toBe(1);
  });

  it('handles button click correctly', async () => {
    // Set imageLoaded to true so the button is enabled
    jest
      .spyOn(React, 'useState')
      .mockImplementationOnce(() => [[], jest.fn()]) // services
      .mockImplementationOnce(() => [mockService, jest.fn()]) // selectedService
      .mockImplementationOnce(() => [false, jest.fn()]) // isLoading
      .mockImplementationOnce(() => [true, jest.fn()]); // imageLoaded - set to true

    await act(async () => {
      render(<MainCard service={mockService} />);
    });

    // Find and click the button
    const button = screen.getByTestId('button');

    await act(async () => {
      fireEvent.click(button);
    });

    // Check that handleAddToCart was called with the service
    expect(mockHandleAddToCart).toHaveBeenCalledWith(mockService);

    // Check that router.push was called with the correct path
    expect(mockRouterPush).toHaveBeenCalledWith(
      `/servicos/${mockService.subcategorySlug}/${mockService.slug}/checkout`
    );
  });

  it('handles service change correctly', async () => {
    await act(async () => {
      render(<MainCard service={mockService} />);
    });

    // Find the select element
    const select = screen.getByLabelText('Qual serviço você precisa?') as HTMLSelectElement;

    // Change the select value
    await act(async () => {
      fireEvent.change(select, { target: { value: mockService2.slug } });
    });

    // Wait for the state update and URL change
    await waitFor(() => {
      expect(mockRouterPush).toHaveBeenCalled();
    });

    // Check that router.replace was called with the correct URL structure
    // The implementation uses the current pathname with a query parameter
    expect(mockRouterPush).toHaveBeenCalledWith('/test-service?=another-service', {
      scroll: false,
    });
  });

  it('renders loading state correctly', async () => {
    // Mock the initial state to show loading
    jest
      .spyOn(React, 'useState')
      .mockImplementationOnce(() => [[], jest.fn()]) // services
      .mockImplementationOnce(() => [null, jest.fn()]) // selectedService
      .mockImplementationOnce(() => [true, jest.fn()]) // isLoading
      .mockImplementationOnce(() => [false, jest.fn()]); // imageLoaded

    await act(async () => {
      render(<MainCard service={mockService} />);
    });

    // Since selectedService is null, the component should return null
    expect(screen.queryByText('Test Subcategory')).not.toBeInTheDocument();
  });

  it('renders loading skeleton for city availability section', async () => {
    // Mock the initial state to show loading for the city availability section
    jest
      .spyOn(React, 'useState')
      .mockImplementationOnce(() => [[], jest.fn()]) // services
      .mockImplementationOnce(() => [mockService, jest.fn()]) // selectedService
      .mockImplementationOnce(() => [true, jest.fn()]) // isLoading - set to true
      .mockImplementationOnce(() => [false, jest.fn()]); // imageLoaded

    await act(async () => {
      render(<MainCard service={mockService} />);
    });

    // Check for loading skeleton elements
    const skeletonElements = screen.getAllByRole('generic');
    const loadingSkeletons = skeletonElements.filter(
      (el) => el.className.includes('animate-pulse') && el.className.includes('bg-gray-200')
    );
    expect(loadingSkeletons.length).toBeGreaterThan(0);
  });
});
