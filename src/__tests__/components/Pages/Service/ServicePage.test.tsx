import ServicePage from '@/src/app/(pages)/servicos/[subcategory]/page';
import { render } from '@testing-library/react';

// Mock the components used in ServicePage
jest.mock('@/src/app/_components', () => ({
  ClientServicePage: ({ slug }: { slug: string }) => (
    <div data-testid="client-service-page">Service Slug: {slug}</div>
  ),
  ClientRedirectFallback: () => <div data-testid="client-redirect-fallback">Redirecting...</div>,
}));

// Mock the serviceTypeApi
jest.mock('@/src/app/_services/serviceTypeApi', () => ({
  serviceTypeApi: {
    getSubcategories: jest.fn().mockResolvedValue([{ subcategory: 'test-subcategory' }]),
  },
}));

// Mock the generateDynamicMetadata function
jest.mock('@/src/app/_utils/dynamicMetadata', () => ({
  generateDynamicMetadata: jest.fn().mockResolvedValue({
    title: 'Test Service',
    description: 'Test Description',
  }),
}));

describe('ServicePage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the ClientServicePage with the correct slug', async () => {
    // Create props with searchParams containing slug
    const props = {
      params: Promise.resolve({ subcategory: 'test-subcategory' }),
      searchParams: Promise.resolve({ slug: 'test-service' }),
    };

    // Render the component
    const { findByTestId } = render(await ServicePage(props));

    // Verify that the ClientServicePage is rendered with the correct slug
    const clientPage = await findByTestId('client-service-page');
    expect(clientPage).toBeInTheDocument();
    expect(clientPage.textContent).toContain('Service Slug: test-service');
  });

  it('renders the ClientRedirectFallback when no slug is provided', async () => {
    // Create props with empty searchParams
    const props = {
      params: Promise.resolve({ subcategory: 'test-subcategory' }),
      searchParams: Promise.resolve({}),
    };

    // Render the component
    const { findByTestId } = render(await ServicePage(props));

    // Verify that the ClientRedirectFallback component is rendered
    const redirectComponent = await findByTestId('client-redirect-fallback');
    expect(redirectComponent).toBeInTheDocument();
  });
});
