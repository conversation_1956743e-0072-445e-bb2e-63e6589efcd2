import { generateMetadata } from '@/src/app/(pages)/servicos/[subcategory]/[slug]/checkout/metadata';
import { ServiceApiService } from '@/src/app/_services/serviceApi';

jest.mock('@/src/app/_services/serviceApi', () => ({
  ServiceApiService: {
    getServiceBySlug: jest.fn(),
  },
}));

describe('Checkout Page Metadata', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('returns service-specific metadata when service is found', async () => {
    // Mock service data
    const mockService = {
      id: '1',
      name: 'Limpeza Residencial',
      description: 'Serviço de limpeza completa para sua residência',
    };

    // Mock successful API response
    (ServiceApiService.getServiceBySlug as jest.Mock).mockResolvedValue({
      service: mockService,
    });

    // Create props with valid slug
    const props = {
      params: { slug: 'limpeza-residencial' },
    };

    // Generate metadata
    const metadata = await generateMetadata(props);

    // Verify API was called with correct slug
    expect(ServiceApiService.getServiceBySlug).toHaveBeenCalledWith('limpeza-residencial');

    // Verify metadata content
    expect(metadata.title).toBe('Agendamento - Limpeza Residencial - GetNinjas');
    expect(metadata.description).toBe(
      'Complete seu agendamento para Limpeza Residencial no GetNinjas'
    );

    // Verify OpenGraph metadata
    expect(metadata.openGraph).toEqual({
      title: 'Agendamento - Limpeza Residencial',
      description: 'Complete seu agendamento para Limpeza Residencial no GetNinjas',
    });
  });

  it('returns default metadata when service is not found', async () => {
    // Mock API response with no service
    (ServiceApiService.getServiceBySlug as jest.Mock).mockResolvedValue({
      service: null,
    });

    // Create props with non-existent slug
    const props = {
      params: { slug: 'non-existent-service' },
    };

    // Generate metadata
    const metadata = await generateMetadata(props);

    // Verify API was called with correct slug
    expect(ServiceApiService.getServiceBySlug).toHaveBeenCalledWith('non-existent-service');

    // Verify default metadata is returned
    expect(metadata.title).toBe('Agendamento - GetNinjas');
    expect(metadata.description).toBe('Complete seu agendamento no GetNinjas');
    expect(metadata.openGraph).toBeUndefined();
  });
});
