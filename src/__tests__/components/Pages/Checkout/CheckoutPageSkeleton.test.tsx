import { CheckoutPageSkeleton } from '@/src/app/_components/Pages/Checkout/CheckoutPageSkeleton';
import { render, screen } from '@testing-library/react';

// Mock react-markdown
jest.mock('react-markdown', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Mock remark-gfm
jest.mock('remark-gfm', () => ({
  __esModule: true,
  default: () => ({}),
}));

// Mock the Skeleton component
jest.mock('@/src/app/_components/Ui/skeleton', () => ({
  Skeleton: ({ className, ...props }: any) => (
    <div data-testid="skeleton" className={className} {...props} />
  ),
}));

// Mock the Separator component
jest.mock('@/src/app/_components/Ui/separator', () => ({
  Separator: ({ className, ...props }: any) => (
    <div data-testid="separator" className={className} {...props} />
  ),
}));

describe('CheckoutPageSkeleton Component', () => {
  test('renders the skeleton layout correctly', () => {
    const { container } = render(<CheckoutPageSkeleton />);

    // Check for main container
    const mainContainer = container.querySelector('.container');
    expect(mainContainer).toBeInTheDocument();
    expect(mainContainer).toHaveClass('container', 'mx-auto', 'max-w-4xl');

    // Check for header section
    const headerSection = container.querySelector('header');
    expect(headerSection).toBeInTheDocument();
    expect(headerSection).toHaveClass('flex', 'items-start', 'space-x-4');

    // Check all skeleton elements are rendered
    const skeletons = screen.getAllByTestId('skeleton');
    expect(skeletons.length).toBeGreaterThan(20); // We expect many skeleton elements

    // Check that separator exists
    const separator = screen.getByTestId('separator');
    expect(separator).toBeInTheDocument();
    expect(separator).toHaveClass('my-6');
  });

  test('renders the correct number of sections', () => {
    const { container } = render(<CheckoutPageSkeleton />);

    // Get all sections using querySelector instead of role
    const sections = container.querySelectorAll('section');

    // Should have 3 main sections (excluding the header)
    expect(sections.length).toBe(3);
  });

  test('renders the grid layout for the middle section', () => {
    render(<CheckoutPageSkeleton />);

    // Find the grid section
    const gridSection = document.querySelector('.grid.gap-8.md\\:grid-cols-2');
    expect(gridSection).toBeInTheDocument();

    // It should have two columns
    const columns = gridSection?.querySelectorAll(':scope > div');
    expect(columns?.length).toBe(2);
  });

  test('renders the correct number of list items', () => {
    render(<CheckoutPageSkeleton />);

    // Check the map iterations worked correctly
    const leftColumnItems = document.querySelectorAll(
      '.grid > div:first-child .flex.items-start.space-x-3'
    );
    const rightColumnItems = document.querySelectorAll(
      '.grid > div:last-child .flex.items-start.space-x-3'
    );
    const bulletItems = document.querySelectorAll(
      '.rounded-lg.bg-muted .flex.items-center.space-x-2'
    );

    expect(leftColumnItems.length).toBe(2); // Should have 2 items in left column
    expect(rightColumnItems.length).toBe(2); // Should have 2 items in right column
    expect(bulletItems.length).toBe(3); // Should have 3 bullet items
  });
});
