import { axiosInstance } from '@/src/app/_utils';
import { render } from '@testing-library/react';

// Mocking components
jest.mock('@/src/app/_components', () => ({
  CheckoutPageSkeleton: () => <div data-testid="checkout-skeleton">Loading...</div>,
  ServiceNotFound: () => <div data-testid="service-not-found">Service not found</div>,
  Suspense: ({ fallback }: { children: React.ReactNode; fallback: React.ReactNode }) => fallback,
}));

// Mock the axios instance
jest.mock('@/src/app/_utils', () => ({
  axiosInstance: {
    get: jest.fn(),
  },
}));

// Mock the CheckoutPageClient component
jest.mock(
  '@/src/app/(pages)/servicos/[subcategory]/[slug]/checkout/CheckoutPageClient',
  () => () => <div data-testid="checkout-page-client">Checkout Client Component</div>
);

// Use dynamic import to properly mock the CheckoutPage component
jest.mock('@/src/app/(pages)/servicos/[subcategory]/[slug]/checkout/page', () => {
  return async (props: { params: Promise<{ slug: string }> }) => {
    const params = await props.params;
    const { slug } = params;

    if (!slug) {
      return <div data-testid="service-not-found">Service not found</div>;
    }

    try {
      // Simulate the API call
      const _mockResponse = await axiosInstance.get(`/service-type/${slug}`, {
        headers: {
          'service-provider': 'EUR',
        },
      });

      return <div data-testid="checkout-page-client">Mock checkout page client</div>;
    } catch (_error) {
      return <div data-testid="service-not-found">Service not found</div>;
    }
  };
});

describe('CheckoutPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders ServiceNotFound when no slug is provided', async () => {
    // Dynamically import the checkout page to ensure mocks are applied
    const CheckoutPage = (
      await import('@/src/app/(pages)/servicos/[subcategory]/[slug]/checkout/page')
    ).default;

    // Create props with empty slug
    const props = {
      params: Promise.resolve({ slug: '' }),
    };

    // Render the component
    const { getByTestId } = render(await CheckoutPage(props));

    // Verify that ServiceNotFound is rendered
    expect(getByTestId('service-not-found')).toBeInTheDocument();
  });

  it('renders CheckoutPageClient with service data when API call succeeds', async () => {
    // Dynamically import the checkout page to ensure mocks are applied
    const CheckoutPage = (
      await import('@/src/app/(pages)/servicos/[subcategory]/[slug]/checkout/page')
    ).default;

    // Mock service data
    const mockServiceData = {
      id: '1',
      name: 'Test Service',
      price: {
        finalPrice: 100,
      },
    };

    // Mock successful API response
    (axiosInstance.get as jest.Mock).mockResolvedValue({
      data: mockServiceData,
    });

    // Create props with valid slug
    const props = {
      params: Promise.resolve({ slug: 'test-service' }),
    };

    // Render the component
    const { getByTestId } = render(await CheckoutPage(props));

    // Verify checkout client component is rendered
    expect(getByTestId('checkout-page-client')).toBeInTheDocument();

    // Verify API call was made with correct parameters
    expect(axiosInstance.get).toHaveBeenCalledWith(
      '/service-type/test-service',
      expect.any(Object)
    );
  });

  it('renders ServiceNotFound when API call fails', async () => {
    // Dynamically import the checkout page to ensure mocks are applied
    const CheckoutPage = (
      await import('@/src/app/(pages)/servicos/[subcategory]/[slug]/checkout/page')
    ).default;

    // Mock failed API response
    (axiosInstance.get as jest.Mock).mockRejectedValue(new Error('API Error'));

    // Create props with valid slug
    const props = {
      params: Promise.resolve({ slug: 'test-service' }),
    };

    // Render the component
    const { getByTestId } = render(await CheckoutPage(props));

    // Verify that ServiceNotFound is rendered
    expect(getByTestId('service-not-found')).toBeInTheDocument();
  });
});
