/**
 * CheckoutPageClient Component Tests
 *
 * This file contains unit tests for the CheckoutPageClient component.
 * Integration tests are in CheckoutPageClient.integration.test.tsx.
 *
 * Unit tests focus on component behavior with mocked dependencies,
 * while integration tests use real API data.
 */

import CheckoutPageClient from '@/src/app/(pages)/servicos/[subcategory]/[slug]/checkout/CheckoutPageClient';
import { useServiceBySlug } from '@/src/app/_hooks';
import { Service } from '@/src/app/_interfaces/service-type';
import { PaymentService } from '@/src/app/_services/payment';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';

// Mock the hooks
jest.mock('@/src/app/_hooks', () => ({
  useServiceBySlug: jest.fn(),
}));

// Mock the router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn().mockReturnValue('/servicos/subcategory/slug/checkout'),
}));

// Mock the payment service
jest.mock('@/src/app/_services/payment', () => ({
  PaymentService: {
    createOrder: jest.fn(),
  },
}));

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {
    'checkout-form-data': JSON.stringify({ name: 'Test User' }),
    'another-form-data': JSON.stringify({ something: 'else' }),
  };

  return {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn((key) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
    keys: () => Object.keys(store),
    length: Object.keys(store).length,
    key: (i: number) => Object.keys(store)[i],
  };
})();

// Mock the components
jest.mock('@/src/app/_components', () => ({
  Breadcrumb: ({ children, className }: any) => <nav className={className}>{children}</nav>,
  BreadcrumbList: ({ children }: any) => <ol data-testid="breadcrumb-list">{children}</ol>,
  BreadcrumbItem: ({ children }: any) => <li>{children}</li>,
  BreadcrumbLink: ({ href, children }: any) => (
    <a href={href} data-testid={`breadcrumb-link-${href}`}>
      {children}
    </a>
  ),
  BreadcrumbPage: ({ children, className }: any) => (
    <span className={className} data-testid="breadcrumb-page">
      {children}
    </span>
  ),
  BreadcrumbSeparator: () => <span>/</span>,
  CheckoutErrorModal: ({ isOpen, onClose, onRetry }: any) =>
    isOpen ? (
      <div data-testid="error-modal">
        <button onClick={onClose} data-testid="close-modal">
          Close
        </button>
        <button onClick={onRetry} data-testid="retry-payment">
          Retry
        </button>
      </div>
    ) : null,
  CheckoutForm: ({ service, provider, onSubmit }: any) => (
    <form
      data-testid="checkout-form"
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          name: 'Test User',
          email: '<EMAIL>',
          // Add other required fields based on CheckoutFormSchema
        });
      }}
    >
      <div>Service: {service.name}</div>
      <div>Provider: {provider?.name || 'No provider'}</div>
      <button type="submit" data-testid="submit-form">
        Submit
      </button>
    </form>
  ),
  CheckoutPageSkeleton: () => <div data-testid="checkout-skeleton">Loading...</div>,
  ErrorFallback: ({ error }: any) => <div data-testid="error-fallback">Error: {error.message}</div>,
  JsonLd: ({ data }: any) => (
    <script
      type="application/ld+json"
      data-testid="json-ld"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  ),
  Loader: () => <div data-testid="loader">Processing payment...</div>,
}));

// Mock lucide-react
jest.mock('lucide-react', () => ({
  Home: () => <span data-testid="home-icon">Home</span>,
}));

describe('CheckoutPageClient', () => {
  const mockRouter = {
    push: jest.fn(),
  };

  const mockService: Service = {
    id: 123,
    slug: 'test-service',
    name: 'Test Service',
    description: 'A test service description',
    imageUrl: 'https://example.com/service.jpg',
    status: 'active',
    provider: {
      id: 789,
      name: 'Test Provider',
      imageUrl: 'https://example.com/provider.jpg',
      providerUrl: 'https://example.com/provider',
      description: 'Test provider description',
    },
    price: {
      priceId: 456,
      originalPrice: 120,
      discountPrice: 100,
      finalPrice: 100,
    },
    availableIn: ['São Paulo'],
    details: ['Detail 1', 'Detail 2'],
    serviceLimits: 'Service limits description',
    keywords: ['test', 'service'],
    termsConditionsUrl: 'https://example.com/terms',
    preparations: 'Service preparations',
  };

  // Original console.error
  const originalConsoleError = console.error;

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup router mock
    (useRouter as jest.Mock).mockReturnValue(mockRouter);

    // Mock console.error
    console.error = jest.fn();

    // Setup localStorage mock
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
    });

    // Setup window.location mock
    window.location = { href: '' } as unknown as Location;
  });

  afterEach(() => {
    // Restore console.error
    console.error = originalConsoleError;
  });

  it('renders loading skeleton when service is loading', () => {
    // Mock loading state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: null,
      provider: null,
      isLoading: true,
      error: null,
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={mockService} />);

    expect(screen.getByTestId('checkout-skeleton')).toBeInTheDocument();
  });

  it('renders loading skeleton when service is null', () => {
    // Mock state with null service but not loading
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: null,
      provider: null,
      isLoading: false,
      error: null,
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={mockService} />);

    expect(screen.getByTestId('checkout-skeleton')).toBeInTheDocument();
  });

  it('renders error state when service loading fails', () => {
    // Mock error state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: null,
      provider: null,
      isLoading: false,
      error: 'Failed to load service',
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={mockService} />);

    expect(screen.getByText('Error Loading Service')).toBeInTheDocument();
    expect(screen.getByText('Failed to load service')).toBeInTheDocument();

    // Test return to home button
    const homeButton = screen.getByText('Return to Home');
    fireEvent.click(homeButton);
    expect(mockRouter.push).toHaveBeenCalledWith('/');
  });

  it('renders checkout form when service is loaded successfully', () => {
    // Mock successful state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: mockService,
      provider: mockService.provider,
      isLoading: false,
      error: null,
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={mockService} />);

    // Verify checkout form is rendered with correct data
    const form = screen.getByTestId('checkout-form');
    expect(form).toBeInTheDocument();
    expect(screen.getByText('Service: Test Service')).toBeInTheDocument();
    expect(screen.getByText('Provider: Test Provider')).toBeInTheDocument();

    // Verify breadcrumb navigation
    expect(screen.getByTestId('breadcrumb-list')).toBeInTheDocument();
    expect(screen.getByTestId('breadcrumb-link-/')).toBeInTheDocument();
    expect(
      screen.getByTestId('breadcrumb-link-/servicos/subcategory?=test-service')
    ).toBeInTheDocument();
    expect(screen.getByTestId('breadcrumb-page')).toHaveTextContent('Agendamento');

    // Verify JsonLd is rendered
    const jsonLd = screen.getByTestId('json-ld');
    expect(jsonLd).toBeInTheDocument();
    const jsonData = JSON.parse(jsonLd.innerHTML);
    expect(jsonData.name).toBe('Test Service');
    expect(jsonData.provider.name).toBe('Test Provider');
  });

  it('handles form submission and successful payment redirection', async () => {
    // Mock successful state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: mockService,
      provider: mockService.provider,
      isLoading: false,
      error: null,
    });

    // Mock successful payment response
    (PaymentService.createOrder as jest.Mock).mockResolvedValue({
      secureUrl: 'https://payment-gateway.com/checkout',
    });

    // Mock window.location
    const mockLocation = new URL('http://localhost');
    Object.defineProperty(window, 'location', {
      value: mockLocation,
      writable: true,
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={mockService} />);

    // Submit the form
    fireEvent.click(screen.getByTestId('submit-form'));

    // Verify loader is displayed during payment processing
    expect(screen.getByTestId('loader')).toBeInTheDocument();

    // Verify payment service is called with correct data
    expect(PaymentService.createOrder).toHaveBeenCalledWith(
      expect.objectContaining({
        name: 'Test User',
        email: '<EMAIL>',
        serviceId: mockService.id,
        priceId: mockService.price.priceId,
      })
    );

    // Wait for redirection
    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith('https://payment-gateway.com/checkout');
    });
  });

  it('shows error modal when payment processing fails', async () => {
    // Mock successful service loading state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: mockService,
      provider: mockService.provider,
      isLoading: false,
      error: null,
    });

    // Mock payment error
    (PaymentService.createOrder as jest.Mock).mockRejectedValue(
      new Error('Payment processing failed')
    );

    render(<CheckoutPageClient slug="test-service" initialServiceData={mockService} />);

    // Submit the form
    fireEvent.click(screen.getByTestId('submit-form'));

    // Wait for error modal to appear
    await waitFor(() => {
      expect(screen.getByTestId('error-modal')).toBeInTheDocument();
    });

    // Test close button
    fireEvent.click(screen.getByTestId('close-modal'));
    expect(screen.queryByTestId('error-modal')).not.toBeInTheDocument();

    // Verify console.error was called
    expect(console.error).toHaveBeenCalled();
  });

  it('shows error modal when payment response has no secureUrl', async () => {
    // Mock successful service loading state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: mockService,
      provider: mockService.provider,
      isLoading: false,
      error: null,
    });

    // Mock payment response with missing secureUrl
    (PaymentService.createOrder as jest.Mock).mockResolvedValue({
      success: true,
      // No secureUrl provided
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={mockService} />);

    // Submit the form
    fireEvent.click(screen.getByTestId('submit-form'));

    // Wait for error modal to appear
    await waitFor(() => {
      expect(screen.getByTestId('error-modal')).toBeInTheDocument();
    });

    // Verify console.error was called
    expect(console.error).toHaveBeenCalled();
  });

  it('does not clear form data from localStorage on mount', () => {
    // Mock successful state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: mockService,
      provider: mockService.provider,
      isLoading: false,
      error: null,
    });

    // Check that localStorage has form data before rendering
    expect(localStorageMock.getItem('checkout-form-data')).toBeTruthy();

    render(<CheckoutPageClient slug="test-service" initialServiceData={mockService} />);

    // The component no longer clears form data from localStorage on mount
    // This test now verifies that behavior has changed
    expect(localStorageMock.removeItem).not.toHaveBeenCalledWith('checkout-form-data');
  });

  it('extracts priceId from service on mount', () => {
    // Create service with priceId explicitly
    const serviceWithPriceId = {
      ...mockService,
      price: {
        ...mockService.price,
        priceId: 789, // Explicit priceId
      },
    };

    // Mock successful state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: serviceWithPriceId,
      provider: serviceWithPriceId.provider,
      isLoading: false,
      error: null,
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={serviceWithPriceId} />);

    // Submit the form to trigger payment processing
    fireEvent.click(screen.getByTestId('submit-form'));

    // Verify the correct priceId was passed to createOrder
    expect(PaymentService.createOrder).toHaveBeenCalledWith(
      expect.objectContaining({
        priceId: 789, // The extracted priceId
      })
    );
  });

  it('handles service with missing priceId correctly', async () => {
    // Create service with missing priceId
    const serviceWithoutPriceId = {
      ...mockService,
      price: {
        ...mockService.price,
        priceId: undefined, // Missing priceId
      },
    };

    // Mock successful state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: serviceWithoutPriceId,
      provider: serviceWithoutPriceId.provider,
      isLoading: false,
      error: null,
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={serviceWithoutPriceId} />);

    // Submit the form
    fireEvent.click(screen.getByTestId('submit-form'));

    // Should try to submit but fail with error
    await waitFor(() => {
      expect(screen.getByTestId('error-modal')).toBeInTheDocument();
    });

    // Verify console.error was called
    expect(console.error).toHaveBeenCalled();
  });

  it('handles service with missing price object correctly', async () => {
    // Create service with missing price object
    const serviceWithoutPrice = {
      ...mockService,
      price: undefined, // Missing price object
    };

    // Mock successful state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: serviceWithoutPrice,
      provider: serviceWithoutPrice.provider,
      isLoading: false,
      error: null,
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={serviceWithoutPrice} />);

    // Submit the form
    fireEvent.click(screen.getByTestId('submit-form'));

    // Should try to submit but fail with error
    await waitFor(() => {
      expect(screen.getByTestId('error-modal')).toBeInTheDocument();
    });

    // Verify console.error was called
    expect(console.error).toHaveBeenCalled();
  });

  it('handles service with missing service.id correctly', async () => {
    // Create service with missing id
    const serviceWithoutId = {
      ...mockService,
      id: undefined, // Missing id
    };

    // Mock successful state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: serviceWithoutId,
      provider: serviceWithoutId.provider,
      isLoading: false,
      error: null,
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={serviceWithoutId} />);

    // Submit the form
    fireEvent.click(screen.getByTestId('submit-form'));

    // Should try to submit but fail with error
    await waitFor(() => {
      expect(screen.getByTestId('error-modal')).toBeInTheDocument();
    });

    // Verify console.error was called
    expect(console.error).toHaveBeenCalled();
  });

  it('handles retry payment correctly', async () => {
    // Mock successful state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: mockService,
      provider: mockService.provider,
      isLoading: false,
      error: null,
    });

    // Mock payment error for the first call, success for the second
    (PaymentService.createOrder as jest.Mock)
      .mockRejectedValueOnce(new Error('Payment failed'))
      .mockResolvedValueOnce({ secureUrl: 'https://payment-gateway.com/checkout' });

    render(<CheckoutPageClient slug="test-service" initialServiceData={mockService} />);

    // Submit the form
    fireEvent.click(screen.getByTestId('submit-form'));

    // Wait for error modal to appear
    await waitFor(() => {
      expect(screen.getByTestId('error-modal')).toBeInTheDocument();
    });

    // Clear previous call logs
    (PaymentService.createOrder as jest.Mock).mockClear();

    // Click close instead of retry to avoid navigation issues in test
    fireEvent.click(screen.getByTestId('close-modal'));

    // Verify the error modal is closed
    await waitFor(() => {
      expect(screen.queryByTestId('error-modal')).not.toBeInTheDocument();
    });
  });

  it('creates proper JsonLd data even when provider is missing', () => {
    // Create service with undefined provider
    const serviceWithoutProvider = {
      ...mockService,
      provider: undefined,
    };

    // Mock state with missing provider
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: serviceWithoutProvider,
      provider: undefined,
      isLoading: false,
      error: null,
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={serviceWithoutProvider} />);

    // Verify JsonLd is rendered with minimal provider data
    const jsonLd = screen.getByTestId('json-ld');
    expect(jsonLd).toBeInTheDocument();

    const jsonData = JSON.parse(jsonLd.innerHTML);
    expect(jsonData.name).toBe('Test Service');
    expect(jsonData.provider).toBeDefined();
    expect(jsonData.provider.name).toBeUndefined();
  });

  it('uses initialServiceData for hydration', () => {
    // Define initial service data
    const initialData = {
      ...mockService,
      name: 'Initial Service Name',
    };

    // Mock useServiceBySlug to verify initialData is passed correctly
    (useServiceBySlug as jest.Mock).mockImplementation((_slug, _initialDataParam) => {
      // Return a different service than the initial data
      return {
        service: {
          ...mockService,
          name: 'Loaded Service Name',
        },
        provider: mockService.provider,
        isLoading: false,
        error: null,
      };
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={initialData} />);

    // Verify useServiceBySlug was called with initialData
    expect(useServiceBySlug).toHaveBeenCalledWith(
      'test-service',
      expect.objectContaining({
        service: initialData,
      })
    );

    // After hydration, the loaded service name is shown
    expect(screen.getByText('Service: Loaded Service Name')).toBeInTheDocument();
  });

  it('handles pathname parts correctly for breadcrumbs', () => {
    // Mock different pathname
    const usePathnameMock = jest.requireMock('next/navigation').usePathname;
    usePathnameMock.mockReturnValue('/servicos/custom-category/custom-subcategory/checkout');

    // Mock successful state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: mockService,
      provider: mockService.provider,
      isLoading: false,
      error: null,
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={mockService} />);

    // Verify breadcrumb navigation uses correct path parts
    expect(
      screen.getByTestId('breadcrumb-link-/servicos/custom-category?=test-service')
    ).toBeInTheDocument();
  });

  it('handles missing initialServiceData correctly', () => {
    // Mock successful state without initialData influence
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: mockService,
      provider: mockService.provider,
      isLoading: false,
      error: null,
    });

    // Render without initialServiceData
    render(<CheckoutPageClient slug="test-service" initialServiceData={undefined} />);

    // Verify useServiceBySlug was called without initialData
    expect(useServiceBySlug).toHaveBeenCalledWith('test-service', undefined);

    // Verify component renders correctly
    expect(screen.getByTestId('checkout-form')).toBeInTheDocument();
  });

  it('prevents multiple form submissions', async () => {
    // Mock successful state
    (useServiceBySlug as jest.Mock).mockReturnValue({
      service: mockService,
      provider: mockService.provider,
      isLoading: false,
      error: null,
    });

    // Mock a delayed payment response to simulate slow processing
    (PaymentService.createOrder as jest.Mock).mockImplementation(() => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ secureUrl: 'https://payment-gateway.com/checkout' });
        }, 100);
      });
    });

    render(<CheckoutPageClient slug="test-service" initialServiceData={mockService} />);

    // Submit the form
    fireEvent.click(screen.getByTestId('submit-form'));

    // Verify loader is displayed
    expect(screen.getByTestId('loader')).toBeInTheDocument();

    // Try to submit the form again while the first submission is processing
    fireEvent.click(screen.getByTestId('submit-form'));

    // Wait for the payment process to complete
    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith('https://payment-gateway.com/checkout');
    });

    // Verify that createOrder was only called once despite multiple clicks
    expect(PaymentService.createOrder).toHaveBeenCalledTimes(1);
  });
});
