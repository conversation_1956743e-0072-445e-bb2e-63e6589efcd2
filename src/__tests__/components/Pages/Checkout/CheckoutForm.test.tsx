import { CheckoutForm } from '@/src/app/_components/Pages/Checkout/CheckoutForm';
import { fireEvent, render, screen, waitFor, within } from '@testing-library/react';
import React from 'react';

// Mock external dependencies first
jest.mock('@/src/app/_hooks/analytics/useTrackBeginCheckout', () => ({
  useHandleBeginCheckout: jest.fn().mockReturnValue({
    trackBeginCheckout: jest.fn(),
  }),
}));

// Mock custom hooks
const mockTogglePreparations = jest.fn();
const mockToggleLimits = jest.fn();
const mockShouldShowExpandButton = jest.fn().mockReturnValue(true);
let mockPreparationsExpanded = false;
let mockLimitsExpanded = false;

jest.mock('@/src/app/_hooks/useExpandableSections', () => ({
  useExpandableSections: () => ({
    preparations: {
      get isExpanded() {
        return mockPreparationsExpanded;
      },
      toggle: mockTogglePreparations,
    },
    limits: {
      get isExpanded() {
        return mockLimitsExpanded;
      },
      toggle: mockToggleLimits,
    },
  }),
  useTextExpansion: () => ({
    formatText: (text: string) => text,
    truncateText: (text: string) => text.substring(0, 100),
    shouldShowExpandButton: mockShouldShowExpandButton,
  }),
}));

// Mock ES module dependencies
jest.mock('react-markdown', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt }: { src: string; alt: string }) => <img src={src} alt={alt} />,
}));

jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  ),
}));

// Mock child components with proper accessibility attributes
jest.mock('@/src/app/_components/Pages/Checkout/ScheduleForm', () => ({
  ScheduleForm: ({}: any) => (
    <div data-testid="schedule-form" role="form" aria-label="schedule form">
      Schedule Form
    </div>
  ),
}));

jest.mock('@/src/app/_components/Pages/Checkout/AddressForm', () => ({
  AddressForm: ({}: any) => (
    <div data-testid="address-form" role="form" aria-label="address form">
      Address Form
    </div>
  ),
}));

jest.mock('@/src/app/_components/Pages/Checkout/PersonalInfoForm', () => ({
  PersonalInfoForm: ({}: any) => (
    <div data-testid="personal-info-form" role="form" aria-label="personal info form">
      Personal Info Form
    </div>
  ),
}));

// Mock UI components with essential props
jest.mock('@/src/app/_components', () => ({
  Button: ({ children, onClick, className }: any) => (
    <button onClick={onClick} className={className} role="button" type="submit">
      {children}
    </button>
  ),
  Checkbox: ({ checked, onCheckedChange, id }: any) => (
    <input
      type="checkbox"
      checked={checked}
      onChange={(e) => onCheckedChange(e.target.checked)}
      id={id}
      role="checkbox"
      aria-checked={checked}
    />
  ),
  FormControl: ({ children }: any) => <div>{children}</div>,
  FormField: ({ name, render }: any) => {
    // Create a mock field value based on the field name
    let fieldValue: any = false;

    if (name === 'firstName' || name === 'lastName') fieldValue = 'Test';
    if (name === 'countryCode') fieldValue = '+55';
    if (name === 'phone') fieldValue = '11999999999';
    if (name === 'cpf') fieldValue = '12345678909';
    if (name === 'email') fieldValue = '<EMAIL>';
    if (name === 'cep') fieldValue = '01234567';
    if (name === 'street') fieldValue = 'Test Street';
    if (name === 'streetNumber') fieldValue = '123';
    if (name === 'neighborhood') fieldValue = 'Test Neighborhood';
    if (name === 'city') fieldValue = 'Test City';
    if (name === 'state') fieldValue = 'SP';
    if (name === 'date') fieldValue = new Date();
    if (name === 'period') fieldValue = 'morning';

    return render({
      field: {
        value: fieldValue,
        onChange: (value: any) => {
          if (name === 'terms') {
            const checkbox = document.querySelector('input[type="checkbox"]');
            if (checkbox) {
              (checkbox as HTMLInputElement).checked = value;
              checkbox.setAttribute('aria-checked', value.toString());
            }
          }
        },
      },
    });
  },
  FormItem: ({ children }: any) => <div>{children}</div>,
  FormMessage: ({ children }: any) => <div role="alert">{children}</div>,
  Icon: ({ name, className }: any) => (
    <svg className={className} role="img" aria-label={name}>
      <title>{name}</title>
    </svg>
  ),
  ServiceSummaryCard: ({ service, onSubmit, isFormValid, showButton, formId }: any) => (
    <div className="w-full overflow-hidden md:hidden">
      <div data-testid="mobile-summary-container" className="w-full overflow-hidden md:hidden">
        <div
          data-testid={`service-summary-${service?.slug}`}
          role="article"
          aria-label="service summary"
        >
          Service Summary for {service?.slug}
          {showButton && (
            <button
              type="submit"
              form={formId}
              onClick={onSubmit}
              disabled={!isFormValid}
              data-testid="continue-button"
            >
              Continuar agendamento
            </button>
          )}
        </div>
      </div>
    </div>
  ),
  AddressForm: ({}: any) => (
    <div data-testid="address-form" role="form" aria-label="address form">
      Address Form
    </div>
  ),
  PersonalInfoForm: ({}: any) => (
    <div data-testid="personal-info-form" role="form" aria-label="personal info form">
      Personal Info Form
    </div>
  ),
  ScheduleForm: ({}: any) => (
    <div data-testid="schedule-form" role="form" aria-label="schedule form">
      Schedule Form
    </div>
  ),
}));

// Mock service data
const mockService = {
  id: 1,
  slug: 'test-service',
  name: 'Test Service',
  description: 'Test service description',
  imageUrl: 'https://example.com/image.jpg',
  status: 'active',
  provider: {
    id: 1,
    name: 'Test Provider',
    imageUrl: 'https://example.com/provider.jpg',
    providerUrl: 'https://example.com/provider',
    description: 'Test provider description',
  },
  price: {
    priceId: 1,
    originalPrice: 100,
    discountPrice: 90,
    finalPrice: 90,
  },
  availableIn: ['SP', 'RJ'],
  details: ['detail 1', 'detail 2'],
  serviceLimits: 'Test service limits text',
  keywords: ['test', 'service'],
  termsConditionsUrl: 'https://example.com/terms',
  preparations: 'Test preparations text',
};

const mockProvider = {
  id: '1',
  name: 'Test Provider',
  imageUrl: 'https://example.com/provider.jpg',
  providerUrl: 'https://example.com/provider',
  description: 'Test provider description',
  testimonials: [
    {
      id: '1',
      author: 'John Doe',
      location: 'São Paulo',
      content: 'Great service!',
      rating: 5,
      order: 1,
    },
  ],
};

describe('CheckoutForm', () => {
  const mockOnSubmit = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockPreparationsExpanded = false;
    mockLimitsExpanded = false;
    mockShouldShowExpandButton.mockReturnValue(true);
  });

  it('renders all form sections', () => {
    render(<CheckoutForm service={mockService} provider={mockProvider} onSubmit={mockOnSubmit} />);

    // Check for main sections
    expect(screen.getByText('Agendar serviço')).toBeInTheDocument();
    expect(screen.getByTestId('schedule-form')).toBeInTheDocument();
    expect(screen.getByTestId('address-form')).toBeInTheDocument();
    expect(screen.getByTestId('personal-info-form')).toBeInTheDocument();

    // Check for terms section with new title
    expect(
      screen.getByText('Você está de acordo com as condições do serviço?')
    ).toBeInTheDocument();
    expect(screen.getByText('O que está incluso')).toBeInTheDocument();
    expect(screen.getByText('Preparação')).toBeInTheDocument();
    expect(screen.getByText('Restrições')).toBeInTheDocument();
  });

  it('displays service details correctly', () => {
    render(<CheckoutForm service={mockService} provider={mockProvider} onSubmit={mockOnSubmit} />);

    // Check that service details are displayed directly (no accordion)
    // The MarkdownRenderer combines array items, so we check for the combined text
    expect(screen.getByText('detail 1detail 2')).toBeInTheDocument();
    expect(screen.getByText('Test preparations text')).toBeInTheDocument();
    expect(screen.getByText('Test service limits text')).toBeInTheDocument();

    // Check that the alert box text is displayed (text is split across elements)
    const alertTexts = screen.getAllByText((content, element) => {
      return (
        element?.textContent ===
        'A eventual aquisição das peças é de responsabilidade do cliente, não inclusas no valor.'
      );
    });
    expect(alertTexts.length).toBeGreaterThan(0);
    expect(alertTexts[0]).toBeInTheDocument();
  });

  it('handles terms checkbox interaction', async () => {
    render(<CheckoutForm service={mockService} provider={mockProvider} onSubmit={mockOnSubmit} />);

    const termsCheckbox = screen.getByRole('checkbox');
    expect(termsCheckbox).toHaveAttribute('aria-checked', 'false');

    // Check the terms
    fireEvent.click(termsCheckbox);
    expect(termsCheckbox).toHaveAttribute('aria-checked', 'true');
  });

  it('calls onSubmit when form is submitted', async () => {
    // Create a mock for the form state
    const mockFormState = {
      formState: { errors: {}, isValid: true },
      getValues: () => ({
        firstName: 'Test',
        lastName: 'User',
        date: new Date(),
        period: 'morning',
        cep: '01234567',
        street: 'Test Street',
        streetNumber: '123',
        neighborhood: 'Test Neighborhood',
        countryCode: '+55',
        city: 'Test City',
        state: 'SP',
        phone: '11999999999',
        cpf: '12345678909',
        email: '<EMAIL>',
        terms: true,
      }),
      trigger: jest.fn().mockResolvedValue(true),
      setValue: jest.fn(),
      handleSubmit: jest.fn().mockImplementation((cb) => {
        return (e) => {
          e?.preventDefault?.();
          return cb(mockFormState.getValues());
        };
      }),
      register: jest.fn(),
      control: { register: jest.fn(), unregister: jest.fn() },
      watch: jest.fn().mockReturnValue({ unsubscribe: jest.fn() }),
    };

    // Mock useForm to return our mock form state
    jest.spyOn(require('react-hook-form'), 'useForm').mockReturnValue(mockFormState);

    // Mock FormProvider
    jest
      .spyOn(require('react-hook-form'), 'FormProvider')
      .mockImplementation(({ children }) => <div>{children}</div>);

    render(<CheckoutForm service={mockService} provider={mockProvider} onSubmit={mockOnSubmit} />);

    // Find and click the continue button
    const continueButtons = screen.getAllByTestId('continue-button');
    fireEvent.click(continueButtons[0]);

    // Wait for the async validation to complete
    await waitFor(() => {
      // Check if onSubmit was called
      expect(mockOnSubmit).toHaveBeenCalled();
    });
  });

  it('renders service conditions with static content', () => {
    render(<CheckoutForm service={mockService} provider={mockProvider} onSubmit={mockOnSubmit} />);

    // Check that service conditions are displayed as static content (no accordion)
    expect(screen.getByText('O que está incluso')).toBeInTheDocument();
    expect(screen.getByText('Preparação')).toBeInTheDocument();
    expect(screen.getByText('Restrições')).toBeInTheDocument();

    // Check that content is visible without interaction
    // The MarkdownRenderer combines array items, so we check for the combined text
    expect(screen.getByText('detail 1detail 2')).toBeInTheDocument();
    expect(screen.getByText('Test preparations text')).toBeInTheDocument();
    expect(screen.getByText('Test service limits text')).toBeInTheDocument();

    // Check for CheckCircle icon in header
    const checkIcon = screen.getByRole('img', { name: 'CheckCircle' });
    expect(checkIcon).toBeInTheDocument();

    // Check for AlertCircle icon in alert box
    const alertIcon = screen.getByRole('img', { name: 'AlertCircle' });
    expect(alertIcon).toBeInTheDocument();
  });

  it('displays all content without expand/collapse functionality', () => {
    render(<CheckoutForm service={mockService} provider={mockProvider} onSubmit={mockOnSubmit} />);

    // Check that all content is always visible (no expand/collapse)
    // The MarkdownRenderer combines array items, so we check for the combined text
    expect(screen.getByText('detail 1detail 2')).toBeInTheDocument();
    expect(screen.getByText('Test preparations text')).toBeInTheDocument();
    expect(screen.getByText('Test service limits text')).toBeInTheDocument();

    // Check that there are no expand buttons
    const expandButtons = screen.queryAllByText('Ver mais');
    expect(expandButtons.length).toBe(0);
  });

  it('displays alert box with important information', () => {
    render(<CheckoutForm service={mockService} provider={mockProvider} onSubmit={mockOnSubmit} />);

    // Check that the alert box is displayed (text is split across elements)
    const alertTexts = screen.getAllByText((content, element) => {
      return (
        element?.textContent ===
        'A eventual aquisição das peças é de responsabilidade do cliente, não inclusas no valor.'
      );
    });
    expect(alertTexts.length).toBeGreaterThan(0);
    expect(alertTexts[0]).toBeInTheDocument();

    // Check that the alert icon is present
    const alertIcon = screen.getByRole('img', { name: 'AlertCircle' });
    expect(alertIcon).toBeInTheDocument();
  });

  it('renders with proper styling and structure', () => {
    render(<CheckoutForm service={mockService} provider={mockProvider} onSubmit={mockOnSubmit} />);

    // Check for the main header with CheckCircle icon
    const checkIcon = screen.getByRole('img', { name: 'CheckCircle' });
    expect(checkIcon).toBeInTheDocument();

    // Check for the main title
    expect(
      screen.getByText('Você está de acordo com as condições do serviço?')
    ).toBeInTheDocument();

    // Check that all sections are present
    expect(screen.getByText('O que está incluso')).toBeInTheDocument();
    expect(screen.getByText('Restrições')).toBeInTheDocument();
    expect(screen.getByText('Preparação')).toBeInTheDocument();

    // Check for external link
    const termsLink = screen.getByRole('link', { name: /ver condições gerais/i });
    expect(termsLink).toBeInTheDocument();

    // Check for checkbox
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).toBeInTheDocument();
  });

  it('shows external link for terms and conditions', () => {
    render(<CheckoutForm service={mockService} provider={mockProvider} onSubmit={mockOnSubmit} />);

    const termsLink = screen.getByRole('link', { name: /ver condições gerais/i });
    expect(termsLink).toHaveAttribute('href', mockService.termsConditionsUrl);
  });

  it('shows service summary card on mobile', () => {
    render(<CheckoutForm service={mockService} provider={mockProvider} onSubmit={mockOnSubmit} />);

    // Find all mobile summary containers
    const mobileSummaryContainers = screen.getAllByTestId('mobile-summary-container');

    // Get the first one (mobile view)
    const mobileContainer = mobileSummaryContainers[0];
    expect(mobileContainer).toHaveClass('w-full', 'overflow-hidden', 'md:hidden');

    // Find the service summary within the mobile container
    const serviceSummary = within(mobileContainer).getByTestId(
      `service-summary-${mockService.slug}`
    );
    expect(serviceSummary).toBeInTheDocument();
    expect(serviceSummary).toHaveAttribute('role', 'article');
    expect(serviceSummary).toHaveAttribute('aria-label', 'service summary');
  });

  it('validates form correctly with missing fields', () => {
    // Mock React's useState to control formIsValid state
    const useStateMock = jest.spyOn(React, 'useState');

    // First useState call in the component is for formIsValid
    // Set it to false to simulate invalid form
    useStateMock.mockImplementationOnce(() => [false, jest.fn()]);

    // Create a mock service with empty termsConditionsUrl (but not undefined)
    const serviceWithInvalidTerms = { ...mockService, termsConditionsUrl: '' };

    // Create a more complete mock for useForm
    const mockFormContext = {
      formState: {
        errors: { firstName: { message: 'Insira seu nome.' } },
        isValid: false,
      },
      getValues: () => ({
        firstName: '', // Empty required field
        lastName: 'Test',
        countryCode: '+55',
        phone: '11999999999',
        cpf: '12345678909',
        email: '<EMAIL>',
        cep: '01234567',
        street: 'Test Street',
        streetNumber: '123',
        neighborhood: 'Test Neighborhood',
        city: 'Test City',
        state: 'SP',
        date: new Date(),
        period: 'morning',
        terms: false, // Terms not accepted
      }),
      trigger: jest.fn(),
      setValue: jest.fn(),
      handleSubmit: jest.fn().mockImplementation((_cb) => jest.fn()), // Add handleSubmit mock
      register: jest.fn(),
      control: { register: jest.fn(), unregister: jest.fn() },
      // Mock watch to return a subscription object with unsubscribe method
      watch: jest.fn().mockReturnValue({ unsubscribe: jest.fn() }),
    };

    // Mock the useForm hook to return our mock form context
    jest.spyOn(require('react-hook-form'), 'useForm').mockReturnValue(mockFormContext);

    // Mock FormProvider
    jest
      .spyOn(require('react-hook-form'), 'FormProvider')
      .mockImplementation((props: any) => <div>{props.children}</div>);

    // Render the component
    render(
      <CheckoutForm
        service={serviceWithInvalidTerms}
        provider={mockProvider}
        onSubmit={mockOnSubmit}
      />
    );

    // Verify the continue button exists in the ServiceSummaryCard
    const continueButtons = screen.getAllByTestId('continue-button');
    expect(continueButtons.length).toBeGreaterThan(0);

    // Since we're mocking the form state, we can't directly test if the button is disabled
    // Instead, we'll verify that the form state has errors
    expect(mockFormContext.formState.errors.firstName).toBeDefined();
    expect(mockFormContext.formState.isValid).toBe(false);

    // Verify error message is displayed
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });

  it('handles service with empty termsConditionsUrl', () => {
    const serviceWithEmptyTerms = { ...mockService, termsConditionsUrl: '' };

    // Create a basic mock for useForm with handleSubmit
    const mockFormContext = {
      formState: { errors: {} },
      getValues: () => ({}),
      trigger: jest.fn(),
      setValue: jest.fn(),
      handleSubmit: jest.fn().mockImplementation((_cb) => jest.fn()),
      register: jest.fn(),
      control: { register: jest.fn(), unregister: jest.fn() },
      watch: jest.fn().mockReturnValue({ unsubscribe: jest.fn() }),
    };

    // Mock the useForm hook
    jest.spyOn(require('react-hook-form'), 'useForm').mockReturnValue(mockFormContext);

    // Mock FormProvider
    jest
      .spyOn(require('react-hook-form'), 'FormProvider')
      .mockImplementation((props: any) => <div>{props.children}</div>);

    render(
      <CheckoutForm
        service={serviceWithEmptyTerms}
        provider={mockProvider}
        onSubmit={mockOnSubmit}
      />
    );

    // Find the link by text content instead of role
    const termsLink = screen.getByText('Ver condições gerais').closest('a');
    expect(termsLink).toHaveAttribute('href', '');
  });

  it('maintains accessibility standards', () => {
    render(<CheckoutForm service={mockService} provider={mockProvider} onSubmit={mockOnSubmit} />);

    // Check for proper ARIA labels and roles
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).toHaveAttribute('id', 'terms-checkbox');

    const checkboxLabel = screen.getByLabelText(
      'Confirmo que estou de acordo com as condições descritas acima.'
    );
    expect(checkboxLabel).toBeInTheDocument();

    // Check for proper heading hierarchy
    const mainHeading = screen.getByRole('heading', { level: 1 });
    expect(mainHeading).toHaveTextContent('Agendar serviço');

    const serviceConditionsHeading = screen.getByRole('heading', { level: 4 });
    expect(serviceConditionsHeading).toHaveTextContent(
      'Você está de acordo com as condições do serviço?'
    );
  });

  it('renders responsive design classes correctly', () => {
    render(<CheckoutForm service={mockService} provider={mockProvider} onSubmit={mockOnSubmit} />);

    // Check for responsive grid classes
    const mainContainer = screen.getByText('Agendar serviço').closest('div')
      ?.parentElement?.parentElement;
    expect(mainContainer).toHaveClass('flex', 'flex-col', 'gap-12', 'md:grid', 'md:grid-cols-3');

    // Check for mobile-specific classes (there are multiple, so get all)
    const mobileContainers = screen.getAllByTestId('mobile-summary-container');
    expect(mobileContainers.length).toBeGreaterThan(0);
    expect(mobileContainers[0]).toHaveClass('w-full', 'overflow-hidden', 'md:hidden');
  });
});
