import CheckoutPage from '@/src/app/(pages)/servicos/[subcategory]/[slug]/checkout/page';
import { axiosInstance } from '@/src/app/_utils';
import { render, screen } from '@testing-library/react';

// Mock the components
jest.mock('@/src/app/_components', () => ({
  ServiceNotFound: () => <div data-testid="service-not-found">Service not found</div>,
  CheckoutPageSkeleton: () => <div data-testid="checkout-skeleton">Loading...</div>,
}));

// Mock the axios instance
jest.mock('@/src/app/_utils', () => ({
  axiosInstance: {
    get: jest.fn(),
  },
}));

// Mock the Suspense component
jest.mock('react', () => {
  const actual = jest.requireActual('react');
  return {
    ...actual,
    Suspense: ({ children }: any) => children,
  };
});

// Mock the CheckoutPageClient component
jest.mock('@/src/app/(pages)/servicos/[subcategory]/[slug]/checkout/CheckoutPageClient', () => {
  return function MockCheckoutPageClient({ slug, initialServiceData }: any) {
    return (
      <div data-testid="checkout-page-client">
        <div>Slug: {slug}</div>
        <div>Service: {initialServiceData ? initialServiceData.name : 'No service data'}</div>
      </div>
    );
  };
});

describe('Checkout Page Server Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders ServiceNotFound when no slug is provided', async () => {
    const props = {
      params: Promise.resolve({ slug: '' }),
    };

    render(await CheckoutPage(props));

    expect(screen.getByTestId('service-not-found')).toBeInTheDocument();
  });

  it('renders CheckoutPageClient with service data when API call succeeds', async () => {
    // Mock service data
    const mockServiceData = {
      id: '1',
      name: 'Test Service',
      price: {
        finalPrice: 100,
      },
    };

    // Mock successful API response
    (axiosInstance.get as jest.Mock).mockResolvedValue({
      data: mockServiceData,
    });

    // Create props with valid slug
    const props = {
      params: Promise.resolve({ slug: 'test-service' }),
    };

    // Render the component
    render(await CheckoutPage(props));

    // Verify checkout client component is rendered with correct props
    const clientComponent = screen.getByTestId('checkout-page-client');
    expect(clientComponent).toBeInTheDocument();
    expect(screen.getByText('Slug:')).toBeInTheDocument();
    expect(screen.getByText('Service: Test Service')).toBeInTheDocument();

    // Verify the API was called with the correct slug
    expect(axiosInstance.get).toHaveBeenCalledWith(
      '/service-type/test-service',
      expect.any(Object)
    );
  });

  it('renders ServiceNotFound when API call fails', async () => {
    // Mock failed API response
    (axiosInstance.get as jest.Mock).mockRejectedValue(new Error('API Error'));

    // Create props with valid slug
    const props = {
      params: Promise.resolve({ slug: 'test-service' }),
    };

    // Render the component
    render(await CheckoutPage(props));

    // Verify that ServiceNotFound is rendered
    expect(screen.getByTestId('service-not-found')).toBeInTheDocument();
  });
});
