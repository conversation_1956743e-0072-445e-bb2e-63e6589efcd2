import { CancellationInfo } from '@/src/app/_components/Pages/Home/CancellationInfo';
import { render, screen } from '@testing-library/react';

describe('CancellationInfo', () => {
  it('should render the section with correct title and text', () => {
    render(<CancellationInfo />);

    // Verifica se o título está correto
    expect(screen.getByText('Precisou cancelar ou reagendar?')).toBeInTheDocument();

    // Verifica se o texto descritivo está correto usando regex para texto parcial
    expect(screen.getByText(/Central de Atendimento da Europ/)).toBeInTheDocument();
    expect(screen.getByText(/0800 202 4011/)).toBeInTheDocument();
  });

  it('should have the correct styling', () => {
    const { container } = render(<CancellationInfo />);

    // Verifica se a div externa tem a classe border-t-[1px]
    const outerDiv = container.firstChild;
    expect(outerDiv).toHaveClass('border-t-[1px]');
    expect(outerDiv).toHaveClass('border-t-gray-200');

    // Verifica se o container interno tem os paddings corretos
    const flexContainer = container.querySelector('.flex');
    expect(flexContainer).toHaveClass('px-8');
    expect(flexContainer).toHaveClass('lg:px-12');
  });
});
