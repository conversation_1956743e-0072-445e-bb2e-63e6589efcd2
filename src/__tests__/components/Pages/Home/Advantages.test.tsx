import { Advantages } from '@/src/app/_components/Pages/Home/Advantages';
import { render, screen } from '@testing-library/react';

// Mock the ChevronRight icon from lucide-react
jest.mock('lucide-react', () => ({
  ChevronRight: () => <div data-testid="chevron-right-icon">ChevronRight Icon</div>,
}));

describe('Advantages Component', () => {
  it('renders the advantages section with correct content', () => {
    const { container } = render(<Advantages />);

    // Check for the main container
    const mainContainer = container.firstChild;
    expect(mainContainer).toHaveClass('z-1', 'relative');

    // Check for the three advantage items
    expect(screen.getByText('90 dias de garantia')).toBeInTheDocument();
    expect(screen.getByText('Agendamento imediato')).toBeInTheDocument();
    expect(screen.getByText('Preço fechado')).toBeInTheDocument();

    // Check for the descriptions
    expect(
      screen.getByText('Garantia de 90 dias em todos os serviços realizados')
    ).toBeInTheDocument();
    expect(
      screen.getByText('Agende seu serviço para o mesmo dia ou dia seguinte')
    ).toBeInTheDocument();
    expect(
      screen.getByText('Sem surpresas, você sabe exatamente quanto vai pagar')
    ).toBeInTheDocument();

    // Check for the ChevronRight icons
    const icons = screen.getAllByTestId('chevron-right-icon');
    expect(icons).toHaveLength(3);
  });

  it('has the correct layout classes for responsive design', () => {
    const { container } = render(<Advantages />);

    // Check for the main container
    const mainContainer = container.firstChild;
    expect(mainContainer).toHaveClass('z-1', 'relative');

    // Check for the inner container
    const innerContainer = mainContainer?.firstChild;
    expect(innerContainer).toHaveClass('mx-auto', 'max-w-7xl');

    // Check for the flex container
    const flexContainer = innerContainer?.firstChild;
    expect(flexContainer).toHaveClass('flex', 'flex-col', 'md:flex-row');

    // Check for the advantage items
    const advantageItems = container.querySelectorAll('.flex-1');
    expect(advantageItems).toHaveLength(3);

    // Check for the icon containers
    const iconContainers = container.querySelectorAll('.flex-shrink-0');
    expect(iconContainers).toHaveLength(3);

    // Check that each advantage has the correct structure
    advantageItems.forEach((item) => {
      // Check that the flex container has items-start to align at the top
      const flexContainer = item.querySelector('.flex');
      expect(flexContainer).toHaveClass('items-start');

      // Check that the icon has the correct classes
      const iconContainer = item.querySelector('.flex-shrink-0');
      expect(iconContainer).toHaveClass('mr-2', 'h-8');

      // Check that the title and description are in the same container
      const contentContainer = item.querySelector('.flex > div:nth-child(2)');
      expect(contentContainer).toBeInTheDocument();

      const title = contentContainer?.querySelector('h3');
      const description = contentContainer?.querySelector('p');

      expect(title).toBeInTheDocument();
      expect(description).toBeInTheDocument();
      expect(description).toHaveClass('mt-2');
    });
  });
});
