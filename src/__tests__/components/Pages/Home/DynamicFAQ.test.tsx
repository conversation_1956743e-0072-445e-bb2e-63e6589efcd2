import { FAQ } from '@/src/app/_components/Pages/Home/FAQ';
import { render, screen } from '@testing-library/react';

// Mock dos componentes de Accordion
jest.mock('@/src/app/_components/Ui/accordion', () => ({
  Accordion: ({ children, type, className, defaultValue, collapsible }: any) => (
    <div
      data-testid="accordion"
      data-type={type}
      data-defaultValue={defaultValue}
      data-collapsible={collapsible ? 'true' : 'false'}
      className={className}
    >
      {children}
    </div>
  ),
  AccordionItem: ({ children, value, className }: any) => (
    <div data-testid="accordion-item" data-value={value} className={className}>
      {children}
    </div>
  ),
  AccordionTrigger: ({ children, className }: any) => (
    <button
      data-testid="accordion-trigger"
      className={className}
      onClick={() => {}}
      aria-expanded="false"
    >
      {children}
    </button>
  ),
  AccordionContent: ({ children, className }: any) => (
    <div data-testid="accordion-content" className={className}>
      {children}
    </div>
  ),
}));

describe('FAQ Component', () => {
  it('should render the FAQ section with correct title', () => {
    render(<FAQ />);

    // Verifica se o título da seção está correto
    expect(screen.getByText('Dúvidas frequentes')).toBeInTheDocument();
  });

  it('should render an Accordion with single type and collapsible', () => {
    render(<FAQ />);

    // Verifica se o componente Accordion é renderizado com type="single"
    const accordion = screen.getByTestId('accordion');
    expect(accordion).toBeInTheDocument();
    expect(accordion).toHaveAttribute('data-type', 'single');
    expect(accordion).toHaveAttribute('data-defaultValue', 'faq-0');
    expect(accordion).toHaveAttribute('data-collapsible', 'true');
  });

  it('should render all FAQ items with questions and answers', () => {
    render(<FAQ />);

    // Verifica se todos os itens de acordo são renderizados
    const accordionItems = screen.getAllByTestId('accordion-item');
    expect(accordionItems).toHaveLength(9); // Devem haver 9 FAQs

    // Verifica se algumas perguntas estão presentes
    expect(
      screen.getByText('Como funciona o serviço GetNinjas + Europ Assistance?')
    ).toBeInTheDocument();
    expect(screen.getByText('Por que devo confiar nesse modelo?')).toBeInTheDocument();
    expect(screen.getByText('Quais os diferenciais desse serviço?')).toBeInTheDocument();
    expect(
      screen.getByText('Como faço para agendar um serviço GetNinjas + Europ Assistance?')
    ).toBeInTheDocument();
    expect(screen.getByText('O serviço tem garantia?')).toBeInTheDocument();
    expect(screen.getByText('Quanto tempo leva para o profissional chegar?')).toBeInTheDocument();
    expect(
      screen.getByText('Os valores exibidos incluem todos os custos do serviço?')
    ).toBeInTheDocument();
    expect(screen.getByText('Posso cancelar ou reagendar o serviço?')).toBeInTheDocument();
    expect(
      screen.getByText('Não encontrei o serviço que eu preciso. O que fazer?')
    ).toBeInTheDocument();

    // Verifica se algumas respostas estão presentes
    expect(screen.getByText(/O GetNinjas \+ Europ Assistance é uma parceria/)).toBeInTheDocument();
    expect(screen.getByText(/O GetNinjas tem mais de 13 anos de experiência/)).toBeInTheDocument();
    expect(screen.getByText(/Preço fechado: sem surpresas/)).toBeInTheDocument();
    expect(screen.getByText(/É simples! Escolha o serviço desejado/)).toBeInTheDocument();
    expect(
      screen.getByText(/Sim, todos os serviços realizados têm garantia de 90 dias/)
    ).toBeInTheDocument();
  });

  it('should apply correct styling to accordion components', () => {
    render(<FAQ />);

    // Verifica se o accordion tem as classes de estilo corretas
    const accordion = screen.getByTestId('accordion');
    expect(accordion.className).toContain('w-full');
    expect(accordion.className).toContain('space-y-1');
    expect(accordion.className).toContain('rounded-2xl');
    expect(accordion.className).toContain('bg-slate-50');

    // Verifica se os items de accordeon têm a classe de borda correta
    const accordionItems = screen.getAllByTestId('accordion-item');
    for (const item of accordionItems) {
      expect(item.className).toContain('overflow-hidden');
      expect(item.className).toContain('border-b');
      expect(item.className).toContain('border-slate-200');
    }

    // Verifica se os triggers têm a classe de texto correta
    const triggers = screen.getAllByTestId('accordion-trigger');
    for (const trigger of triggers) {
      expect(trigger.className).toContain('text-left');
      expect(trigger.className).toContain('text-base');
      expect(trigger.className).toContain('font-semibold');
      expect(trigger.className).toContain('text-slate-900');
      expect(trigger.className).toContain('hover:no-underline');
    }

    // Verifica se os conteúdos têm a classe de texto correta
    const contents = screen.getAllByTestId('accordion-content');
    for (const content of contents) {
      expect(content.className).toContain('text-muted-foreground');
    }
  });

  it('should have a flex layout with correct structure', () => {
    const { container } = render(<FAQ />);

    // Verifica se o container principal tem as classes corretas
    const mainContainer = container.firstChild as HTMLElement;
    expect(mainContainer.className).toContain('relative');
    expect(mainContainer.className).toContain('z-30');
    expect(mainContainer.className).toContain('bg-white');

    // Verifica se o container flex tem as classes corretas
    const flexContainer = mainContainer.firstChild as HTMLElement;
    expect(flexContainer.className).toContain('flex');
    expect(flexContainer.className).toContain('flex-col');
    expect(flexContainer.className).toContain('md:flex-row');

    // Verifica se o título está no container correto
    const titleContainer = screen.getByText('Dúvidas frequentes').closest('div');
    expect(titleContainer).toHaveClass('md:w-1/4');

    // Verifica se o accordion está no container correto
    const accordionParent = screen.getByTestId('accordion').parentElement;
    expect(accordionParent).toHaveClass('md:w-3/4');
  });
});
