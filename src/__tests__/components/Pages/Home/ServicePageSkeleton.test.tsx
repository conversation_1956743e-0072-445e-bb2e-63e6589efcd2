import { ServicePageSkeleton } from '@/src/app/_components/Pages/Home/ServicePageSkeleton';
import { render, screen } from '@testing-library/react';

// Mock dos componentes Skeleton, Card e CardContent
jest.mock('@/src/app/_components', () => ({
  Skeleton: ({ className }: { className: string }) => (
    <div data-testid="skeleton" className={className} />
  ),
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>
      {children}
    </div>
  ),
  CardContent: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card-content" className={className}>
      {children}
    </div>
  ),
}));

describe('ServicePageSkeleton', () => {
  it('should render the skeleton structure', () => {
    render(<ServicePageSkeleton />);

    // Verifica se o container principal existe
    const container = screen.getByText(
      (content, element) =>
        element?.tagName.toLowerCase() === 'div' && element?.className.includes('container mx-auto')
    );
    expect(container).toBeInTheDocument();
  });

  it('should render the breadcrumb skeleton', () => {
    render(<ServicePageSkeleton />);

    // Verifica se o skeleton para breadcrumb existe
    const breadcrumbSkeleton = screen.getAllByTestId('skeleton')[0];
    expect(breadcrumbSkeleton).toBeInTheDocument();
    expect(breadcrumbSkeleton.className).toContain('mb-4 h-8 w-48');
  });

  it('should render the hero section', () => {
    render(<ServicePageSkeleton />);

    // Verifica se a seção hero existe
    const heroSection = screen.getByText(
      (content, element) =>
        element?.tagName.toLowerCase() === 'section' &&
        element?.className.includes('mb-8 mt-4 overflow-hidden rounded-md')
    );
    expect(heroSection).toBeInTheDocument();

    // Verifica se a estrutura flex existe dentro da seção hero
    const flexContainer = screen.getByText(
      (content, element) =>
        element?.tagName.toLowerCase() === 'div' &&
        element?.className.includes('flex flex-col items-stretch lg:flex-row')
    );
    expect(flexContainer).toBeInTheDocument();
  });

  it('should render service information skeletons', () => {
    render(<ServicePageSkeleton />);

    // Conta o número de skeletons para verificar se todos estão sendo renderizados
    const allSkeletons = screen.getAllByTestId('skeleton');
    expect(allSkeletons.length).toBeGreaterThan(10); // Deve haver muitos skeletons

    // Verifica se há um skeleton para o título do serviço
    const titleSkeleton = allSkeletons.find(
      (skeleton) =>
        skeleton.className.includes('mb-4 h-8 w-3/4') ||
        skeleton.className.includes('mb-4 h-10 w-3/4') ||
        skeleton.className.includes('mb-4 h-12 w-3/4')
    );
    expect(titleSkeleton).toBeInTheDocument();
  });

  it('should render service image skeleton', () => {
    render(<ServicePageSkeleton />);

    // Verifica se há um container para a imagem do serviço
    const imageContainer = screen.getByText(
      (content, element) =>
        element?.tagName.toLowerCase() === 'div' && element?.className.includes('relative h-48')
    );
    expect(imageContainer).toBeInTheDocument();

    // Verifica se há um skeleton para a imagem
    const imageSkeleton = screen
      .getAllByTestId('skeleton')
      .find((skeleton) => skeleton.className === 'h-full w-full');
    expect(imageSkeleton).toBeInTheDocument();
  });

  it('should render service details section', () => {
    render(<ServicePageSkeleton />);

    // Verifica se a seção de detalhes existe (usando getAllByText para pegar todas as seções)
    const detailsSections = screen.getAllByText(
      (content, element) =>
        element?.tagName.toLowerCase() === 'section' && element?.className.includes('mb-12')
    );
    expect(detailsSections.length).toBeGreaterThan(0);

    // Verifica se o grid de detalhes existe
    const detailsGrid = screen.getByText(
      (content, element) =>
        element?.tagName.toLowerCase() === 'div' &&
        element?.className.includes('grid grid-cols-1 gap-8 lg:grid-cols-3')
    );
    expect(detailsGrid).toBeInTheDocument();
  });

  it('should render card components', () => {
    render(<ServicePageSkeleton />);

    // Verifica se o componente Card está sendo renderizado
    const cards = screen.getAllByTestId('card');
    expect(cards.length).toBeGreaterThan(0);

    // Verifica se o componente CardContent está sendo renderizado
    const cardContents = screen.getAllByTestId('card-content');
    expect(cardContents.length).toBeGreaterThan(0);

    // Verifica se o card tem a classe correta
    expect(cards[0].className).toContain('border-none bg-card shadow-lg');
  });
});
