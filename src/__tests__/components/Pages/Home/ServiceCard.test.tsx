import { ServiceCard } from '@/src/app/_components/Pages/Home/ServiceCard';
import { trackViewItem } from '@/src/app/_functions/analytics/common/trackViewItem';
import { fireEvent, render, screen } from '@testing-library/react';
import { useRouter } from 'next/navigation';

// Mock das dependências externas, não do componente em si
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock do Next.js Image
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, className, layout }: any) => (
    <img src={src} alt={alt} className={className} data-layout={layout} />
  ),
}));

// Mock do Next.js Link
jest.mock('next/link', () => {
  return function Link({ children, href, className, onClick }: any) {
    return (
      <a href={href} className={className} onClick={onClick} data-testid="next-link">
        {children}
      </a>
    );
  };
});

// Mock de componentes do projeto
jest.mock('@/src/app/_components', () => ({
  Button: ({ asChild, children, className, size, onClick }: any) => (
    <button
      className={className}
      data-as-child={asChild ? 'true' : 'false'}
      data-size={size}
      onClick={onClick}
    >
      {children}
    </button>
  ),
  Card: ({ children, className, onClick, ...props }: any) => (
    <div className={className} onClick={onClick} data-testid="card" {...props}>
      {children}
    </div>
  ),
  CardHeader: ({ children }: any) => <div data-testid="card-header">{children}</div>,
  CardContent: ({ children, className }: any) => (
    <div data-testid="card-content" className={className}>
      {children}
    </div>
  ),
  CardTitle: ({ children, className }: any) => (
    <div data-testid="card-title" className={className}>
      {children}
    </div>
  ),
}));

// Mock das funções de analytics
jest.mock('@/src/app/_functions/analytics/common/trackViewItem', () => ({
  trackViewItem: jest.fn(),
}));

// Mock dos ícones Lucide
jest.mock('lucide-react', () => ({
  Calendar: () => <span data-testid="calendar-icon">Calendar Icon</span>,
}));

// Mock das funções de utilidade
jest.mock('@/src/app/_utils/stringUtils', () => ({
  capitalizeFirstLetter: (str: string) => str.charAt(0).toUpperCase() + str.slice(1),
}));

describe('ServiceCard', () => {
  const mockService = {
    id: '1',
    name: 'service test',
    slug: 'service-test',
    description: 'This is a test service',
    imageUrl: '/test-image.jpg',
    price: {
      finalPrice: 100,
      originalPrice: 120,
      discountPrice: 20,
    },
    provider: {
      name: 'Test Provider',
      imageUrl: '/provider-image.jpg',
    },
  };

  const mockRouter = {
    push: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
  });

  it('should render the service card with correct data', () => {
    render(<ServiceCard service={mockService} />);

    // Verifica se o nome do serviço é renderizado com primeira letra maiúscula
    expect(screen.getByTestId('card-title')).toHaveTextContent('Service test');

    // Verifica se a descrição é exibida
    expect(screen.getByText('This is a test service')).toBeInTheDocument();

    // Verifica se o preço é formatado corretamente
    expect(screen.getByText('R$ 100,00')).toBeInTheDocument();

    // Verifica se o preço original (riscado) é exibido quando há desconto
    const originalPrice = screen.getByText(/R\$ 120,00/);
    expect(originalPrice).toBeInTheDocument();
    expect(originalPrice.className).toContain('line-through');
  });

  it('should navigate to service page when card is clicked', () => {
    render(<ServiceCard service={mockService} />);

    // Simula o clique no card
    fireEvent.click(screen.getByTestId('card'));

    // Verifica se o router.push foi chamado com o slug correto
    expect(mockRouter.push).toHaveBeenCalledWith('/service/service-test');
  });

  it('should track analytics when scheduling button is clicked', () => {
    render(<ServiceCard service={mockService} />);

    // Encontra o link de agendamento
    const scheduleLinks = screen.getAllByRole('link');
    const scheduleLink = scheduleLinks.find((link) => link.textContent?.includes('Agendar agora'));

    // Simula o clique no link
    if (scheduleLink) {
      fireEvent.click(scheduleLink);
    }

    // Verifica se a função de tracking foi chamada com os parâmetros corretos
    expect(trackViewItem).toHaveBeenCalledWith({
      id: '1',
      name: 'service test',
      price: 100,
    });
  });

  it('should render custom button when renderButton prop is provided', () => {
    const customButton = () => <button data-testid="custom-button">Custom Button</button>;

    render(<ServiceCard service={mockService} renderButton={customButton} />);

    // Verifica se o botão personalizado é renderizado
    expect(screen.getByTestId('custom-button')).toBeInTheDocument();

    // Verifica se os botões padrão não são renderizados
    expect(screen.queryByText('Agendar agora')).not.toBeInTheDocument();
    expect(screen.queryByText('Detalhes')).not.toBeInTheDocument();
  });

  it('should not display provider image when provider is not available', () => {
    const serviceWithoutProvider = {
      ...mockService,
      provider: undefined,
    };

    render(<ServiceCard service={serviceWithoutProvider} />);

    // Verifica se existem apenas 1 imagem (a do serviço, não a do provider)
    const images = screen.getAllByRole('img');
    expect(images).toHaveLength(1);
  });
});
