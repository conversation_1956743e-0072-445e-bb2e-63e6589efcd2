import { ServiceCardSkeleton } from '@/src/app/_components/Pages/Home/ServiceCardSkeleton';
import { render, screen } from '@testing-library/react';

// Mock dos componentes UI usados no ServiceCardSkeleton
jest.mock('@/src/app/_components', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>
      {children}
    </div>
  ),
  CardHeader: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card-header">{children}</div>
  ),
  CardContent: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card-content" className={className}>
      {children}
    </div>
  ),
  Skeleton: ({ className }: { className?: string }) => (
    <div data-testid="skeleton" className={className} />
  ),
}));

describe('ServiceCardSkeleton', () => {
  it('should render the skeleton components with proper structure', () => {
    render(<ServiceCardSkeleton />);

    // Verifica se o componente Card foi renderizado
    expect(screen.getByTestId('card')).toBeInTheDocument();

    // Verifica se o CardHeader foi renderizado
    expect(screen.getByTestId('card-header')).toBeInTheDocument();

    // Verifica se o CardContent foi renderizado
    expect(screen.getByTestId('card-content')).toBeInTheDocument();

    // Verifica se os componentes Skeleton foram renderizados
    const skeletons = screen.getAllByTestId('skeleton');

    // Deve haver múltiplos elementos Skeleton
    // 1 para imagem, 1 para título, 3 para categorias, 3 para detalhes, 1 para disponibilidade, 2 para preço, 2 para botões
    expect(skeletons.length).toBeGreaterThan(10);
  });

  it('should have the proper flex classes for layout', () => {
    render(<ServiceCardSkeleton />);

    // Verifica se o Card tem as classes de layout corretas
    const card = screen.getByTestId('card');
    expect(card.className).toContain('flex');
    expect(card.className).toContain('h-full');
    expect(card.className).toContain('flex-col');

    // Verifica se o CardContent tem as classes de layout corretas
    const cardContent = screen.getByTestId('card-content');
    expect(cardContent.className).toContain('flex');
    expect(cardContent.className).toContain('flex-grow');
    expect(cardContent.className).toContain('flex-col');
  });

  it('should render a skeleton for the image with aspect-video', () => {
    render(<ServiceCardSkeleton />);

    // Verifica se existe um container com aspect-video para a imagem
    const imageContainer = screen.getByTestId('card').querySelector('.aspect-video');
    expect(imageContainer).toBeInTheDocument();

    // Verifica se o skeleton da imagem tem as classes corretas
    const imageSkeletons = screen.getAllByTestId('skeleton');
    const imageSkeleton = imageSkeletons.find(
      (skeleton) => skeleton.className?.includes('h-full') && skeleton.className?.includes('w-full')
    );
    expect(imageSkeleton).toBeInTheDocument();
    expect(imageSkeleton?.className).toContain('rounded-t-lg');
  });
});
