import ServiceSummary from '@/src/app/_components/Pages/Home/ServiceSummary';
import { ServiceType } from '@/src/app/_interfaces';
import { render } from '@testing-library/react';

// Mock the ServiceSummaryCard component
jest.mock('@/src/app/_components/Pages/Home/ServiceSummaryCard', () => ({
  ServiceSummaryCard: ({ service }: { service: ServiceType | null }) => {
    if (!service) {
      return (
        <div data-testid="service-summary-card-error">
          <p>Não foi possível carregar os detalhes do serviço.</p>
        </div>
      );
    }

    return (
      <div data-testid="service-summary-card">
        <h2 data-testid="service-name">{service.name}</h2>
        <p data-testid="service-description">{service.description}</p>
        <p data-testid="service-price">R$ {service.price.finalPrice}</p>
      </div>
    );
  },
}));

describe('ServiceSummary Component (Integration Tests)', () => {
  // This variable will hold our real API data
  let realApiService: ServiceType | null = null;

  // Fetch real data before running tests
  beforeAll(async () => {
    // Increase timeout to 30 seconds for API calls
    jest.setTimeout(30000);
    try {
      console.warn('🌐 Fetching real API data for integration tests...');

      // Make a real API call
      const apiUrl = 'https://ecommerce-bff-api-smoke.getninjas.io/api/v1/service-type/list';
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'service-provider': 'EUR',
          Accept: 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }

      const data = await response.json();

      // Process the nested API response to extract a service
      if (data && data.categories) {
        for (const category of data.categories) {
          if (category.subcategories) {
            for (const subcategory of category.subcategories) {
              if (subcategory.services && subcategory.services.length > 0) {
                // Use the first service for testing
                realApiService = {
                  ...subcategory.services[0],
                  categoryName: category.name,
                  categorySlug: category.slug,
                  subcategoryName: subcategory.name,
                  subcategorySlug: subcategory.slug,
                };
                console.warn(`✅ Successfully fetched service: ${realApiService.name}`);
                break;
              }
            }
            if (realApiService) break;
          }
        }
      }
    } catch (error) {
      console.error('❌ Error fetching API data:', error);
    }
  });

  // Setup for mocking fetch
  beforeEach(() => {
    // Reset mocks between tests
    jest.clearAllMocks();

    // Mock the fetch function to return real API data
    global.fetch = jest.fn().mockImplementation(async (url: string) => {
      if (url.includes('/service-type/') && realApiService) {
        const slug = url.split('/').pop();
        if (slug === realApiService.slug) {
          return {
            ok: true,
            json: async () => realApiService,
          };
        }
      }

      // Return a 404 for invalid slugs
      return {
        ok: false,
        statusText: 'Not Found',
        json: async () => {
          throw new Error('Not Found');
        },
      };
    });
  });

  it('renders with real API data', async () => {
    // Skip test if no API data is available
    if (!realApiService) {
      console.warn('⚠️ Skipping test due to missing API data');
      return;
    }

    // Render the component with the slug from our real service
    const component = await ServiceSummary({ slug: realApiService.slug });
    const { findByTestId } = render(component);

    // Check that the component renders with real API data
    const summaryCard = await findByTestId('service-summary-card');
    expect(summaryCard).toBeInTheDocument();

    const serviceName = await findByTestId('service-name');
    expect(serviceName.textContent).toBe(realApiService.name);

    const serviceDescription = await findByTestId('service-description');
    expect(serviceDescription.textContent).toBe(realApiService.description);

    // Log whether we're using real or mock data
    console.warn('✅ Test running with REAL API data');
  });

  it('handles API errors gracefully', async () => {
    // Override the fetch mock to simulate an error
    global.fetch = jest.fn().mockImplementation(async () => {
      throw new Error('API error');
    });

    // Render the component with an invalid slug
    const component = await ServiceSummary({ slug: 'invalid-slug' });

    // The component should return a ServiceSummaryCard with null service
    expect(component).not.toBeNull();

    // Render the component to check if it handles the error correctly
    const { getByTestId } = render(component);

    // The ServiceSummaryCard should show an error message
    expect(getByTestId('service-summary-card-error')).toBeInTheDocument();
  });
});
