import { ServiceCarouselSkeleton } from '@/src/app/_components/Pages/Home/ServiceCarouselSkeleton';
import { render, screen } from '@testing-library/react';

describe('ServiceCarouselSkeleton', () => {
  it('renders the skeleton with correct title', () => {
    render(<ServiceCarouselSkeleton />);

    // Check if the title is rendered
    expect(screen.getByText('Disponível para agendar agora')).toBeInTheDocument();
  });

  it('renders the correct number of skeleton cards', () => {
    render(<ServiceCarouselSkeleton />);

    // There should be 4 skeleton cards
    const skeletonCards = document.querySelectorAll('.animate-pulse');
    expect(skeletonCards.length).toBe(4);
  });

  it('renders skeleton elements for each card', () => {
    render(<ServiceCarouselSkeleton />);

    // Check if each card has the expected skeleton elements
    const imageSkeletons = document.querySelectorAll('.h-40.w-full.rounded-xl.bg-gray-200');
    const titleSkeletons = document.querySelectorAll('.h-5.w-2\\/3.rounded.bg-gray-300');
    const priceSmallSkeletons = document.querySelectorAll('.h-4.w-1\\/3.rounded.bg-gray-200');
    const priceLargeSkeletons = document.querySelectorAll('.h-6.w-1\\/2.rounded.bg-gray-300');

    // Each card should have one of each skeleton element
    expect(imageSkeletons.length).toBe(4);
    expect(titleSkeletons.length).toBe(4);
    expect(priceSmallSkeletons.length).toBe(4);
    expect(priceLargeSkeletons.length).toBe(4);
  });

  it('renders subcategory skeleton elements', () => {
    render(<ServiceCarouselSkeleton />);

    // Check if each card has the expected subcategory skeleton elements
    const subcategorySkeletons = document.querySelectorAll(
      '.h-4.w-4\\/5.rounded.bg-gray-200, .h-4.w-3\\/5.rounded.bg-gray-200, .h-4.w-2\\/3.rounded.bg-gray-200'
    );

    // Each card should have 3 subcategory skeleton elements, so 12 in total
    expect(subcategorySkeletons.length).toBe(12);
  });
});
