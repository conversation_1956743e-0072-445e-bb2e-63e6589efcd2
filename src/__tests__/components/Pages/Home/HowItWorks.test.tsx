import { HowItWorks } from '@/src/app/_components/Pages/Home/HowItWorks';
import { render, screen } from '@testing-library/react';

// Mock the Lucide icons
jest.mock('lucide-react', () => ({
  Pointer: () => <div data-testid="pointer-icon">Pointer Icon</div>,
  Calendar: () => <div data-testid="calendar-icon">Calendar Icon</div>,
  HardHat: () => <div data-testid="hardhat-icon">HardHat Icon</div>,
  Receipt: () => <div data-testid="receipt-icon">Receipt Icon</div>,
}));

describe('HowItWorks Component', () => {
  it('renders the section with correct title', () => {
    render(<HowItWorks />);

    // Check for the title
    expect(screen.getByText('Como funciona?')).toBeInTheDocument();
  });

  it('renders all four steps with correct content', () => {
    render(<HowItWorks />);

    // Check for step numbers
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('4')).toBeInTheDocument();

    // Check for step titles
    expect(screen.getByText('Selecione o serviço')).toBeInTheDocument();
    expect(screen.getByText('Escolha a melhor data')).toBeInTheDocument();
    expect(screen.getByText('Receba o profissional')).toBeInTheDocument();
    expect(screen.getByText('Aprove o pagamento')).toBeInTheDocument();

    // Check for icons
    expect(screen.getByTestId('pointer-icon')).toBeInTheDocument();
    expect(screen.getByTestId('calendar-icon')).toBeInTheDocument();
    expect(screen.getByTestId('hardhat-icon')).toBeInTheDocument();
    expect(screen.getByTestId('receipt-icon')).toBeInTheDocument();
  });

  it('has the correct layout classes for responsive design', () => {
    const { container } = render(<HowItWorks />);

    // Check for the main container
    const mainContainer = container.firstChild;
    expect(mainContainer).toHaveClass('mx-auto');

    // Check for the steps container
    const stepsContainer = screen.getByText('Como funciona?').parentElement?.nextSibling;
    expect(stepsContainer).toHaveClass('flex', 'flex-col');
    expect(stepsContainer).toHaveClass('md:flex-row');
  });
});
