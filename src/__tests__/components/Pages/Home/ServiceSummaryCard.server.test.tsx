import { render, screen } from '@testing-library/react';

// Create a mock ServiceSummaryCard component
function MockServiceSummaryCard({ service, showButton = false, onSubmit, formId }: any) {
  if (!service) {
    return (
      <div data-testid="card">
        <div data-testid="card-content">
          <h2>Resumo do serviço</h2>
          <div className="bg-red-50 text-red-700">
            <p>Não foi possível carregar os detalhes do serviço.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div data-testid="card">
      <div data-testid="card-content">
        <h2>Resumo do serviço</h2>

        <div>
          <p>{service.categoryName}</p>
          {service.subcategoryName && service.subcategoryName !== service.categoryName && (
            <p>{service.subcategoryName}</p>
          )}
          <div>
            <div>{service.name}</div>
          </div>
        </div>

        <hr data-testid="separator" />

        <div>
          <div>
            <div data-testid="check-icon">Check Icon</div>
            <div>
              <span>90 dias de Garantia</span>
              <p>Garantia de 90 dias em todos os serviços realizados</p>
            </div>
          </div>
          <div>
            <div data-testid="check-icon">Check Icon</div>
            <div>
              <span>Agendamento imediato</span>
              <p>Escolha o melhor dia e a hora do seu atendimento.</p>
            </div>
          </div>
        </div>

        {service.provider && (
          <a href={service.provider.providerUrl}>
            <div>
              {service.provider.imageUrl ? (
                <img
                  src={service.provider.imageUrl}
                  alt={service.provider.name}
                  data-testid="next-image"
                />
              ) : (
                <div className="rounded-full bg-muted">
                  <span>{service.provider.name.charAt(0)}</span>
                </div>
              )}
            </div>
            <div>
              <h4>{service.provider.name}</h4>
              <p>{service.provider.description || ''}</p>
            </div>
          </a>
        )}

        <hr data-testid="separator" />

        <div>
          <p className="line-through">
            R$ {service.price.originalPrice.toFixed(2).replace('.', ',')}
          </p>
          <p>R$ {service.price.finalPrice.toFixed(2).replace('.', ',')}</p>
          {showButton && (
            <button type="submit" form={formId} data-testid="button" onClick={onSubmit}>
              <div data-testid="calendar-icon">Calendar Icon</div>
              Continuar agendamento
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

// Mock the ServiceSummaryCard component
jest.mock('@/src/app/_components/Pages/Home/ServiceSummaryCard', () => ({
  ServiceSummaryCard: MockServiceSummaryCard,
}));

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, width, height, className }: any) => (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      data-testid="next-image"
    />
  ),
}));

// Mock the icons from lucide-react
jest.mock('lucide-react', () => ({
  Check: () => <div data-testid="check-icon">Check Icon</div>,
  Calendar: () => <div data-testid="calendar-icon">Calendar Icon</div>,
}));

// Mock the components
jest.mock('@/src/app/_components', () => ({
  Button: ({ children, onClick, disabled, className }: any) => (
    <button onClick={onClick} disabled={disabled} className={className} data-testid="button">
      {children}
    </button>
  ),
  Card: ({ className, children }: any) => (
    <div className={className} data-testid="card">
      {children}
    </div>
  ),
  CardContent: ({ className, children }: any) => (
    <div className={className} data-testid="card-content">
      {children}
    </div>
  ),
  Separator: ({ className }: any) => <hr className={className} data-testid="separator" />,
}));

describe('ServiceSummaryCard (Server Component)', () => {
  const mockService = {
    id: 1,
    name: 'Test Service',
    description: 'This is a test service',
    price: {
      originalPrice: 120,
      finalPrice: 100,
    },
    provider: {
      name: 'Test Provider',
      providerUrl: 'https://provider.com',
      imageUrl: '/provider-image.jpg',
      description: 'Provider description',
    },
    categoryName: 'Test Category',
    categorySlug: 'test-category',
    subcategoryName: 'Test Subcategory',
    subcategorySlug: 'test-subcategory',
  };

  it('renders error state when service is null', () => {
    render(<MockServiceSummaryCard service={null} />);

    // Check for the title
    expect(screen.getByText('Resumo do serviço')).toBeInTheDocument();

    // Check for the error message
    expect(
      screen.getByText('Não foi possível carregar os detalhes do serviço.')
    ).toBeInTheDocument();

    // Check that the error message is in a red container
    const errorContainer = screen.getByText(
      'Não foi possível carregar os detalhes do serviço.'
    ).parentElement;
    expect(errorContainer).toHaveClass('bg-red-50', 'text-red-700');
  });

  it('renders service details correctly when service is provided', () => {
    render(<MockServiceSummaryCard service={mockService} />);

    // Check for the title and service name
    expect(screen.getByText('Resumo do serviço')).toBeInTheDocument();
    expect(screen.getByText('Test Service')).toBeInTheDocument();

    // Check for category and subcategory
    expect(screen.getByText('Test Category')).toBeInTheDocument();
    expect(screen.getByText('Test Subcategory')).toBeInTheDocument();

    // Check for the guarantee and scheduling sections
    expect(screen.getByText('90 dias de Garantia')).toBeInTheDocument();
    expect(
      screen.getByText('Garantia de 90 dias em todos os serviços realizados')
    ).toBeInTheDocument();
    expect(screen.getByText('Agendamento imediato')).toBeInTheDocument();
    expect(
      screen.getByText('Escolha o melhor dia e a hora do seu atendimento.')
    ).toBeInTheDocument();

    // Check for the provider section
    expect(screen.getByText('Test Provider')).toBeInTheDocument();
    expect(screen.getByText('Provider description')).toBeInTheDocument();

    // Check for the provider image
    const providerImage = screen.getByTestId('next-image');
    expect(providerImage).toBeInTheDocument();
    expect(providerImage).toHaveAttribute('src', '/provider-image.jpg');
    expect(providerImage).toHaveAttribute('alt', 'Test Provider');

    // Check for the price section
    expect(screen.getByText('R$ 120,00')).toBeInTheDocument();
    expect(screen.getByText('R$ 100,00')).toBeInTheDocument();
  });

  it('renders provider initial when provider image is not available', () => {
    const serviceWithoutProviderImage = {
      ...mockService,
      provider: {
        ...mockService.provider,
        imageUrl: undefined,
      },
    };

    render(<MockServiceSummaryCard service={serviceWithoutProviderImage} />);

    // Check that the provider initial is rendered instead of an image
    expect(screen.queryByTestId('next-image')).not.toBeInTheDocument();

    // Check for the provider initial (first letter of provider name)
    const initialElement = screen.getByText('T');
    expect(initialElement).toBeInTheDocument();
    expect(initialElement.parentElement).toHaveClass('rounded-full', 'bg-muted');
  });

  it('renders the correct number of check icons', () => {
    render(<MockServiceSummaryCard service={mockService} />);

    // There should be two check icons (for guarantee and scheduling)
    const checkIcons = screen.getAllByTestId('check-icon');
    expect(checkIcons).toHaveLength(2);
  });

  it('formats prices correctly', () => {
    render(<MockServiceSummaryCard service={mockService} />);

    // Check that prices are formatted with comma as decimal separator
    expect(screen.getByText('R$ 120,00')).toBeInTheDocument();
    expect(screen.getByText('R$ 100,00')).toBeInTheDocument();
  });

  it('does not render provider section when provider is not available', () => {
    const serviceWithoutProvider = {
      ...mockService,
      provider: undefined,
    };

    render(<MockServiceSummaryCard service={serviceWithoutProvider} />);

    // Check that the provider section is not rendered
    expect(screen.queryByText('Test Provider')).not.toBeInTheDocument();
    expect(screen.queryByText('Provider description')).not.toBeInTheDocument();
    expect(screen.queryByTestId('next-image')).not.toBeInTheDocument();
  });

  it('renders the button when showButton is true', () => {
    const mockOnSubmit = jest.fn();
    render(
      <MockServiceSummaryCard
        service={mockService}
        showButton={true}
        onSubmit={mockOnSubmit}
        formId="test-form"
      />
    );

    // Check if the button is rendered
    const button = screen.getByTestId('button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute('form', 'test-form');
    expect(button).toHaveAttribute('type', 'submit');
  });

  it('does not render the button when showButton is false', () => {
    const mockOnSubmit = jest.fn();
    render(
      <MockServiceSummaryCard service={mockService} showButton={false} onSubmit={mockOnSubmit} />
    );

    // Check that the button is not rendered
    expect(screen.queryByTestId('button')).not.toBeInTheDocument();
  });
});
