import { fireEvent, render, screen } from '@testing-library/react';

// Create a mock ServiceSummaryCard component
function MockServiceSummaryCard({
  slug,
  _className,
  showButton = false,
  onSubmit,
  formId,
}: {
  slug?: string;
  className?: string;
  showButton?: boolean;
  onSubmit?: () => void;
  formId?: string;
}) {
  // Get the data-testid for the service summary
  const testId = slug ? `service-summary-${slug}` : 'service-summary-test-service';

  return (
    <div data-testid={testId} role="article" aria-label="service summary">
      Service Summary for {slug || 'test-service'}
      {showButton && (
        <button data-testid="continue-button" type="submit" form={formId} onClick={onSubmit}>
          Continuar agendamento
        </button>
      )}
    </div>
  );
}

// Mock the ServiceSummaryCard component
jest.mock('@/src/app/_components/Pages/Home/ServiceSummaryCard.client', () => ({
  ServiceSummaryCard: MockServiceSummaryCard,
}));

// Import the ServiceSummaryCard component
import { ServiceSummaryCard } from '@/src/app/_components/Pages/Home/ServiceSummaryCard.client';

// Mock the Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, ...props }: any) => (
    <img src={src} alt={alt} {...props} data-testid="next-image" />
  ),
}));

// Mock the icons
jest.mock('lucide-react', () => ({
  Check: () => <div data-testid="check-icon" />,
  Calendar: () => <div data-testid="calendar-icon" />,
}));

// Mock Link component
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ href, children, ...props }: any) => (
    <a href={href} {...props} data-testid="next-link">
      {children}
    </a>
  ),
}));

// Mock components
jest.mock('@/src/app/_components', () => ({
  Button: ({ children, onClick, disabled, className }: any) => (
    <button onClick={onClick} disabled={disabled} className={className} data-testid="button">
      {children}
    </button>
  ),
  Card: ({ children, className }: any) => (
    <div className={className} data-testid="card">
      {children}
    </div>
  ),
  CardContent: ({ children, className }: any) => (
    <div className={className} data-testid="card-content">
      {children}
    </div>
  ),
  Separator: ({ className }: any) => <hr className={className} data-testid="separator" />,
}));

// Mock dos hooks de navegação
jest.mock('next/navigation', () => ({
  useParams: () => ({ slug: 'test-service' }),
}));

// Mock the useServiceBySlug hook
jest.mock('@/src/app/_hooks/useServiceBySlug', () => ({
  useServiceBySlug: jest.fn(),
}));

// Import the mocked hook
import { useServiceBySlug as mockUseService } from '@/src/app/_hooks/useServiceBySlug';

// Mock dos componentes UI
jest.mock('@/src/app/_components', () => ({
  Card: ({ children, className }: any) => (
    <div data-testid="card" className={className}>
      {children}
    </div>
  ),
  CardContent: ({ children, className }: any) => (
    <div data-testid="card-content" className={className}>
      {children}
    </div>
  ),
  Separator: ({ className }: any) => <hr data-testid="separator" className={className} />,
  Skeleton: ({ className }: any) => <div data-testid="skeleton" className={className} />,
}));

// Mock do utilitário cn
jest.mock('@/src/app/_utils/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' '),
}));

// Mock dos ícones Lucide
jest.mock('lucide-react', () => ({
  Check: () => <span data-testid="check-icon">✓</span>,
}));

// Mock do next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, width, height, className }: any) => (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      data-testid="next-image"
    />
  ),
}));

// Mock do next/link
jest.mock('next/link', () => {
  return ({ children, href, className, target, rel }: any) => (
    <a href={href} className={className} target={target} rel={rel} data-testid="next-link">
      {children}
    </a>
  );
});

describe('ServiceSummaryCard', () => {
  const mockService = {
    id: '1',
    name: 'test service',
    slug: 'test-service',
    description: 'This is a test service',
    price: {
      finalPrice: 100,
      originalPrice: 120,
      discountPrice: 20,
    },
    provider: {
      name: 'test provider',
      description: 'Provider description',
      imageUrl: '/provider-image.jpg',
      providerUrl: 'https://provider.com',
    },
    categoryName: 'Test Category',
    categorySlug: 'test-category',
    subcategoryName: 'Test Subcategory',
    subcategorySlug: 'test-subcategory',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the service summary component', () => {
    mockUseService.mockReturnValue({
      service: mockService,
      isLoading: false,
      error: null,
    });

    render(<ServiceSummaryCard />);

    // Check if the service summary is rendered
    expect(screen.getByTestId('service-summary-test-service')).toBeInTheDocument();
    expect(screen.getByText('Service Summary for test-service')).toBeInTheDocument();
  });

  it('should render with a custom slug', () => {
    mockUseService.mockReturnValue({
      service: mockService,
      isLoading: false,
      error: null,
    });

    const customSlug = 'custom-slug';
    render(<ServiceSummaryCard slug={customSlug} />);

    // Check if the service summary is rendered with the custom slug
    expect(screen.getByTestId(`service-summary-${customSlug}`)).toBeInTheDocument();
    expect(screen.getByText(`Service Summary for ${customSlug}`)).toBeInTheDocument();
  });

  it('should render with the default slug when no slug is provided', () => {
    mockUseService.mockReturnValue({
      service: mockService,
      isLoading: false,
      error: null,
    });

    render(<ServiceSummaryCard />);

    // Check if the service summary is rendered with the default slug
    expect(screen.getByTestId('service-summary-test-service')).toBeInTheDocument();
    expect(screen.getByText('Service Summary for test-service')).toBeInTheDocument();
  });

  it('should use slug from props if provided', () => {
    const slugFromProp = 'custom-slug';

    mockUseService.mockReturnValue({
      service: mockService,
      isLoading: false,
      error: null,
    });

    render(<ServiceSummaryCard slug={slugFromProp} />);

    // Check if the service summary is rendered with the custom slug
    expect(screen.getByTestId(`service-summary-${slugFromProp}`)).toBeInTheDocument();
  });

  it('should use slug from params if not provided in props', () => {
    mockUseService.mockReturnValue({
      service: mockService,
      isLoading: false,
      error: null,
    });

    render(<ServiceSummaryCard />);

    // Check if the service summary is rendered with the default slug
    expect(screen.getByTestId('service-summary-test-service')).toBeInTheDocument();
  });

  it('should apply custom className when provided', () => {
    mockUseService.mockReturnValue({
      service: mockService,
      isLoading: false,
      error: null,
    });

    render(<ServiceSummaryCard className="custom-class" />);

    // Check if the service summary is rendered
    expect(screen.getByTestId('service-summary-test-service')).toBeInTheDocument();
  });

  it('should render the button when showButton is true', () => {
    mockUseService.mockReturnValue({
      service: mockService,
      isLoading: false,
      error: null,
    });

    const mockOnSubmit = jest.fn();
    render(<ServiceSummaryCard showButton={true} onSubmit={mockOnSubmit} formId="test-form" />);

    // Check if the button is rendered
    const button = screen.getByTestId('continue-button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Continuar agendamento');
    expect(button).toHaveAttribute('form', 'test-form');
    expect(button).toHaveAttribute('type', 'submit');

    // Check if clicking the button calls the onSubmit function
    fireEvent.click(button);
    expect(mockOnSubmit).toHaveBeenCalled();
  });

  it('should not render the button when showButton is false', () => {
    mockUseService.mockReturnValue({
      service: mockService,
      isLoading: false,
      error: null,
    });

    const mockOnSubmit = jest.fn();
    render(<ServiceSummaryCard showButton={false} onSubmit={mockOnSubmit} />);

    // Check that the button is not rendered
    expect(screen.queryByTestId('continue-button')).not.toBeInTheDocument();
  });
});
