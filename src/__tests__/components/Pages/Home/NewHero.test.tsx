import { NewHero } from '@/src/app/_components/Pages/Home/NewHero';
import { render, screen } from '@testing-library/react';

describe('NewHero Component', () => {
  it('renders the hero section with correct content', () => {
    render(<NewHero />);

    // Check for the main heading
    const heading = screen.getByText(/Serviço garantido,/i);
    expect(heading).toBeInTheDocument();
    expect(heading).toHaveClass('bg-gradient-to-r', 'text-transparent');

    // Check for the rest of the heading
    const restOfHeading = screen.getByText(/seguro e sem complicação./i);
    expect(restOfHeading).toBeInTheDocument();

    // Check for the description text
    const description = screen.getByText(/O GetNinjas ajuda você a contratar o serviço/i);
    expect(description).toBeInTheDocument();
    expect(description).toHaveClass('mt-6');
  });

  it('has the correct layout and styling', () => {
    const { container } = render(<NewHero />);

    // Check for the main container
    const mainDiv = container.firstChild;
    expect(mainDiv).toHaveClass('relative', 'w-full', 'text-black');

    // Check for the content container
    const contentDiv = mainDiv?.firstChild;
    expect(contentDiv).toHaveClass('w-full');
    expect(contentDiv).toHaveClass('md:w-9/12');

    // Check for responsive text classes
    const heading = screen.getByText(/Serviço garantido,/i).parentElement;
    expect(heading).toHaveClass('text-5xl', 'font-extrabold');
    expect(heading).toHaveClass('md:text-5xl', 'lg:text-6xl');

    const description = screen.getByText(/O GetNinjas ajuda você a contratar o serviço/i);
    expect(description).toHaveClass('text-lg', 'font-medium');
    expect(description).toHaveClass('md:text-xl', 'lg:text-2xl');
  });
});
