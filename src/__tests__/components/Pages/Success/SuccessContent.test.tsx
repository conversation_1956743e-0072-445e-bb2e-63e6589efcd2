import { mockServices } from '@/src/__tests__/fixtures/servicesMock';
import { SuccessContent } from '@/src/app/_components/Pages/Success/SuccessContent';
import { ServiceProvider } from '@/src/app/_context/ServiceContext';
import { render, screen } from '@testing-library/react';

// Mock the hooks
jest.mock('@/src/app/_hooks/useOrderData', () => ({
  useOrderData: () => ({
    orderData: {
      service: {
        id: '123',
        name: 'Conser<PERSON> de fogão',
        slug: 'conserto-de-fogao',
        description: 'Serviço de conserto de fogão',
        details: 'Inclui diagnóstico e reparo',
        serviceLimits: 'Não inclui peças',
        price: {
          finalPrice: 150.0,
        },
        provider: {
          name: 'Europ Assistance',
        },
      },
      appointment: {
        date: '2024-01-15',
        period: 'morning',
      },
      address: {
        street: 'Rua das Flores',
        numberAd: '123',
        complement: 'Apto 45',
        neighborhood: 'Centro',
        cityName: 'São Paulo',
        uf: 'SP',
        zipCode: '01234-567',
      },
      customer: {
        fullName: '<PERSON>',
        phone: '(11) 99999-9999',
        email: '<EMAIL>',
        document: '123.456.789-00',
      },
      payment: {
        method: 'Credit Card',
      },
    },
    isLoading: false,
    error: null,
  }),
}));

jest.mock('@/src/app/_hooks/analytics/useTrackPurchase', () => ({
  useTrackPurchaseEvent: () => ({
    trackPurchaseEvent: jest.fn(),
  }),
}));

jest.mock('next/navigation', () => ({
  useSearchParams: () => ({
    get: () => 'test-order-123',
  }),
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(<ServiceProvider services={mockServices}>{component}</ServiceProvider>);
};

describe('SuccessContent', () => {
  it('should render the success page with two-column layout', () => {
    renderWithProvider(<SuccessContent />);

    // Check header
    expect(screen.getByText(/Pronto,/)).toBeInTheDocument();
    expect(screen.getByText(/serviço agendado!/)).toBeInTheDocument();

    // Check service summary
    expect(screen.getAllByText('Conserto de fogão').length).toBeGreaterThan(0);

    // Check left column - Next Steps
    expect(screen.getByText('Próximos passos')).toBeInTheDocument();
    expect(screen.getByText(/email de confirmação/)).toBeInTheDocument();
    expect(screen.getByText(/entrará/)).toBeInTheDocument();
    expect(screen.getByText(/Prepare-se para receber o serviço/)).toBeInTheDocument();

    // Check right column - Appointment Details
    expect(screen.getByText('Detalhes do agendamento')).toBeInTheDocument();
    expect(screen.getByText('Data e Horário')).toBeInTheDocument();
    expect(screen.getByText('Endereço')).toBeInTheDocument();
    expect(screen.getByText('Dados pessoais do contratante')).toBeInTheDocument();
    expect(screen.getByText('Pagamento')).toBeInTheDocument();
    expect(screen.getByText('O que está incluso')).toBeInTheDocument();
    expect(screen.getByText('Restrições')).toBeInTheDocument();
  });

  it('should render alert boxes in the next steps section', () => {
    renderWithProvider(<SuccessContent />);

    // Check reminder alert - text is split across elements
    expect(screen.getByText(/Lembre-se:/)).toBeInTheDocument();
    // Check for the text about parts acquisition (appears in multiple places)
    const partsText = screen.getAllByText(/eventual aquisição das peças/);
    expect(partsText.length).toBeGreaterThan(0);

    // Check reschedule alert
    expect(screen.getByText(/Precisou reagendar ou cancelar/)).toBeInTheDocument();
    expect(screen.getAllByText(/0800 202 4011/).length).toBeGreaterThan(0);
  });

  it('should not render service navigation section (handled by parent)', () => {
    renderWithProvider(<SuccessContent />);

    // SuccessContent should not contain service navigation - that's handled by the parent page
    expect(screen.queryByText('O que você precisa?')).not.toBeInTheDocument();
  });

  it('should use the correct category icon for the service', () => {
    renderWithProvider(<SuccessContent />);

    // The test service 'conserto-de-fogao' should match 'assistencia-tecnica' category
    // which should use the 'Wrench' icon according to the categories mapping
    // We can verify the Icon components are rendered by checking for SVG elements
    const svgElements = document.querySelectorAll('svg');
    expect(svgElements.length).toBeGreaterThan(0);

    // Check for specific icon classes that indicate the presence of icons
    const iconElements = document.querySelectorAll('.lucide');
    expect(iconElements.length).toBeGreaterThan(0);
  });

  it('should fallback to Wrench icon when category is not found', () => {
    // Test with a service that doesn't match any category
    const orderDataWithUnknownCategory = {
      service: {
        id: '999',
        name: 'Unknown Service',
        slug: 'unknown-service',
        description: 'Unknown service description',
        details: 'Unknown details',
        serviceLimits: 'Unknown limits',
        price: { finalPrice: 100.0 },
        provider: { name: 'Unknown Provider' },
      },
      appointment: { date: '2024-01-15', period: 'morning' },
      address: {
        street: 'Test Street',
        numberAd: '123',
        complement: 'Test',
        neighborhood: 'Test',
        cityName: 'Test',
        uf: 'SP',
        zipCode: '12345-678',
      },
      customer: {
        fullName: 'Test User',
        phone: '(11) 99999-9999',
        email: '<EMAIL>',
        document: '123.456.789-00',
      },
      payment: { method: 'Credit Card' },
    };

    // Mock the hook to return unknown service data
    jest.doMock('@/src/app/_hooks/useOrderData', () => ({
      useOrderData: () => ({
        orderData: orderDataWithUnknownCategory,
        isLoading: false,
        error: null,
      }),
    }));

    renderWithProvider(<SuccessContent />);

    // Should still render icons (fallback to Wrench)
    const svgElements = document.querySelectorAll('svg');
    expect(svgElements.length).toBeGreaterThan(0);

    // Check for specific icon classes that indicate the presence of icons
    const iconElements = document.querySelectorAll('.lucide');
    expect(iconElements.length).toBeGreaterThan(0);
  });
});
