import { render } from '@testing-library/react';
import { JsonLd } from '@/src/app/_components/Common/JsonLd/JsonLd';

describe('JsonLd', () => {
  it('renders JSON-LD script with provided data', () => {
    const testData = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: 'GetNinjas',
      url: 'https://www.getninjas.com.br',
    };

    const { container } = render(<JsonLd data={testData} />);
    const script = container.querySelector('script[type="application/ld+json"]');

    expect(script).toBeInTheDocument();
    expect(script?.textContent).toBe(JSON.stringify(testData));
  });

  it('filters out router state objects from data', () => {
    const testData = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: 'GetNin<PERSON>',
      url: 'https://www.getninjas.com.br',
      children: { some: 'data' },
      params: { some: 'data' },
      __PAGE__: { some: 'data' },
    };

    const { container } = render(<JsonLd data={testData} />);
    const script = container.querySelector('script[type="application/ld+json"]');

    expect(script).toBeInTheDocument();
    const parsedData = JSON.parse(script?.textContent || '{}');
    expect(parsedData).not.toHaveProperty('children');
    expect(parsedData).not.toHaveProperty('params');
    expect(parsedData).not.toHaveProperty('__PAGE__');
  });

  it('handles empty data object', () => {
    const { container } = render(<JsonLd data={{}} />);
    const script = container.querySelector('script[type="application/ld+json"]');

    expect(script).toBeInTheDocument();
    expect(script?.textContent).toBe('{}');
  });

  it('handles nested objects with router state', () => {
    const testData = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: 'GetNinjas',
      nested: {
        children: { some: 'data' },
        params: { some: 'data' },
        __PAGE__: { some: 'data' },
      },
    };

    const { container } = render(<JsonLd data={testData} />);
    const script = container.querySelector('script[type="application/ld+json"]');

    expect(script).toBeInTheDocument();
    const parsedData = JSON.parse(script?.textContent || '{}');
    expect(parsedData.nested).not.toHaveProperty('children');
    expect(parsedData.nested).not.toHaveProperty('params');
    expect(parsedData.nested).not.toHaveProperty('__PAGE__');
  });
});
