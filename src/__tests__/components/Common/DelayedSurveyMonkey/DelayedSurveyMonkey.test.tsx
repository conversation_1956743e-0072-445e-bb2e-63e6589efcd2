/**
 * @jest-environment jsdom
 */

// Mock React hooks
jest.mock('react', () => {
  const originalReact = jest.requireActual('react');
  return {
    ...originalReact,
    useRef: jest.fn(() => ({ current: false })),
    useEffect: jest.fn((callback) => callback()),
  };
});

import { DelayedSurveyMonkeyScript } from '@/src/app/_components/Common/DelayedSurveyMonkey/DelayedSurveyMonkey';
import React from 'react';

describe('DelayedSurveyMonkeyScript', () => {
  let originalAddEventListener;
  let originalRemoveEventListener;
  let originalCreateElement;
  let originalAppendChild;

  let mockAddEventListener;
  let mockRemoveEventListener;
  let mockCreateElement;
  let mockAppendChild;
  let mockScript;

  beforeEach(() => {
    // Save original methods
    originalAddEventListener = window.addEventListener;
    originalRemoveEventListener = window.removeEventListener;
    originalCreateElement = document.createElement;
    originalAppendChild = document.body.appendChild;

    // Create mocks
    mockScript = { src: '', async: false };
    mockAddEventListener = jest.fn();
    mockRemoveEventListener = jest.fn();
    mockCreateElement = jest.fn(() => mockScript);
    mockAppendChild = jest.fn();

    // Apply mocks
    window.addEventListener = mockAddEventListener;
    window.removeEventListener = mockRemoveEventListener;
    document.createElement = mockCreateElement;
    document.body.appendChild = mockAppendChild;

    // Mock window properties
    Object.defineProperty(window, 'scrollY', { value: 0, configurable: true, writable: true });
    Object.defineProperty(window, 'innerHeight', { value: 500, configurable: true });
    Object.defineProperty(document.body, 'offsetHeight', { value: 1000, configurable: true });
  });

  afterEach(() => {
    // Restore original methods
    window.addEventListener = originalAddEventListener;
    window.removeEventListener = originalRemoveEventListener;
    document.createElement = originalCreateElement;
    document.body.appendChild = originalAppendChild;

    // Clear mocks
    jest.clearAllMocks();
  });

  it('adds scroll event listener on mount', () => {
    // Get the useEffect mock
    const useEffectMock = React.useEffect as jest.Mock;

    // Call the component
    const component = DelayedSurveyMonkeyScript();

    // Check if useEffect was called
    expect(useEffectMock).toHaveBeenCalled();

    // Get the effect callback
    const effectCallback = useEffectMock.mock.calls[0][0];

    // Call the effect callback
    effectCallback();

    // Check if addEventListener was called with 'scroll'
    expect(mockAddEventListener).toHaveBeenCalledWith('scroll', expect.any(Function));

    // The component should return null
    expect(component).toBeNull();
  });

  it('loads script when scrolled near bottom', () => {
    // Get the useEffect mock
    const useEffectMock = React.useEffect as jest.Mock;

    // Call the component
    DelayedSurveyMonkeyScript();

    // Get the effect callback
    const effectCallback = useEffectMock.mock.calls[0][0];

    // Call the effect callback to get the scroll handler
    effectCallback();

    // Get the scroll handler function
    const scrollHandler = mockAddEventListener.mock.calls[0][1];

    // Simulate scrolling to bottom
    window.scrollY = 700;

    // Call the scroll handler
    scrollHandler();

    // Check if script was created with correct properties
    expect(mockCreateElement).toHaveBeenCalledWith('script');
    expect(mockAppendChild).toHaveBeenCalled();
    expect(mockScript.src).toBe(
      'https://widget.surveymonkey.com/collect/website/js/tRaiETqnLgj758hTBazgd94d4_2F5337WmKyXyyFb0ZZ1yx8d9n5Oj95R2dUpN6OKp.js'
    );
    expect(mockScript.async).toBe(true);
  });

  it('does not load script when not scrolled near bottom', () => {
    // Get the useEffect mock
    const useEffectMock = React.useEffect as jest.Mock;

    // Call the component
    DelayedSurveyMonkeyScript();

    // Get the effect callback
    const effectCallback = useEffectMock.mock.calls[0][0];

    // Call the effect callback to get the scroll handler
    effectCallback();

    // Get the scroll handler function
    const scrollHandler = mockAddEventListener.mock.calls[0][1];

    // Simulate scrolling to middle (not near bottom)
    window.scrollY = 200;

    // Call the scroll handler
    scrollHandler();

    // Script should not be created
    expect(mockCreateElement).not.toHaveBeenCalled();
    expect(mockAppendChild).not.toHaveBeenCalled();
  });

  it('removes event listener on cleanup', () => {
    // Get the useEffect mock
    const useEffectMock = React.useEffect as jest.Mock;

    // Call the component
    DelayedSurveyMonkeyScript();

    // Get the effect callback
    const effectCallback = useEffectMock.mock.calls[0][0];

    // Call the effect callback to get the cleanup function
    const cleanup = effectCallback();

    // Call the cleanup function
    cleanup();

    // Check if removeEventListener was called
    expect(mockRemoveEventListener).toHaveBeenCalledWith('scroll', expect.any(Function));
  });
});
