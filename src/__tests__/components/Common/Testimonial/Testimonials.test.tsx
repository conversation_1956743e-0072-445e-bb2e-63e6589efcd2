import { Testimonials } from '@/src/app/_components/Common/Testimonial/Testimonials';
import { render, screen } from '@testing-library/react';

// Mock the Star icon from lucide-react
jest.mock('lucide-react', () => ({
  Star: ({ className }: { className?: string }) => (
    <svg className={className} role="img" aria-label="star" />
  ),
}));

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, width, height, className }: any) => (
    <img src={src} alt={alt} width={width} height={height} className={className} />
  ),
}));

describe('Testimonials Component', () => {
  const mockProvider = {
    name: 'europ assistance',
    imageUrl: 'https://example.com/logo.png',
  };

  test('renders provider logo and name correctly', () => {
    render(<Testimonials provider={mockProvider} />);

    // Check provider logo
    const logo = screen.getByAltText(`${mockProvider.name} logo`);
    expect(logo).toBeInTheDocument();
    expect(logo).toHaveAttribute('src', mockProvider.imageUrl);
    expect(logo).toHaveAttribute('width', '80');
    expect(logo).toHaveAttribute('height', '80');
    expect(logo).toHaveClass('object-cover');

    // Check provider name (should be properly capitalized)
    expect(screen.getByText('Europ Assistance')).toBeInTheDocument();
  });

  test('renders section header with decorative elements', () => {
    render(<Testimonials provider={mockProvider} />);

    // Check section title
    expect(screen.getByText('O que nossos clientes dizem sobre')).toBeInTheDocument();

    // Check decorative lines
    const decorativeLines = screen.getAllByTestId('decorative-line');
    expect(decorativeLines).toHaveLength(2);
    decorativeLines.forEach((line) => {
      expect(line).toHaveClass('h-px', 'w-12', 'bg-gray-200');
    });
  });

  test('renders all testimonial cards', () => {
    render(<Testimonials provider={mockProvider} />);

    // Check if all testimonials are rendered
    expect(screen.getByText('Maria Silva')).toBeInTheDocument();
    expect(screen.getByText('João Santos')).toBeInTheDocument();
    expect(screen.getByText('Ana Oliveira')).toBeInTheDocument();

    // Check testimonial comments
    expect(
      screen.getByText('Excelente serviço! Profissionais muito competentes e atenciosos.')
    ).toBeInTheDocument();
    expect(
      screen.getByText('Muito satisfeito com o atendimento e a qualidade do serviço.')
    ).toBeInTheDocument();
    expect(
      screen.getByText('Bom serviço, recomendo! O tempo para chegar e consertar foi bem pontual.')
    ).toBeInTheDocument();
  });

  test('renders correct number of stars for each testimonial', () => {
    render(<Testimonials provider={mockProvider} />);

    const stars = screen.getAllByRole('img', { name: 'star' });
    expect(stars).toHaveLength(15); // 5 stars × 3 testimonials
  });

  test('renders testimonial cards with correct styling', () => {
    render(<Testimonials provider={mockProvider} />);

    // Check card styling
    const card = screen.getByText('Maria Silva').closest('div')?.parentElement;
    if (!card) {
      throw new Error('Card element not found');
    }
    expect(card).toHaveClass('rounded-lg', 'bg-white', 'p-6');

    // Check star styling
    const stars = screen.getAllByRole('img', { name: 'star' });
    stars.forEach((star) => {
      expect(star).toHaveClass('h-5', 'w-5', 'fill-current', 'text-yellow-400');
    });

    // Check text styling
    const comment = screen.getByText(
      'Excelente serviço! Profissionais muito competentes e atenciosos.'
    );
    expect(comment).toHaveClass('text-gray-600');

    const name = screen.getByText('Maria Silva');
    expect(name).toHaveClass('text-lg', 'font-bold', 'text-muted-foreground');

    const location = screen.getByText('Rio de Janeiro');
    expect(location).toHaveClass('text-lg', 'text-gray-400');
  });
});
