import { TestimonialsSkeleton } from '@/src/app/_components/Common/Testimonial/TestimonialsSkeleton';
import { render } from '@testing-library/react';

describe('TestimonialsSkeleton', () => {
  it('renders the skeleton component correctly', () => {
    render(<TestimonialsSkeleton />);

    // Check for the presence of skeleton elements
    const skeletons = document.querySelectorAll('.animate-pulse');
    expect(skeletons.length).toBeGreaterThan(0);

    // Check for the three testimonial card skeletons
    const containers = document.querySelectorAll('[class*="flex-1"]');
    expect(containers.length).toBe(3);
  });
});
