import { TestimonialCard } from '@/src/app/_components/Common/Testimonial/TestimonialCard';
import { render, screen } from '@testing-library/react';

// Mock the Star icon from lucide-react
jest.mock('lucide-react', () => ({
  Star: ({ className }: { className?: string }) => (
    <svg className={className} role="img" aria-label="star" />
  ),
}));

describe('TestimonialCard Component', () => {
  const mockTestimonial = {
    id: '1',
    author: '<PERSON>',
    location: 'São Paulo',
    content: 'Great service! Very professional and efficient.',
    rating: 5,
    order: 1,
  };

  test('renders testimonial content correctly', () => {
    render(<TestimonialCard testimonial={mockTestimonial} />);

    expect(screen.getByText(mockTestimonial.content)).toBeInTheDocument();
    expect(screen.getByText(mockTestimonial.author)).toBeInTheDocument();
    expect(screen.getByText(mockTestimonial.location)).toBeInTheDocument();
  });

  test('renders correct number of stars', () => {
    render(<TestimonialCard testimonial={mockTestimonial} />);

    const stars = screen.getAllByRole('img', { name: 'star' });
    expect(stars).toHaveLength(5);
  });

  test('has correct styling classes', () => {
    render(<TestimonialCard testimonial={mockTestimonial} />);

    // Check container styling
    const container = screen.getByText(mockTestimonial.content).parentElement;
    if (!container) {
      throw new Error('Container element not found');
    }
    expect(container).toHaveClass('flex', 'h-full', 'flex-col');

    // Check content styling
    const content = screen.getByText(mockTestimonial.content);
    expect(content).toHaveClass('flex-grow', 'text-gray-600');

    // Check author styling
    const author = screen.getByText(mockTestimonial.author);
    expect(author).toHaveClass('text-sm', 'font-semibold', 'text-gray-900');

    // Check location styling
    const location = screen.getByText(mockTestimonial.location);
    expect(location).toHaveClass('text-sm', 'text-gray-500');
  });

  test('renders stars with correct styling', () => {
    render(<TestimonialCard testimonial={mockTestimonial} />);

    const stars = screen.getAllByRole('img', { name: 'star' });
    stars.forEach((star) => {
      expect(star).toHaveClass('mr-1', 'h-5', 'w-5', 'fill-current', 'text-gray-400');
    });
  });

  test('handles long content gracefully', () => {
    const longContent = 'A'.repeat(500); // Very long content
    const testimonialWithLongContent = {
      ...mockTestimonial,
      content: longContent,
    };

    render(<TestimonialCard testimonial={testimonialWithLongContent} />);

    const content = screen.getByText(longContent);
    expect(content).toBeInTheDocument();
    expect(content).toHaveClass('flex-grow');
  });

  test('maintains consistent layout structure', () => {
    render(<TestimonialCard testimonial={mockTestimonial} />);

    const container = screen.getByText(mockTestimonial.content).parentElement;
    if (!container) {
      throw new Error('Container element not found');
    }

    // Check if all elements are present in the correct order
    expect(container.children).toHaveLength(3); // stars container, content, author info

    // Check stars container
    const starsContainer = container.children[0];
    expect(starsContainer).toHaveClass('mb-4');

    // Check content
    const content = container.children[1];
    expect(content.tagName).toBe('P');

    // Check author info container
    const authorInfo = container.children[2];
    expect(authorInfo).toHaveClass('mt-4');
    expect(authorInfo.children).toHaveLength(2); // author and location
  });
});
