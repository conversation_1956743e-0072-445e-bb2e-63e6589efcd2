import { CookieBanner } from '@/src/app/_components/Common/CookieBanner/CookieBanner';
import {
  COOKIE_CONSENT_EXPIRY,
  COOKIE_CONSENT_KEY,
  SECURE_COOKIE_OPTIONS,
} from '@/src/app/_constants/cookies';
import { trackClickEvent } from '@/src/app/_functions/analytics/common/trackClickEvent';
import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import Cookies from 'js-cookie';

// Mock dependencies
jest.mock('js-cookie', () => ({
  get: jest.fn(),
  set: jest.fn(),
}));

jest.mock('@/src/app/_functions/analytics/common/trackClickEvent', () => ({
  trackClickEvent: jest.fn(),
}));

// Mock constants
jest.mock('@/src/app/_constants/cookies', () => ({
  COOKIE_CONSENT_KEY: 'cookie-consent',
  COOKIE_CONSENT_EXPIRY: 365,
  SECURE_COOKIE_OPTIONS: {
    secure: true,
    sameSite: 'strict',
  },
}));

describe('CookieBanner', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the cookie banner when no consent cookie exists', () => {
    // Mock Cookies.get to return null (no consent given yet)
    (Cookies.get as jest.Mock).mockReturnValue(null);

    render(<CookieBanner />);

    // Check if the banner text is displayed
    expect(
      screen.getByText(
        /Este site usa cookies para garantir que você obtenha a melhor experiência em nosso site/i
      )
    ).toBeInTheDocument();

    // Check if the "Permitir" button is displayed with proper accessibility
    expect(screen.getByRole('button', { name: /Permitir o uso de cookies/i })).toBeInTheDocument();

    // Check if the "Saiba mais" link is displayed with proper accessibility
    expect(screen.getByText(/Saiba mais/i)).toBeInTheDocument();
    expect(
      screen.getByLabelText(/Saiba mais sobre nossa política de cookies/i)
    ).toBeInTheDocument();

    // Check if the banner has the correct background color and positioning
    const banner = screen.getByRole('alert');
    expect(banner).toHaveClass('bg-black');
    expect(banner).toHaveClass('fixed');
    expect(banner).toHaveClass('bottom-0');
    expect(banner).toHaveClass('z-[100]');
  });

  it('does not render the cookie banner when consent cookie exists', () => {
    // Mock Cookies.get to return a value (consent already given)
    (Cookies.get as jest.Mock).mockReturnValue('true');

    render(<CookieBanner />);

    // Check that the banner is not displayed
    expect(
      screen.queryByText(
        /Este site usa cookies para garantir que você obtenha a melhor experiência em nosso site/i
      )
    ).not.toBeInTheDocument();
  });

  it('sets a cookie and hides the banner when "Permitir" is clicked', () => {
    // Mock Cookies.get to return null (no consent given yet)
    (Cookies.get as jest.Mock).mockReturnValue(null);

    render(<CookieBanner />);

    // Click the "Permitir" button
    fireEvent.click(screen.getByRole('button', { name: /Permitir o uso de cookies/i }));

    // Check that the cookie was set with the correct parameters
    expect(Cookies.set).toHaveBeenCalledWith(COOKIE_CONSENT_KEY, 'true', {
      expires: COOKIE_CONSENT_EXPIRY,
      ...SECURE_COOKIE_OPTIONS,
    });

    // Check that the analytics event was tracked
    expect(trackClickEvent).toHaveBeenCalledWith('cookie_consent_accepted');

    // Check that the banner is no longer displayed
    expect(
      screen.queryByText(
        /Este site usa cookies para garantir que você obtenha a melhor experiência em nosso site/i
      )
    ).not.toBeInTheDocument();

    // Check that the banner element is removed
    expect(screen.queryByRole('alert')).not.toBeInTheDocument();
  });

  it('tracks analytics event when "Saiba mais" is clicked', () => {
    // Mock Cookies.get to return null (no consent given yet)
    (Cookies.get as jest.Mock).mockReturnValue(null);

    render(<CookieBanner />);

    // Click the "Saiba mais" link
    fireEvent.click(screen.getByText(/Saiba mais/i));

    // Check that the analytics event was tracked
    expect(trackClickEvent).toHaveBeenCalledWith('cookie_consent_learn_more');
  });

  it('handles errors when checking for cookie consent', () => {
    // Mock console.error to prevent actual console output during test
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Mock Cookies.get to throw an error
    (Cookies.get as jest.Mock).mockImplementation(() => {
      throw new Error('Cookie access error');
    });

    render(<CookieBanner />);

    // Check that the error was handled
    expect(console.error).toHaveBeenCalledWith('Error checking cookie consent:', expect.any(Error));

    // Restore original console.error
    console.error = originalConsoleError;
  });

  it('handles errors when setting cookie consent', () => {
    // Mock console.error to prevent actual console output during test
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Mock Cookies.get to return null (no consent given yet)
    (Cookies.get as jest.Mock).mockReturnValue(null);

    // Mock Cookies.set to throw an error
    (Cookies.set as jest.Mock).mockImplementation(() => {
      throw new Error('Cookie set error');
    });

    render(<CookieBanner />);

    // Click the "Permitir" button
    fireEvent.click(screen.getByRole('button', { name: /Permitir o uso de cookies/i }));

    // Check that the error was handled
    expect(console.error).toHaveBeenCalledWith('Error setting cookie consent:', expect.any(Error));

    // Restore original console.error
    console.error = originalConsoleError;
  });
});
