import { Icon } from '@/src/app/_components/Common/Icon/Icon';
import { render, screen } from '@testing-library/react';

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  Search: ({ className, ...props }: { className?: string } & Record<string, any>) => (
    <svg className={className} role="img" aria-label="search" {...props} />
  ),
  Menu: ({ className, ...props }: { className?: string } & Record<string, any>) => (
    <svg className={className} role="img" aria-label="menu" {...props} />
  ),
}));

describe('Icon', () => {
  it('renders the icon with default classes', () => {
    render(<Icon name="Search" />);
    const icon = screen.getByRole('img', { name: 'search' });
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveClass('h-4', 'w-4');
  });

  it('renders the icon with custom classes', () => {
    render(<Icon name="Menu" className="custom-class" />);
    const icon = screen.getByRole('img', { name: 'menu' });
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveClass('h-4', 'w-4', 'custom-class');
  });

  it('returns null when icon name is not found', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const { container } = render(<Icon name="Activity" />);
    expect(container).toBeEmptyDOMElement();
    // The implementation doesn't log errors for missing icons, so we shouldn't expect this
    consoleSpy.mockRestore();
  });

  it('passes additional props to the icon component', () => {
    render(<Icon name="Search" data-testid="test-icon" />);
    const icon = screen.getByTestId('test-icon');
    expect(icon).toBeInTheDocument();
  });
});
