import { ErrorDisplay } from '@/src/app/_components/Common/ErrorDisplay/ErrorDisplay';
import { fireEvent, render, screen } from '@testing-library/react';

// Mock react-markdown
jest.mock('react-markdown', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Mock remark-gfm
jest.mock('remark-gfm', () => ({
  __esModule: true,
  default: () => ({}),
}));

// Mock the Button component
jest.mock('@/src/app/_components/Ui/button', () => ({
  Button: ({ children, onClick }: any) => <button onClick={onClick}>{children}</button>,
}));

// Mock window.location.reload
const mockReload = jest.fn();
Object.defineProperty(window, 'location', {
  value: { reload: mockReload },
  writable: true,
});

describe('ErrorDisplay Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders with default message when no props are provided', () => {
    render(<ErrorDisplay />);

    expect(
      screen.getByText('Ocorreu um erro. Por favor, tente novamente mais tarde.')
    ).toBeInTheDocument();
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  test('renders with custom message', () => {
    const customMessage = 'Custom error message';
    render(<ErrorDisplay message={customMessage} />);

    expect(screen.getByText(customMessage)).toBeInTheDocument();
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  test('renders with error object', () => {
    const error = new Error('Test error message');
    render(<ErrorDisplay error={error} />);

    expect(screen.getByText('Aconteceu alguma coisa errada:')).toBeInTheDocument();
    expect(screen.getByText('Test error message')).toBeInTheDocument();
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  test('renders in fullPage mode with retry button', () => {
    render(<ErrorDisplay fullPage />);

    expect(
      screen.getByText('Ocorreu um erro. Por favor, tente novamente mais tarde.')
    ).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Tentar novamente' })).toBeInTheDocument();
  });

  test('calls onRetry callback when retry button is clicked in fullPage mode', () => {
    const mockOnRetry = jest.fn();
    render(<ErrorDisplay fullPage onRetry={mockOnRetry} />);

    const retryButton = screen.getByRole('button', { name: 'Tentar novamente' });
    fireEvent.click(retryButton);

    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });

  test('calls window.location.reload when retry button is clicked in fullPage mode without onRetry prop', () => {
    render(<ErrorDisplay fullPage />);

    const retryButton = screen.getByRole('button', { name: 'Tentar novamente' });
    fireEvent.click(retryButton);

    expect(mockReload).toHaveBeenCalledTimes(1);
  });

  test('has correct accessibility attributes', () => {
    render(<ErrorDisplay />);

    const alertElement = screen.getByRole('alert');
    expect(alertElement).toBeInTheDocument();
    expect(alertElement).toHaveClass('rounded-md', 'border-red-400', 'bg-red-100');
  });

  test('renders with fullPage styles when fullPage prop is true', () => {
    render(<ErrorDisplay fullPage />);

    const container = screen.getByRole('alert');
    expect(container).toHaveClass(
      'flex',
      'min-h-[50vh]',
      'flex-col',
      'items-center',
      'justify-center'
    );
  });

  test('renders with inline styles when fullPage prop is false', () => {
    render(<ErrorDisplay />);

    const container = screen.getByRole('alert');
    expect(container).toHaveClass('rounded-md', 'border-red-400', 'bg-red-100');
  });
});
