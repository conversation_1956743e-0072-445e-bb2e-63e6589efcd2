import { ErrorSection } from '@/src/app/_components/Common/ErrorSection/ErrorSection';
import { fireEvent, render, screen } from '@testing-library/react';

// Mock the Button component
jest.mock('@/src/app/_components/Ui/button', () => ({
  Button: ({ children, onClick }: any) => <button onClick={onClick}>{children}</button>,
}));

// Mock react-markdown
jest.mock('react-markdown', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Mock remark-gfm
jest.mock('remark-gfm', () => ({
  __esModule: true,
  default: () => ({}),
}));

// Mock window.location.reload
const mockReload = jest.fn();
Object.defineProperty(window, 'location', {
  value: { reload: mockReload },
  writable: true,
});

describe('ErrorSection Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders with correct error message', () => {
    render(<ErrorSection />);

    expect(screen.getByText('Erro ao carregar serviços')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Ocorreu um erro ao carregar a lista de serviços. Por favor, tente novamente mais tarde.'
      )
    ).toBeInTheDocument();
  });

  test('renders retry button', () => {
    render(<ErrorSection />);

    const retryButton = screen.getByRole('button', { name: 'Tentar novamente' });
    expect(retryButton).toBeInTheDocument();
  });

  test('calls window.location.reload when retry button is clicked', () => {
    render(<ErrorSection />);

    const retryButton = screen.getByRole('button', { name: 'Tentar novamente' });
    fireEvent.click(retryButton);

    expect(mockReload).toHaveBeenCalledTimes(1);
  });

  test('has correct styling classes', () => {
    render(<ErrorSection />);

    // Check container styling
    const container = screen.getByRole('button').parentElement;
    expect(container).toHaveClass(
      'flex',
      'min-h-[50vh]',
      'flex-col',
      'items-center',
      'justify-center',
      'p-4'
    );

    // Check heading styling
    const heading = screen.getByText('Erro ao carregar serviços');
    expect(heading).toHaveClass('mb-4', 'text-xl', 'font-semibold', 'text-red-600');

    // Check paragraph styling
    const paragraph = screen.getByText(
      'Ocorreu um erro ao carregar a lista de serviços. Por favor, tente novamente mais tarde.'
    );
    expect(paragraph).toHaveClass('mb-4', 'text-gray-600');
  });

  test('maintains consistent layout structure', () => {
    render(<ErrorSection />);

    // Check if all elements are present in the correct order
    const button = screen.getByRole('button');
    const container = button.parentElement;
    if (!container) {
      throw new Error('Container element not found');
    }

    expect(container.children).toHaveLength(3); // h2, p, button

    const heading = container.children[0];
    expect(heading.tagName).toBe('H2');

    const paragraph = container.children[1];
    expect(paragraph.tagName).toBe('P');

    const buttonElement = container.children[2];
    expect(buttonElement.tagName).toBe('BUTTON');
  });
});
