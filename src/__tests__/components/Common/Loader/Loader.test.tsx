import { Loader } from '@/src/app/_components/Common/Loader/Loader';
import { render, screen } from '@testing-library/react';

// Mock the Loader2 icon from lucide-react
jest.mock('lucide-react', () => ({
  Loader2: ({
    className,
    'aria-label': ariaLabel,
  }: {
    className?: string;
    'aria-label'?: string;
  }) => <svg className={className} aria-label={ariaLabel} role="img" />,
}));

describe('Loader Component', () => {
  test('renders loading spinner', () => {
    render(<Loader />);

    // Check if the loading spinner is present
    const spinner = screen.getByRole('img', { name: 'Carregan<PERSON>' });
    expect(spinner).toBeInTheDocument();
  });

  test('has correct accessibility attributes', () => {
    render(<Loader />);

    // Check if the loading spinner has the correct aria-label
    const spinner = screen.getByRole('img', { name: '<PERSON><PERSON><PERSON>' });
    expect(spinner).toHaveAttribute('aria-label', 'Carregando');
  });

  test('has correct styling classes', () => {
    render(<Loader />);

    // Check container styling
    const container = screen.getByRole('img').parentElement;
    if (!container) {
      throw new Error('Container element not found');
    }

    expect(container).toHaveClass(
      'fixed',
      'inset-0',
      'z-50',
      'flex',
      'items-center',
      'justify-center',
      'bg-background/80',
      'backdrop-blur-sm'
    );

    // Check spinner styling
    const spinner = screen.getByRole('img');
    expect(spinner).toHaveClass('h-16', 'w-16', 'animate-spin', 'text-primary');
  });

  test('covers the entire viewport', () => {
    render(<Loader />);

    const container = screen.getByRole('img').parentElement;
    if (!container) {
      throw new Error('Container element not found');
    }

    expect(container).toHaveClass('fixed', 'inset-0');
  });

  test('has proper z-index to appear above other content', () => {
    render(<Loader />);

    const container = screen.getByRole('img').parentElement;
    if (!container) {
      throw new Error('Container element not found');
    }

    expect(container).toHaveClass('z-50');
  });

  test('has semi-transparent background with blur effect', () => {
    render(<Loader />);

    const container = screen.getByRole('img').parentElement;
    if (!container) {
      throw new Error('Container element not found');
    }

    expect(container).toHaveClass('bg-background/80', 'backdrop-blur-sm');
  });
});
