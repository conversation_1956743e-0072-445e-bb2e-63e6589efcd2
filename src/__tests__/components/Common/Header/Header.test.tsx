import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';

// Mock dos componentes antes de importar o Header
jest.mock('@/src/app/_components', () => ({
  Button: (props: any) => <button {...props} />,
  Icon: (props: any) => (
    <span {...props} data-testid={props.name ? `icon-${props.name}` : 'icon'} />
  ),
}));

// Mock dos hooks
jest.mock('@/src/app/_hooks/useMenu', () => ({
  useMenu: () => ({
    isMenuOpen: false,
    toggleMenu: jest.fn(),
  }),
}));

jest.mock('@/src/app/_hooks/useSubmenu', () => ({
  useSubmenu: () => ({
    isSubmenuOpen: false,
    handleMouseEnter: jest.fn(),
    handleMouseLeave: jest.fn(),
  }),
}));

// Mock do usePathname
jest.mock('next/navigation', () => ({
  usePathname: () => '/test-path',
}));

// Mock dos componentes dinâmicos
jest.mock('next/dynamic', () => () => {
  const DynamicComponent = (props: any) => (
    <div data-testid="dynamic-component">{props.children}</div>
  );
  DynamicComponent.displayName = 'MockedDynamicComponent';
  return DynamicComponent;
});

// Mock de imagem do Next.js
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ ...props }: any) => {
    // Remover priority que causa warnings no React
    return <img {...props} data-testid="next-image" />;
  },
}));

// Mock de Link do Next.js
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ children, ...props }: any) => <a {...props}>{children}</a>,
}));

// Importar o Header depois dos mocks
import Header from '@/src/app/_components/Common/Header/Header';

describe('Header Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the Header component correctly', () => {
    render(<Header logoPath="/images/test-logo.svg" />);

    // Check logo
    const logo = screen.getByTestId('next-image');
    expect(logo).toBeInTheDocument();
    expect(logo).toHaveAttribute('src', '/images/test-logo.svg');
    expect(logo).toHaveAttribute('alt', 'GetNinjas Logo');

    // Check for mobile menu button
    const menuButton = screen.getByLabelText('Open menu');
    expect(menuButton).toBeInTheDocument();
    expect(menuButton).toHaveClass('md:hidden');
  });

  it('calls toggleMenu when mobile menu button is clicked', () => {
    const mockToggleMenu = jest.fn();

    // Substituir o mock do hook useMenu apenas para este teste
    jest.requireMock('@/src/app/_hooks/useMenu').useMenu = () => ({
      isMenuOpen: false,
      toggleMenu: mockToggleMenu,
    });

    render(<Header logoPath="/images/test-logo.svg" />);

    const menuButton = screen.getByLabelText('Open menu');
    fireEvent.click(menuButton);

    expect(mockToggleMenu).toHaveBeenCalledTimes(1);
  });

  it('uses default logo when logoPath is not provided', () => {
    render(<Header logoPath="" />);

    const logo = screen.getByTestId('next-image');
    expect(logo).toHaveAttribute('src', '/images/getninjas_europ_logo.svg');
  });

  it('handles logo load error by setting fallback image', () => {
    render(<Header logoPath="/images/test-logo.svg" />);

    const logo = screen.getByTestId('next-image');

    // Simula o evento de erro
    fireEvent.error(logo);

    // Verifica se a imagem de fallback foi definida
    expect(logo).toHaveAttribute('src', '/images/getninjas_europ_logo.svg');
  });

  it('displays menu icon when menu is closed', () => {
    jest.requireMock('@/src/app/_hooks/useMenu').useMenu = () => ({
      isMenuOpen: false,
      toggleMenu: jest.fn(),
    });

    render(<Header logoPath="/images/test-logo.svg" />);

    // Verifica se o ícone do menu está presente quando o menu está fechado
    expect(screen.getByTestId('icon-Menu')).toBeInTheDocument();
    expect(screen.queryByTestId('icon-X')).not.toBeInTheDocument();
  });

  it('displays X icon when menu is open', () => {
    jest.requireMock('@/src/app/_hooks/useMenu').useMenu = () => ({
      isMenuOpen: true,
      toggleMenu: jest.fn(),
    });

    render(<Header logoPath="/images/test-logo.svg" />);

    // Verifica se o ícone X está presente quando o menu está aberto
    expect(screen.getByTestId('icon-X')).toBeInTheDocument();
    expect(screen.queryByTestId('icon-Menu')).not.toBeInTheDocument();
  });

  it('renders the header with correct classes for fixed positioning', () => {
    render(<Header logoPath="/images/test-logo.svg" />);

    const header = screen.getByRole('banner');
    expect(header).toHaveClass('fixed');
    expect(header).toHaveClass('top-0');
    expect(header).toHaveClass('z-[60]');
    expect(header).toHaveClass('border-b');
    expect(header).toHaveClass('border-gray-200');
    expect(header).toHaveClass('bg-white');
    expect(header).toHaveClass('shadow-[0px_1px_4px_0px_rgba(0,0,0,0.08)]');
  });

  it('renders the logo link with href to homepage', () => {
    render(<Header logoPath="/images/test-logo.svg" />);

    const logoLink = screen.getByRole('link');
    expect(logoLink).toHaveAttribute('href', '/');
  });

  it('passes correct props to the DesktopNavigation component', () => {
    // Mock para capturar os props passados para o DesktopNavigation
    jest.mock('next/dynamic', () => () => {
      return (props: any) => <div data-testid="desktop-navigation-props" {...props} />;
    });

    const { _isSubmenuOpen, _handleMouseEnter, _handleMouseLeave } = jest
      .requireMock('@/src/app/_hooks/useSubmenu')
      .useSubmenu();

    render(<Header logoPath="/images/test-logo.svg" />);

    // Verificar se os componentes dinâmicos estão sendo renderizados
    expect(screen.getAllByTestId('dynamic-component').length).toBeGreaterThan(0);
  });
});
