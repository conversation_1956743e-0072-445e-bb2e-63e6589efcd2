import {
  ClientLayoutWrapper,
  DynamicFAQ,
} from '@/src/app/_components/Common/DynamicImports/ClientDynamicImports';
import { render, screen, within } from '@testing-library/react';
import React from 'react';

// Mock next/dynamic to resolve components for testing
jest.mock('next/dynamic', () => (load: () => Promise<any>) => {
  let Component: React.ComponentType | null = null;
  // Eagerly load the component in the test environment
  load()
    .then((mod) => {
      Component = mod.default || mod;
    })
    .catch((err) => {
      // Log errors if the dynamic import fails during the test setup
      console.error('Mocked dynamic import failed:', err);
    });
  // Return a component that renders the loaded component or a placeholder
  const DynamicComponent = (props: any) =>
    Component ? <Component {...props} /> : <div>Loading...</div>;
  DynamicComponent.displayName = 'MockedDynamic';
  return DynamicComponent;
});

// Mock the dynamically imported components
jest.mock('@/src/app/_components/Common/Header/Header', () => ({
  __esModule: true,
  default: ({ logoPath }: { logoPath: string }) => (
    <div data-testid="mock-header" data-logo-path={logoPath}>
      Header Content
    </div>
  ),
}));

jest.mock('@/src/app/_components/Common/Footer/Footer', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-footer">Footer Content</div>,
}));

jest.mock('@/src/app/_components/Common/ErrorBoundary/HydrationErrorBoundary', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="mock-hydration-error-boundary">{children}</div>
  ),
}));

// Mock the ClientCookieBanner component
jest.mock('@/src/app/_components/Common/CookieBanner/ClientCookieBanner', () => ({
  ClientCookieBanner: () => <div data-testid="mock-cookie-banner">Cookie Banner</div>,
}));

jest.mock('@/src/app/_components/Pages/Home/FAQ', () => ({
  __esModule: true, // Ensure ES Module compatibility
  FAQ: () => <div data-testid="mock-faq">FAQ Content</div>,
}));

describe('ClientDynamicImports', () => {
  describe('ClientLayoutWrapper', () => {
    it('renders with correct component hierarchy', async () => {
      const logoPath = '/test-logo.svg';
      const testContent = <div data-testid="test-content">Test Content</div>;

      render(<ClientLayoutWrapper logoPath={logoPath}>{testContent}</ClientLayoutWrapper>);

      // Use findBy* to wait for async components
      expect(await screen.findByTestId('mock-header')).toBeInTheDocument();
      expect(await screen.findByTestId('mock-footer')).toBeInTheDocument();
      expect(await screen.findByTestId('mock-cookie-banner')).toBeInTheDocument();
      // Check that at least one error boundary is present
      const errorBoundaries = await screen.findAllByTestId('mock-hydration-error-boundary');
      expect(errorBoundaries.length).toBeGreaterThan(0);
      expect(screen.getByTestId('test-content')).toBeInTheDocument(); // Check for child content

      // Check main element structure with the correct classes from the component
      const mainElement = screen.getByRole('main');
      expect(mainElement).toHaveClass('mx-auto', 'pb-8');

      // Check logo path is passed correctly
      expect(screen.getByTestId('mock-header')).toHaveAttribute('data-logo-path', logoPath);
    });

    it('renders children within main element', async () => {
      const testContent = <div data-testid="test-content">Test Content</div>;

      render(<ClientLayoutWrapper logoPath="/test-logo.svg">{testContent}</ClientLayoutWrapper>);

      // Wait for the main element content to potentially render asynchronously
      const mainElement = await screen.findByRole('main');
      expect(within(mainElement).getByTestId('test-content')).toBeInTheDocument();
    });

    it('wraps children in HydrationErrorBoundary', async () => {
      const testContent = <div data-testid="test-content">Test Content</div>;

      render(<ClientLayoutWrapper logoPath="/test-logo.svg">{testContent}</ClientLayoutWrapper>);

      // Find the main element first
      const mainElement = await screen.findByRole('main');

      // Find the boundary *within* the main element
      const innerBoundary = await within(mainElement).findByTestId('mock-hydration-error-boundary');
      expect(innerBoundary).toBeInTheDocument();

      // Check if the test content is within this inner boundary
      expect(within(innerBoundary).getByTestId('test-content')).toBeInTheDocument();
    });
  });

  describe('DynamicFAQ', () => {
    it('renders FAQ component', async () => {
      render(<DynamicFAQ />);
      // Use findByTestId to wait for the dynamic component
      const faqElement = await screen.findByTestId('mock-faq');
      expect(faqElement).toBeInTheDocument();
    });
  });
});
