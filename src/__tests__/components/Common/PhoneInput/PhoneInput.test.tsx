import { PhoneInput } from '@/src/app/_components/Common/PhoneInput/PhoneInput';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

describe('PhoneInput Component', () => {
  const mockOnChange = jest.fn();
  const mockOnValidationError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders correctly with default props', () => {
    render(<PhoneInput value="" countryCode="+55" onChange={mockOnChange} />);

    const inputElement = screen.getByRole('textbox');
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveValue('');
  });

  test('calls onChange when input value changes', async () => {
    render(<PhoneInput value="" countryCode="+55" onChange={mockOnChange} />);

    const inputElement = screen.getByRole('textbox');
    await userEvent.type(inputElement, '1');

    expect(mockOnChange).toHaveBeenCalledWith('1');
  });

  test('validates phone number on value change', async () => {
    const { rerender } = render(
      <PhoneInput
        value=""
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // Simular interação do usuário primeiro
    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // Update with invalid number (too short)
    rerender(
      <PhoneInput
        value="(65) 16516515"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // Wait for validation to occur
    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith(
        'Insira um número de telefone válido com DDD.'
      );
    });

    // Update with valid number
    rerender(
      <PhoneInput
        value="(11) 98765-4321"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // Wait for validation to occur
    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith(null);
    });
  });

  test('shows error message for invalid phone number on blur', () => {
    render(
      <PhoneInput
        value="123"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    expect(screen.getByText('Insira um número de telefone válido com DDD.')).toBeInTheDocument();
    expect(mockOnValidationError).toHaveBeenCalledWith(
      'Insira um número de telefone válido com DDD.'
    );
  });

  test('shows error when Brazilian phone number does not have exactly 11 digits', () => {
    render(
      <PhoneInput
        value="(11) 9876-543"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    expect(screen.getByText('Insira um número de telefone válido com DDD.')).toBeInTheDocument();
    expect(mockOnValidationError).toHaveBeenCalledWith(
      'Insira um número de telefone válido com DDD.'
    );
  });

  test('validates correctly when Brazilian phone number has exactly 11 digits with formatting', async () => {
    const { rerender } = render(
      <PhoneInput
        value=""
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // Simular interação do usuário
    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // Aguardar a validação do campo vazio
    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith(
        'Insira um número de telefone válido com DDD.'
      );
    });

    // Atualizar o valor para um número válido
    rerender(
      <PhoneInput
        value="(11) 98765-4321"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // Simular interação novamente
    fireEvent.change(inputElement, { target: { value: '(11) 98765-4321' } });
    fireEvent.blur(inputElement);

    // Aguardar a validação do número válido
    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith(null);
    });
  });

  test('shows required error when input is empty on blur', () => {
    render(
      <PhoneInput
        value=""
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    expect(screen.getByText('Insira um número de telefone válido com DDD.')).toBeInTheDocument();
    expect(mockOnValidationError).toHaveBeenCalledWith(
      'Insira um número de telefone válido com DDD.'
    );
  });

  test('shows error for invalid country code', () => {
    render(
      <PhoneInput
        value="11987654321"
        countryCode="+999"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    expect(screen.getByText('Código do país inválido.')).toBeInTheDocument();
    expect(mockOnValidationError).toHaveBeenCalledWith('Código do país inválido.');
  });

  test('clears error when valid phone number is entered', async () => {
    const { rerender } = render(
      <PhoneInput
        value="123"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // First trigger an error
    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    expect(screen.getByText('Insira um número de telefone válido com DDD.')).toBeInTheDocument();

    // Now update with valid number
    rerender(
      <PhoneInput
        value="(11) 98765-4321"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // Wait for validation to occur
    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith(null);
    });
  });

  test('shows error for invalid Brazilian DDD', () => {
    render(
      <PhoneInput
        value="(00) 98765-4321" // 00 is not a valid DDD in Brazil
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    expect(screen.getByText('O DDD informado não é válido no Brasil.')).toBeInTheDocument();
    expect(mockOnValidationError).toHaveBeenCalledWith('O DDD informado não é válido no Brasil.');
  });

  test('handles invalid phone number format', () => {
    // Use an invalid phone format that will cause validation to fail
    render(
      <PhoneInput
        value="invalid-phone-format"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // The component should handle the validation error gracefully
    expect(mockOnValidationError).toHaveBeenCalledWith(
      'Insira um número de telefone válido com DDD.'
    );
  });

  test('handles phone number with special characters', () => {
    // Use a phone number with special characters that might cause parsing issues
    render(
      <PhoneInput
        value="(11) 9876$-43@21"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // The component should handle the validation error gracefully
    expect(mockOnValidationError).toHaveBeenCalledWith(
      'Insira um número de telefone válido com DDD.'
    );
  });

  test('handles case when onValidationError is not provided', () => {
    render(
      <PhoneInput
        value="123" // Invalid number
        countryCode="+55"
        onChange={mockOnChange}
        // No onValidationError prop
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // Component should not crash even without onValidationError
    expect(screen.getByText('Insira um número de telefone válido com DDD.')).toBeInTheDocument();
  });

  test('handles input change and sets hasInteracted', () => {
    render(
      <PhoneInput
        value=""
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');

    // Change the input value
    fireEvent.change(inputElement, { target: { value: '123' } });

    // Check that onChange was called and hasInteracted was set
    expect(mockOnChange).toHaveBeenCalledWith('123');

    // Now blur to trigger validation (which should happen because hasInteracted is true)
    fireEvent.blur(inputElement);

    // Validation error should be shown
    expect(mockOnValidationError).toHaveBeenCalledWith(
      'Insira um número de telefone válido com DDD.'
    );
  });
});
