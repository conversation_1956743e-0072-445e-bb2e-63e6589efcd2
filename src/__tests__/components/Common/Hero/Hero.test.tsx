import { render, screen } from '@testing-library/react';
import { Hero } from '@/src/app/_components/Common/Hero/Hero';

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, className }: any) => <img src={src} alt={alt} className={className} />,
}));

describe('Hero', () => {
  it('renders the hero section with correct content', () => {
    render(<Hero />);

    // Check for main heading
    const heading = screen.getByRole('heading', {
      name: 'Seu problema resolvido com garantia e confiança',
    });
    expect(heading).toBeInTheDocument();
    expect(heading).toHaveClass('text-3xl', 'font-bold');

    // Check for description text
    const description = screen.getByText(
      'O GetNinjas ajuda você a encontrar o serviço que você precisa com segurança e agilidade, em parceria com a Europ Assistance.'
    );
    expect(description).toBeInTheDocument();
    expect(description).toHaveClass('text-base', 'text-gray-600');

    // Check for image
    const image = screen.getByRole('img');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute(
      'alt',
      'Mosaico de fotos de quatro profissionais diferentes prestando diversos serviços como troca de fechadura, conserto de pia, dedetização e troca de lustre.'
    );
    expect(image).toHaveClass('max-h-60', 'max-w-80', 'scale-110');
  });

  it('renders with correct layout classes', () => {
    const { container: rootContainer } = render(<Hero />);

    // Check for main section
    const section = rootContainer.querySelector('section');
    expect(section).toHaveClass('flex', 'min-h-[70vh]', 'items-center');

    // Check for container div
    const containerDiv = section?.firstChild;
    expect(containerDiv).toHaveClass('container', 'mx-auto', 'px-4');

    // Check for content wrapper
    const contentWrapper = containerDiv?.firstChild;
    expect(contentWrapper).toHaveClass('flex', 'flex-col', 'items-center');
  });
});
