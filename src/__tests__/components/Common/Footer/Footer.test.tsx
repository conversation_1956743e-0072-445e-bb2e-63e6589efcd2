import Footer from '@/src/app/_components/Common/Footer/Footer';
import { fireEvent, render, screen } from '@testing-library/react';

jest.mock('@/src/app/_functions/analytics/common/trackClickEvent', () => ({
  trackClickEvent: jest.fn(),
}));

import { trackClickEvent } from '@/src/app/_functions/analytics/common/trackClickEvent';

// Mock the window.innerWidth to test responsive behavior
const originalInnerWidth = window.innerWidth;
const setWindowInnerWidth = (width: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  window.dispatchEvent(new Event('resize'));
};

describe('Footer Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset window.innerWidth
    setWindowInnerWidth(originalInnerWidth);

    process.env.NEXT_PUBLIC_RECLAME_AQUI_URL = 'https://www.reclameaqui.com.br/empresa/getninjas/';
    process.env.NEXT_PUBLIC_FOOTER_RATING_IMAGE_URL = '/images/reclameaqui.png';
    process.env.NEXT_PUBLIC_INSTAGRAM_URL = 'https://www.instagram.com/getninjas';
    process.env.NEXT_PUBLIC_YOUTUBE_URL =
      'https://www.youtube.com/channel/UCWgyIuQWHB3u88SY1hr5zZg';
    process.env.NEXT_PUBLIC_TIKTOK_URL = 'https://www.tiktok.com/@getninjas';
    process.env.NEXT_PUBLIC_GETNINJAS_URL = 'https://www.getninjas.com.br';
  });

  afterAll(() => {
    // Reset window.innerWidth after all tests
    setWindowInnerWidth(originalInnerWidth);
  });

  test('should render footer correctly', () => {
    render(<Footer />);

    // Verifica se o texto "Site seguro" está renderizando
    expect(screen.getByText('Site seguro')).toBeInTheDocument();

    // Verifica se o selo ReclameAqui é renderizado
    expect(screen.getByAltText('Selo ReclameAqui')).toBeInTheDocument();

    // Verifica se o copyright é renderizado corretamente
    expect(screen.getByText(/©/)).toBeInTheDocument();

    // Verifica se a seção de cancelamento está presente
    expect(screen.getByText('Precisou cancelar ou reagendar?')).toBeInTheDocument();
    expect(screen.getByText(/Central de Atendimento da Europ/)).toBeInTheDocument();
    expect(screen.getByText(/0800 202 4011/)).toBeInTheDocument();
  });

  test('should display the current year in the copyright notice', () => {
    // Mock current year for testing
    const realDate = Date;
    global.Date = class extends Date {
      constructor(...args: any[]) {
        if (args.length === 0) {
          return new realDate(2023, 0, 1);
        }
        return new realDate(...args);
      }
      getFullYear() {
        return 2023;
      }
    } as any;

    render(<Footer />);

    expect(screen.getByText(/© 2023/)).toBeInTheDocument();

    // Restore Date
    global.Date = realDate;
  });

  test('should display "Todos os direitos reservados" text', () => {
    render(<Footer />);
    expect(screen.getByText(/Todos os direitos reservados/)).toBeInTheDocument();
  });

  test('should open ReclameAqui link and trigger tracking event', () => {
    render(<Footer />);

    const reclameAquiLink = screen.getByRole('link', { name: 'GetNinjas no ReclameAqui' });
    fireEvent.click(reclameAquiLink);

    expect(reclameAquiLink).toHaveAttribute(
      'href',
      'https://www.reclameaqui.com.br/empresa/getninjas/'
    );
    expect(trackClickEvent).toHaveBeenCalledWith('ReclameAqui');
  });

  test('should open Instagram link and trigger tracking event', () => {
    render(<Footer />);

    const instagramLink = screen.getByRole('link', {
      name: 'Siga o GetNinjas no Instagram',
    });
    fireEvent.click(instagramLink);

    expect(instagramLink).toHaveAttribute('href', 'https://www.instagram.com/getninjas');
    expect(trackClickEvent).toHaveBeenCalledWith('Instagram');
  });

  test('should open YouTube link and trigger tracking event', () => {
    render(<Footer />);

    const youtubeLink = screen.getByRole('link', {
      name: 'Siga o GetNinjas no YouTube',
    });
    fireEvent.click(youtubeLink);

    expect(youtubeLink).toHaveAttribute(
      'href',
      'https://www.youtube.com/channel/UCWgyIuQWHB3u88SY1hr5zZg'
    );
    expect(trackClickEvent).toHaveBeenCalledWith('Youtube');
  });

  test('should open TikTok link and trigger tracking event', () => {
    render(<Footer />);

    const tiktokLink = screen.getByRole('link', {
      name: 'Siga o GetNinjas no TikTok',
    });
    fireEvent.click(tiktokLink);

    expect(tiktokLink).toHaveAttribute('href', 'https://www.tiktok.com/@getninjas');
    expect(trackClickEvent).toHaveBeenCalledWith('TikTok');

    // Test the alt text for TikTok image
    const tiktokImage = screen.getByAltText('TikTok');
    expect(tiktokImage).toBeInTheDocument();
  });

  test('should open GetNinjas link and trigger tracking event', () => {
    render(<Footer />);

    const getNinjasLink = screen.getByRole('link', {
      name: 'GetNinjas',
    });
    fireEvent.click(getNinjasLink);

    expect(getNinjasLink).toHaveAttribute('href', 'https://www.getninjas.com.br');
    expect(trackClickEvent).toHaveBeenCalledWith('GetNinjas - Footer');
  });

  test('should use fallback URLs when environment variables are not set', () => {
    // Save original values
    const originalReclameAqui = process.env.NEXT_PUBLIC_RECLAME_AQUI_URL;
    const originalInstagram = process.env.NEXT_PUBLIC_INSTAGRAM_URL;
    const originalYoutube = process.env.NEXT_PUBLIC_YOUTUBE_URL;
    const originalTiktok = process.env.NEXT_PUBLIC_TIKTOK_URL;
    const originalGetninjas = process.env.NEXT_PUBLIC_GETNINJAS_URL;

    // Set to empty strings (not undefined)
    process.env.NEXT_PUBLIC_RECLAME_AQUI_URL = '';
    process.env.NEXT_PUBLIC_INSTAGRAM_URL = '';
    process.env.NEXT_PUBLIC_YOUTUBE_URL = '';
    process.env.NEXT_PUBLIC_TIKTOK_URL = '';
    process.env.NEXT_PUBLIC_GETNINJAS_URL = '';

    render(<Footer />);

    // Check fallback URLs
    const reclameAquiLink = screen.getByRole('link', { name: 'GetNinjas no ReclameAqui' });
    expect(reclameAquiLink).toHaveAttribute(
      'href',
      'https://www.reclameaqui.com.br/empresa/getninjas/'
    );

    const instagramLink = screen.getByRole('link', { name: 'Siga o GetNinjas no Instagram' });
    expect(instagramLink).toHaveAttribute('href', 'https://www.instagram.com/getninjas');

    const youtubeLink = screen.getByRole('link', { name: 'Siga o GetNinjas no YouTube' });
    expect(youtubeLink).toHaveAttribute(
      'href',
      'https://www.youtube.com/channel/UCWgyIuQWHB3u88SY1hr5zZg'
    );

    const tiktokLink = screen.getByRole('link', { name: 'Siga o GetNinjas no TikTok' });
    expect(tiktokLink).toHaveAttribute('href', 'https://www.tiktok.com/@getninjas');

    const getNinjasLink = screen.getByRole('link', { name: 'GetNinjas' });
    expect(getNinjasLink).toHaveAttribute('href', 'https://www.getninjas.com.br');

    // Restore original values
    process.env.NEXT_PUBLIC_RECLAME_AQUI_URL = originalReclameAqui;
    process.env.NEXT_PUBLIC_INSTAGRAM_URL = originalInstagram;
    process.env.NEXT_PUBLIC_YOUTUBE_URL = originalYoutube;
    process.env.NEXT_PUBLIC_TIKTOK_URL = originalTiktok;
    process.env.NEXT_PUBLIC_GETNINJAS_URL = originalGetninjas;
  });

  test('should have correct Image dimensions for social icons', () => {
    render(<Footer />);

    // Check ReclameAqui image dimensions
    const reclameAquiImage = screen.getByAltText('Selo ReclameAqui');
    expect(reclameAquiImage).toHaveAttribute('width', '100');
    expect(reclameAquiImage).toHaveAttribute('height', '45');

    // Check TikTok image dimensions
    const tiktokImage = screen.getByAltText('TikTok');
    expect(tiktokImage).toHaveAttribute('width', '24');
    expect(tiktokImage).toHaveAttribute('height', '24');
  });

  test('should have proper CSS classes for responsive behavior', () => {
    render(<Footer />);

    // Test container and main structure
    const footer = screen.getByRole('contentinfo');

    // Test main footer section
    const mainFooter = footer.querySelector('div.border-t.border-border.bg-background');
    expect(mainFooter).toBeInTheDocument();

    // Test flex container
    const flexContainer = footer.querySelector('.flex.flex-col.space-y-10');
    expect(flexContainer).toBeInTheDocument();
    expect(flexContainer).toHaveClass(
      'lg:flex-row',
      'lg:items-center',
      'lg:justify-between',
      'lg:space-y-0'
    );

    // Test copyright section
    const copyright = screen.getByText(/© \d{4}/);
    const copyrightContainer = copyright.closest('div');
    expect(copyrightContainer).toHaveClass('order-4', 'text-center', 'lg:order-2', 'lg:text-left');
  });

  test('should have accessible social media links', () => {
    render(<Footer />);

    // Get all social links
    const instagramLink = screen.getByRole('link', { name: 'Siga o GetNinjas no Instagram' });
    const youtubeLink = screen.getByRole('link', { name: 'Siga o GetNinjas no YouTube' });
    const tiktokLink = screen.getByRole('link', { name: 'Siga o GetNinjas no TikTok' });

    // Test for accessibility attributes
    [instagramLink, youtubeLink, tiktokLink].forEach((link) => {
      expect(link).toHaveAttribute('target', '_blank');
      expect(link).toHaveAttribute('rel', 'noopener noreferrer');
    });
  });
});
