import { ErrorFallback } from '@/src/app/_components/Common/ErrorFallback/ErrorFallback';
import { render, screen } from '@testing-library/react';

// Mock react-markdown
jest.mock('react-markdown', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Mock remark-gfm
jest.mock('remark-gfm', () => ({
  __esModule: true,
  default: () => ({}),
}));

describe('ErrorFallback Component', () => {
  test('renders with error message', () => {
    const error = new Error('Test error message');
    render(<ErrorFallback error={error} />);

    expect(screen.getByText('Aconteceu alguma coisa errada:')).toBeInTheDocument();
    expect(screen.getByText('Test error message')).toBeInTheDocument();
  });

  test('renders with long error message', () => {
    const longMessage =
      'This is a very long error message that should wrap properly in the pre tag and maintain proper formatting and spacing while being displayed to the user in a readable way';
    const error = new Error(longMessage);
    render(<ErrorFallback error={error} />);

    const preElement = screen.getByText(longMessage);
    expect(preElement).toBeInTheDocument();
    expect(preElement).toHaveClass('whitespace-pre-wrap');
  });

  test('has correct accessibility attributes', () => {
    const error = new Error('Test error message');
    render(<ErrorFallback error={error} />);

    const alertElement = screen.getByRole('alert');
    expect(alertElement).toBeInTheDocument();
  });

  test('has correct styling classes', () => {
    const error = new Error('Test error message');
    render(<ErrorFallback error={error} />);

    const container = screen.getByRole('alert');
    expect(container).toHaveClass('rounded-md', 'border-red-400', 'bg-red-100', 'p-4');

    const heading = screen.getByText('Aconteceu alguma coisa errada:');
    expect(heading).toHaveClass('mb-2', 'text-lg', 'font-semibold', 'text-red-800');

    const errorMessage = screen.getByText('Test error message');
    expect(errorMessage).toHaveClass('whitespace-pre-wrap', 'text-sm', 'text-red-600');
  });

  test('handles error object with stack trace', () => {
    const error = new Error('Test error message');
    error.stack = 'Error: Test error message\n    at TestComponent (test.js:1:1)';
    render(<ErrorFallback error={error} />);

    expect(screen.getByText('Test error message')).toBeInTheDocument();
  });

  test('handles error object with custom properties', () => {
    const error = new Error('Test error message') as Error & { customProp?: string };
    error.customProp = 'Custom property';
    render(<ErrorFallback error={error} />);

    expect(screen.getByText('Test error message')).toBeInTheDocument();
  });
});
