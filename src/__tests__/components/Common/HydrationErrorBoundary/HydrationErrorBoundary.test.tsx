import { HydrationErrorBoundary } from '@/src/app/_components/Common/ErrorBoundary/HydrationErrorBoundary';
import { act, render, screen } from '@testing-library/react';

// Mock console.error to prevent noise in test output
const mockConsoleError = jest.spyOn(console, 'error').mockImplementation(() => {});

// Mock setTimeout to control timing in tests
jest.useFakeTimers();

describe('HydrationErrorBoundary Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
  });

  afterEach(() => {
    mockConsoleError.mockClear();
  });

  test('renders children when there is no error', () => {
    const ChildComponent = () => <div>Child Content</div>;

    render(
      <HydrationErrorBoundary>
        <ChildComponent />
      </HydrationErrorBoundary>
    );

    expect(screen.getByText('Child Content')).toBeInTheDocument();
  });

  test('renders default fallback UI when error occurs', () => {
    const ThrowError = () => {
      throw new Error('Test error');
    };

    render(
      <HydrationErrorBoundary>
        <ThrowError />
      </HydrationErrorBoundary>
    );

    expect(
      screen.getByText('Ocorreu um erro de carregamento. Tentando recuperar...')
    ).toBeInTheDocument();
  });

  test('renders custom fallback UI when provided', () => {
    const ThrowError = () => {
      throw new Error('Test error');
    };

    const CustomFallback = () => <div>Custom Error Message</div>;

    render(
      <HydrationErrorBoundary fallback={<CustomFallback />}>
        <ThrowError />
      </HydrationErrorBoundary>
    );

    expect(screen.getByText('Custom Error Message')).toBeInTheDocument();
  });

  test('attempts to recover from error after delay', () => {
    const ThrowError = () => {
      throw new Error('Test error');
    };

    const ChildComponent = () => <div>Child Content</div>;

    const { rerender } = render(
      <HydrationErrorBoundary>
        <ThrowError />
      </HydrationErrorBoundary>
    );

    // Verify error state
    expect(
      screen.getByText('Ocorreu um erro de carregamento. Tentando recuperar...')
    ).toBeInTheDocument();

    // Fast-forward timers and wait for state update
    act(() => {
      jest.advanceTimersByTime(100);
    });

    // Rerender with working component
    rerender(
      <HydrationErrorBoundary>
        <ChildComponent />
      </HydrationErrorBoundary>
    );

    // Wait for the next tick to allow React to process the state update
    act(() => {
      jest.runOnlyPendingTimers();
    });

    // Verify recovery
    expect(screen.getByText('Child Content')).toBeInTheDocument();
  });

  // The implementation doesn't log errors to console, so we'll remove this test

  test('handles multiple errors gracefully', () => {
    const ThrowError = () => {
      throw new Error('Test error');
    };

    const { rerender } = render(
      <HydrationErrorBoundary>
        <ThrowError />
      </HydrationErrorBoundary>
    );

    // First error
    expect(
      screen.getByText('Ocorreu um erro de carregamento. Tentando recuperar...')
    ).toBeInTheDocument();

    // Fast-forward timers
    act(() => {
      jest.advanceTimersByTime(100);
    });

    // Rerender with another error
    rerender(
      <HydrationErrorBoundary>
        <ThrowError />
      </HydrationErrorBoundary>
    );

    // Should still show error state
    expect(
      screen.getByText('Ocorreu um erro de carregamento. Tentando recuperar...')
    ).toBeInTheDocument();
  });
});
