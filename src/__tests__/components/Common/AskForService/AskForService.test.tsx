import { AskForService } from '@/src/app/_components/Common/AskForService/AskForService';
import { render, screen } from '@testing-library/react';

// Mock the Button component and Image component
jest.mock('@/src/app/_components', () => ({
  Button: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <button className={className}>{children}</button>
  ),
}));

jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, className }: { src: string; alt: string; className?: string }) => (
    <img src={src} alt={alt} className={className} />
  ),
}));

describe('AskForService', () => {
  describe('default variant', () => {
    it('renders with default classes', () => {
      const { container } = render(<AskForService />);

      // Check section
      const section = container.querySelector('section');
      expect(section).toHaveClass('rounded-2xl', 'bg-gradient-to-br');

      // Check title
      const title = screen.getByRole('heading', { level: 2 });
      expect(title).toHaveClass('text-2xl', 'font-bold', 'text-gray-800');
      expect(title).toHaveTextContent('Não encontrou o serviço que procura?');

      // Check description
      const description = screen.getByText(
        'Faça um pedido personalizado no GetNinjas e encontre o profissional ideal para sua necessidade.'
      );
      expect(description).toHaveClass('text-base', 'text-gray-600');

      // Check button wrapper
      const buttonWrapper = container.querySelector('.w-full');
      expect(buttonWrapper).toBeInTheDocument();

      // Check button
      const button = screen.getByRole('button');
      expect(button).toHaveClass('rounded-lg', 'bg-gray-800', 'text-white', 'w-full');

      // Check button text
      const buttonText = screen.getByText('Pedir serviço');
      expect(buttonText).toBeInTheDocument();

      const personalizedText = screen.getByText('personalizado');
      expect(personalizedText).toBeInTheDocument();
    });

    it('renders with custom classes', () => {
      const { container } = render(
        <AskForService
          className="custom-section"
          containerClassName="custom-container"
          titleClassName="custom-title"
          descriptionClassName="custom-description"
          buttonClassName="custom-button"
          showIcon={false}
        />
      );

      // Check section
      const section = container.querySelector('section');
      expect(section).toHaveClass('custom-section');

      // Check container
      const containerDiv = section?.querySelector('.container');
      expect(containerDiv).toHaveClass('custom-container');

      // Check title
      const title = screen.getByRole('heading', { level: 2 });
      expect(title).toHaveClass('custom-title');

      // Check description
      const description = screen.getByText(
        'Faça um pedido personalizado no GetNinjas e encontre o profissional ideal para sua necessidade.'
      );
      expect(description).toHaveClass('custom-description');

      // Check button
      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-button');
    });
  });

  describe('compact variant', () => {
    it('renders with default classes', () => {
      const { container } = render(<AskForService variant="compact" />);

      // Check container
      const containerDiv = container.querySelector('div');
      expect(containerDiv).toHaveClass('rounded-lg', 'bg-gray-900');

      // Check title
      const title = screen.getByRole('heading', { level: 3 });
      expect(title).toHaveClass('text-lg', 'font-semibold', 'text-white');
      expect(title).toHaveTextContent('Não encontrou o serviço que precisa?');

      // Check description
      const description = screen.getByText(
        'Faça um pedido personalizado no GetNinjas e encontre o profissional ideal para sua necessidade.'
      );
      expect(description).toHaveClass('text-sm', 'text-gray-300');

      // Check link
      const link = screen.getByRole('link');
      expect(link).toHaveClass('inline-block', 'w-full', 'rounded-md', 'bg-white');
      expect(link).toHaveAttribute('href', 'https://www.getninjas.com.br');
      expect(link).toHaveAttribute('target', '_blank');
      expect(link).toHaveAttribute('rel', 'noopener noreferrer');

      // Check link text
      const linkText = screen.getByText('Pedir serviço');
      expect(linkText).toBeInTheDocument();

      const personalizedText = screen.getByText('personalizado');
      expect(personalizedText).toBeInTheDocument();
    });

    it('renders with custom classes', () => {
      const { container } = render(
        <AskForService
          variant="compact"
          className="custom-container"
          titleClassName="custom-title"
          descriptionClassName="custom-description"
          buttonClassName="custom-button"
          showIcon={false}
        />
      );

      // Check container
      const containerDiv = container.querySelector('div');
      expect(containerDiv).toHaveClass('custom-container');

      // Check title
      const title = screen.getByRole('heading', { level: 3 });
      expect(title).toHaveClass('custom-title');

      // Check description
      const description = screen.getByText(
        'Faça um pedido personalizado no GetNinjas e encontre o profissional ideal para sua necessidade.'
      );
      expect(description).toHaveClass('custom-description');

      // Check link
      const link = screen.getByRole('link');
      expect(link).toHaveClass('custom-button');
    });
  });

  describe('with icon', () => {
    it('renders with icon when showIcon is true', () => {
      const { container } = render(<AskForService showIcon={true} />);

      // Check if the image is rendered
      const image = container.querySelector('img');
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute('src', '/images/GetNinjas_Icon.svg');
      expect(image).toHaveAttribute('alt', 'GetNinjas Europe');
      expect(image).toHaveClass('h-16', 'w-auto');
    });
  });

  describe('custom variant', () => {
    it('renders with custom layout', () => {
      const { container } = render(<AskForService variant="custom" showIcon={true} />);

      // Check section
      const section = container.querySelector('section');
      expect(section).toHaveClass('relative', 'w-full', 'overflow-hidden', 'rounded-3xl');

      // Check gradient style
      expect(section).toHaveStyle({
        background: 'linear-gradient(26deg, #EDA909 -40.7%, #FDE63E 95.2%)',
      });

      // Check grid background
      const gridBackground = container.querySelector('div[class*="bg-grid-gray-300"]');
      expect(gridBackground).toBeInTheDocument();

      // Check container
      const containerDiv = container.querySelector('.relative.z-10.flex');
      expect(containerDiv).toHaveClass('flex-col', 'items-start', 'lg:flex-row');

      // Check icon
      const image = container.querySelector('img');
      expect(image).toBeInTheDocument();

      // Check content div
      const contentDiv = container.querySelectorAll('div')[2]; // First div is grid bg, second is container, third is content
      expect(contentDiv).toBeInTheDocument();

      // Check title
      const title = screen.getByRole('heading', { level: 2 });
      expect(title).toHaveClass('text-xl', 'font-bold', 'leading-tight', 'md:text-2xl');
      expect(title).toHaveTextContent('Não encontrou o serviço que precisa?');

      // Check description
      const description = screen.getByText(
        'Faça um pedido personalizado no GetNinjas e encontre o profissional ideal para sua necessidade.'
      );
      expect(description).toHaveClass('max-w-2xl');

      // Check button wrapper
      const buttonWrapper = container.querySelector('.w-full.lg\\:w-auto');
      expect(buttonWrapper).toBeInTheDocument();

      // Check button
      const button = screen.getByRole('button');
      expect(button).toHaveClass('w-full', 'rounded-xl', 'bg-white');
    });
  });
});
