import { render } from '@testing-library/react';
import { ReactElement } from 'react';
import { ServiceProvider } from '@/src/app/_context/ServiceContext';
import { mockServices } from '@/src/__tests__/fixtures/servicesMock';

// Mock dos hooks do Next.js
const routerMock = {
  push: jest.fn(),
  replace: jest.fn(),
  prefetch: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  pathname: '/test-path',
  query: {},
  asPath: '/test-path',
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
  isFallback: false,
  isReady: true,
  isPreview: false,
};

// Properly mock the Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter() {
    return routerMock;
  },
  usePathname() {
    return '/test-path';
  },
  useSearchParams() {
    return new URLSearchParams();
  },
  useParams() {
    return { slug: 'test-service' };
  },
  redirect: jest.fn(),
}));

// Mock for next/router (older API)
jest.mock('next/router', () => ({
  useRouter() {
    return routerMock;
  },
}));

/**
 * Utilitário para renderizar componentes com providers necessários
 * Permite testar componentes que dependem de contextos
 *
 * @param ui - Componente React a ser renderizado
 * @param options - Opções adicionais para o render
 * @param services - Dados de serviços personalizados (opcional)
 * @returns O resultado do render com queries e outros utilitários
 */
export function renderWithProviders(ui: ReactElement, options = {}, services = mockServices) {
  // Wrapper que fornece todos os providers necessários
  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    return <ServiceProvider services={services}>{children}</ServiceProvider>;
  };

  return render(ui, {
    wrapper: Wrapper,
    ...options,
  });
}
