/**
 * Utilitários compartilhados para testes
 */

/**
 * Simula um evento de clique em um elemento
 * @param element Elemento a ser clicado
 */
export const simulateClick = (element: HTMLElement) => {
  element.click();
};

/**
 * Simula o preenchimento de um campo de texto
 * @param element Elemento de input
 * @param value Valor a ser preenchido
 */
export const simulateChange = (element: HTMLElement, value: string) => {
  const inputElement = element as HTMLInputElement;
  inputElement.value = value;

  // Criar e disparar evento de mudança
  const event = new Event('change', { bubbles: true });
  inputElement.dispatchEvent(event);
};

/**
 * Aguarda por um tempo determinado
 * Útil para testar comportamentos assíncronos
 * @param ms Tempo em milissegundos
 */
export const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * Converte um valor para o formato de moeda
 * @param value Valor numérico
 * @param locale Localização para formatação
 * @param currency Moeda
 * @returns String formatada como moeda
 */
export const formatCurrencyForTest = (
  value: number,
  locale = 'pt-BR',
  currency = 'BRL'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
  }).format(value);
};
