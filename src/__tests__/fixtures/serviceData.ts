/**
 * Dados de exemplo para serviços
 * Usados em testes relacionados a serviços
 */

import { Service } from '@/src/app/_types';

export const mockService: Service = {
  id: 'service-1',
  name: 'Serviç<PERSON> de Teste',
  description: 'Descrição do serviço de teste para fins de teste',
  details: ['Detalhe 1 do serviço', 'Detalhe 2 do serviço', 'Detalhe 3 do serviço'],
  price: {
    finalPrice: 199.9,
    originalPrice: 249.9,
  },
  image: '/images/test-service.jpg',
  slug: 'servico-de-teste',
  provider: {
    id: 'provider-1',
    name: '<PERSON><PERSON><PERSON> de Teste',
    description: 'Descrição do provedor de teste',
    imageUrl: '/images/test-provider.jpg',
    providerUrl: 'https://example.com/provider',
  },
};

export const mockServiceList: Service[] = [
  mockService,
  {
    id: 'service-2',
    name: 'Outro Serviço',
    description: 'Descrição de outro serviço de teste',
    details: ['Detalhe 1', 'Detalhe 2'],
    price: {
      finalPrice: 99.9,
      originalPrice: 129.9,
    },
    image: '/images/another-service.jpg',
    slug: 'outro-servico',
    provider: {
      id: 'provider-2',
      name: 'Outro Provedor',
      description: 'Descrição do outro provedor',
      imageUrl: '/images/another-provider.jpg',
      providerUrl: 'https://example.com/another-provider',
    },
  },
];
