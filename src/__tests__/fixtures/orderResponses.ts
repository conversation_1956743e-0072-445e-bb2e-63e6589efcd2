/**
 * Order API response fixtures
 *
 * These fixtures represent realistic API responses and are used for snapshot testing
 * to ensure our services can handle real-world API response structures
 */

export const orderApiResponses = {
  orderByUuid: {
    service: {
      id: 12345,
      slug: 'plumbing-service',
      name: 'Plumbing Service',
      description: 'Professional plumbing services for your home or business',
      imageUrl: 'https://example.com/images/services/plumbing.jpg',
      status: 'active',
      preparations: 'Please ensure access to all relevant areas of your home.',
      termsConditionsUrl: 'https://example.com/terms/plumbing-service',
      availableIn: ['São Paulo', 'Rio de Janeiro', 'Belo Horizonte'],
      details: [
        'Emergency service available',
        'Includes basic diagnostics',
        'Parts not included in base price',
        'Guaranteed service for 90 days',
      ],
      serviceLimits: 'Service does not include replacing major pipes or fixtures unless specified.',
      keywords: ['plumbing', 'pipes', 'leak', 'water', 'repair'],
      price: {
        priceId: 9876,
        originalPrice: 150.0,
        discountPrice: 129.99,
        finalPrice: 129.99,
      },
      provider: {
        id: 6789,
        name: 'Professional Plumbers Inc.',
        imageUrl: 'https://example.com/images/providers/prof-plumbers.jpg',
        providerUrl: 'https://example.com/providers/professional-plumbers',
        description: 'Trusted plumbing professionals since 1985',
      },
    },
    appointment: {
      date: '2023-07-15',
      period: 'MANHA',
    },
    customer: {
      customerId: 'cust-45678',
      fullName: 'João Silva',
      phone: '11987654321',
      email: '<EMAIL>',
      document: '12345678900',
    },
    address: {
      numberAd: '123',
      street: 'Rua das Flores',
      neighborhood: 'Jardim Paulista',
      cityName: 'São Paulo',
      uf: 'SP',
      zipCode: '01234567',
      complement: 'Apt 45',
    },
    payment: {
      method: 'credit_card',
      totalPaid: 129.99,
    },
  },

  orderWithAfternoonAppointment: {
    service: {
      id: 54321,
      slug: 'electrical-repair',
      name: 'Electrical Repair',
      description: 'Electrical repair and installation services',
      imageUrl: 'https://example.com/images/services/electrical.jpg',
      status: 'active',
      preparations: 'Ensure power is turned off to problem areas.',
      termsConditionsUrl: 'https://example.com/terms/electrical-service',
      availableIn: ['São Paulo', 'Rio de Janeiro'],
      details: [
        'Licensed electricians',
        'Safety inspections included',
        'Small parts included in base price',
      ],
      serviceLimits: 'Service covers basic electrical repairs and diagnostics.',
      keywords: ['electrical', 'repair', 'installation', 'wiring'],
      price: {
        priceId: 5432,
        originalPrice: 200.0,
        discountPrice: 180.0,
        finalPrice: 180.0,
      },
      provider: {
        id: 8765,
        name: 'Electric Experts',
        imageUrl: 'https://example.com/images/providers/electric-experts.jpg',
        providerUrl: 'https://example.com/providers/electric-experts',
        description: 'Licensed and insured electrical professionals',
      },
    },
    appointment: {
      date: '2023-07-20',
      period: 'TARDE',
    },
    customer: {
      customerId: 'cust-56789',
      fullName: 'Maria Oliveira',
      phone: '11976543210',
      email: '<EMAIL>',
      document: '09876543210',
    },
    address: {
      numberAd: '456',
      street: 'Avenida Paulista',
      neighborhood: 'Bela Vista',
      cityName: 'São Paulo',
      uf: 'SP',
      zipCode: '01311000',
      complement: '',
    },
    payment: {
      method: 'pix',
      totalPaid: 180.0,
    },
  },

  errorResponse: {
    status: 404,
    message: 'Order not found',
    error: 'The requested order UUID does not exist in our records',
    timestamp: '2023-06-01T15:23:45Z',
    path: '/api/order/by-uuid',
  },
};
