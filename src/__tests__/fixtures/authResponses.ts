/**
 * Auth API response fixtures
 *
 * These fixtures represent realistic API responses and are used for snapshot testing
 * to ensure our services can handle real-world API response structures
 */

export const authApiResponses = {
  validToken: {
    access_token:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IlRlc3QgVXNlciIsImlhdCI6MTUxNjIzOTAyMn0.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
    token_type: 'Bearer',
    expires_in: 3600,
    scope: 'read write',
    refresh_token:
      'def50200d20b5f7e6240b9d3cbcb48ad31e75deb29d9d5229457afdce946a9344683e97c69ade7747e47dfce8e24c1e8424fe4b038d7a0c818c7242483e2dab18e38e77bf31fe672231f5e24b752bf94b3f24f852ea11a6a8512e0aa870943bc2961ca75faba2ba8c82cfed2e2e9b513bea9443b9ea1',
  },

  expiredToken: {
    access_token:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI5ODc2NTQzMjEwIiwibmFtZSI6IkV4cGlyZWQgVG9rZW4gVXNlciIsImlhdCI6MTUxNjIzOTAyMiwiZXhwIjoxNTE2MjQyNjIyfQ.Xd6u-Ih0fsw66J8EBkDIjbS3F5yUwXPh3vuPXbxLwfM',
    token_type: 'Bearer',
    expires_in: 0, // Already expired
    scope: 'read',
    refresh_token: 'expired_refresh_token',
  },

  errorResponses: {
    invalidCredentials: {
      error: 'invalid_client',
      error_description: 'Client authentication failed',
      status: 401,
    },

    serverError: {
      error: 'server_error',
      error_description: 'The authorization server encountered an unexpected condition',
      status: 500,
    },

    invalidGrant: {
      error: 'invalid_grant',
      error_description:
        'The provided authorization grant is invalid, expired, revoked, or does not match the redirection URI used in the authorization request',
      status: 400,
    },
  },
};
