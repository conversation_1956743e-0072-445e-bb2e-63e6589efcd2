/**
 * Payment API response fixtures
 *
 * These fixtures represent realistic API responses and are used for snapshot testing
 * to ensure our services can handle real-world API response structures
 */

export const paymentApiResponses = {
  successfulPayment: {
    orderId: 'order-12345-abcde',
    secureId: 'sec-67890-fghij',
    secureUrl:
      'https://payment-gateway.example.com/checkout/secure-session-id?success=https://example.com/success',
    errors: [],
  },

  paymentWithErrors: {
    orderId: 'order-54321-zyxwv',
    secureId: 'sec-98765-utsrq',
    secureUrl:
      'https://payment-gateway.example.com/checkout/secure-session-id?success=https://example.com/success',
    errors: ['Invalid credit card number', 'Credit card has expired'],
  },

  paymentWithoutSecureUrl: {
    orderId: 'order-13579-mnopq',
    secureId: 'sec-24680-rstuv',
    errors: [],
  },

  errorResponse: {
    status: 400,
    message: 'Bad Request',
    error: 'The payment request contains invalid or missing data',
    timestamp: '2023-06-01T16:45:30Z',
    path: '/api/checkout',
  },
};
