import { Category } from '@/src/app/_context/ServiceContext';

export const mockServices: Category[] = [
  {
    id: 1,
    name: 'Assistência Técnica',
    slug: 'assistencia-tecnica',
    subcategories: [
      {
        id: 101,
        name: 'Eletrodomésticos',
        slug: 'eletrodomesticos',
        services: [
          {
            id: 1001,
            name: 'Conserto de Geladeira',
            slug: 'conserto-de-geladeira',
            description: 'Serviço de conserto de geladeira',
            status: 'active',
            price: {
              priceId: 1,
              originalPrice: 150,
              discountPrice: 120,
              finalPrice: 120,
            },
            provider: {
              id: 1,
              name: 'Técnico João',
              providerUrl: '/provider/1',
            },
            availableIn: ['São Paulo', 'Rio de Janeiro'],
            details: ['Garantia de 3 meses', 'Atendimento em 24h'],
            keywords: ['geladeira', 'conserto', 'refrigerador'],
            categoryId: 1,
            categoryName: 'Assistência Técnica',
            categorySlug: 'assistencia-tecnica',
            subcategoryId: 101,
            subcategoryName: 'Eletrodomésticos',
            subcategorySlug: 'eletrodomesticos',
          },
        ],
      },
      {
        id: 102,
        name: 'Celulares',
        slug: 'celulares',
        services: [
          {
            id: 1002,
            name: 'Troca de Tela',
            slug: 'troca-de-tela',
            description: 'Serviço de troca de tela de celular',
            status: 'active',
            price: {
              priceId: 2,
              originalPrice: 200,
              discountPrice: 180,
              finalPrice: 180,
            },
            provider: {
              id: 2,
              name: 'Técnico Pedro',
              providerUrl: '/provider/2',
            },
            availableIn: ['São Paulo'],
            details: ['Garantia de 6 meses'],
            keywords: ['celular', 'tela', 'conserto'],
            categoryId: 1,
            categoryName: 'Assistência Técnica',
            categorySlug: 'assistencia-tecnica',
            subcategoryId: 102,
            subcategoryName: 'Celulares',
            subcategorySlug: 'celulares',
          },
        ],
      },
    ],
  },
  {
    id: 2,
    name: 'Reformas',
    slug: 'reformas',
    subcategories: [
      {
        id: 201,
        name: 'Pinturas',
        slug: 'pinturas',
        services: [
          {
            id: 2001,
            name: 'Pintura Residencial',
            slug: 'pintura-residencial',
            description: 'Serviço de pintura residencial',
            status: 'active',
            price: {
              priceId: 3,
              originalPrice: 500,
              discountPrice: 450,
              finalPrice: 450,
            },
            provider: {
              id: 3,
              name: 'Pintor Silva',
              providerUrl: '/provider/3',
            },
            availableIn: ['São Paulo', 'Campinas'],
            details: ['Material incluso', 'Garantia de 1 ano'],
            keywords: ['pintura', 'residencial', 'casa'],
            categoryId: 2,
            categoryName: 'Reformas',
            categorySlug: 'reformas',
            subcategoryId: 201,
            subcategoryName: 'Pinturas',
            subcategorySlug: 'pinturas',
          },
        ],
      },
    ],
  },
];
