/**
 * Service API response fixtures
 *
 * These fixtures represent realistic API responses and are used for snapshot testing
 * to ensure our services can handle real-world API response structures
 */

export const serviceApiResponses = {
  serviceBySlug: {
    id: 12345,
    slug: 'plumbing-service',
    name: 'Plumbing Service',
    description: 'Professional plumbing services for your home or business',
    imageUrl: 'https://example.com/images/services/plumbing.jpg',
    status: 'active',
    provider: {
      id: 6789,
      name: 'Professional Plumbers Inc.',
      description: 'Trusted plumbing professionals since 1985',
      imageUrl: 'https://example.com/images/providers/prof-plumbers.jpg',
      providerUrl: 'https://example.com/providers/professional-plumbers',
    },
    price: {
      priceId: 9876,
      originalPrice: 150.0,
      discountPrice: 129.99,
      finalPrice: 129.99,
    },
    availableIn: ['São Paulo', 'Rio de Janeiro', 'Belo Horizonte'],
    details: [
      'Emergency service available',
      'Includes basic diagnostics',
      'Parts not included in base price',
      'Guaranteed service for 90 days',
    ],
    serviceLimits:
      'Service does not include replacing major pipes or fixtures unless specified. Additional fees may apply for emergency service outside normal business hours.',
    keywords: ['plumbing', 'pipes', 'leak', 'water', 'repair', 'bathroom', 'kitchen'],
    termsConditionsUrl: 'https://example.com/terms/plumbing-service',
    preparations:
      'Please ensure access to all relevant areas of your home. Clear space around sinks, toilets, and other plumbing fixtures. Shut off water main if there is active flooding.',
  },

  serviceWithMissingData: {
    id: 54321,
    slug: 'electrical-repair',
    name: 'Electrical Repair',
    description: 'Electrical repair and installation services',
    imageUrl: 'https://example.com/images/services/electrical.jpg',
    status: 'active',
    provider: {
      id: 8765,
      name: 'Electric Experts',
      // Missing description
      imageUrl: 'https://example.com/images/providers/electric-experts.jpg',
      // Missing providerUrl
    },
    price: {
      priceId: 5432,
      originalPrice: 200.0,
      discountPrice: 180.0,
      finalPrice: 180.0,
    },
    availableIn: [], // Empty array instead of missing
    details: [], // Empty array instead of missing
    serviceLimits: 'Service covers basic electrical repairs and diagnostics.',
    keywords: [], // Empty array instead of missing
    termsConditionsUrl: 'https://example.com/terms/electrical-service',
    preparations: 'Ensure power is turned off to problem areas before the electrician arrives.',
  },

  // API error response
  errorResponse: {
    status: 500,
    message: 'Internal server error',
    error: 'Server experienced an unexpected issue processing the request',
    timestamp: '2023-06-01T14:35:12Z',
    path: '/service-type/invalid-service',
  },
};
