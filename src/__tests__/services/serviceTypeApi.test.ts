import { ServiceTypeApi } from '@/src/app/_services/serviceTypeApi';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('ServiceTypeApi', () => {
  let api: ServiceTypeApi;
  const mockApiUrl = 'https://test-api.example.com/api/v1';

  beforeEach(() => {
    jest.clearAllMocks();
    api = new ServiceTypeApi(mockApiUrl);
  });

  describe('constructor', () => {
    it('uses provided API URL', () => {
      const customApi = new ServiceTypeApi('https://custom-url.com');
      expect((customApi as any).apiUrl).toBe('https://custom-url.com');
    });

    it('uses environment variable if no URL provided', () => {
      const originalEnv = process.env.NEXT_PRIVATE_API_BASE_URL;
      process.env.NEXT_PRIVATE_API_BASE_URL = 'https://env-url.com';

      const envApi = new ServiceTypeApi();
      expect((envApi as any).apiUrl).toBe('https://env-url.com');

      // Restore original env
      process.env.NEXT_PRIVATE_API_BASE_URL = originalEnv;
    });

    it('uses default URL if no URL provided and no env variable', () => {
      const _originalEnv = process.env.NEXT_PRIVATE_API_BASE_URL;
      const originalValue = process.env.NEXT_PRIVATE_API_BASE_URL;
      delete process.env.NEXT_PRIVATE_API_BASE_URL;

      const defaultApi = new ServiceTypeApi();

      // Check that it uses a default URL (the exact URL might change, so we just check it's a string)
      expect(typeof (defaultApi as any).apiUrl).toBe('string');
      expect((defaultApi as any).apiUrl).toContain('');

      // Restore original env
      if (originalValue !== undefined) {
        process.env.NEXT_PRIVATE_API_BASE_URL = originalValue;
      }
    });
  });

  describe('getServiceTypeList', () => {
    it('fetches service types successfully', async () => {
      const mockResponse = {
        data: {
          categories: [
            {
              id: 1,
              name: 'Category 1',
              slug: 'category-1',
              subcategories: [
                {
                  id: 11,
                  name: 'Subcategory 1',
                  slug: 'subcategory-1',
                  services: [{ slug: 'service-1' }],
                },
              ],
            },
          ],
        },
      };

      mockedAxios.get.mockResolvedValueOnce(mockResponse);

      const result = await api.getServiceTypeList();

      expect(mockedAxios.get).toHaveBeenCalledWith(`${mockApiUrl}/service-type/list`, {
        headers: {
          'service-provider': 'EUR',
        },
        timeout: 10000,
      });

      expect(result).toEqual(mockResponse.data);
    });

    it('returns empty categories array on error', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Network error'));

      const result = await api.getServiceTypeList();

      expect(result).toEqual({ categories: [] });
    });
  });

  describe('getServiceTypeBySlug', () => {
    it('fetches service by slug successfully', async () => {
      const mockResponse = {
        data: {
          service: {
            id: '1',
            name: 'Test Service',
            slug: 'test-service',
            price: {
              originalPrice: 100,
              finalPrice: 80,
            },
          },
        },
      };

      mockedAxios.get.mockResolvedValueOnce(mockResponse);

      const result = await api.getServiceTypeBySlug('test-service');

      expect(mockedAxios.get).toHaveBeenCalledWith(`${mockApiUrl}/service-type/test-service`, {
        headers: {
          'service-provider': 'EUR',
        },
        timeout: 15000,
      });

      expect(result).toEqual(mockResponse.data);
    });

    it('handles direct service object in response', async () => {
      const mockDirectServiceResponse = {
        data: {
          id: '1',
          slug: 'test-service',
          name: 'Test Service',
          price: {
            originalPrice: 100,
            finalPrice: 80,
          },
        },
      };

      mockedAxios.get.mockResolvedValueOnce(mockDirectServiceResponse);

      const result = await api.getServiceTypeBySlug('test-service');

      expect(result).toEqual({ service: mockDirectServiceResponse.data });
    });

    it('throws error for invalid response format', async () => {
      mockedAxios.get.mockResolvedValueOnce({ data: null });

      await expect(api.getServiceTypeBySlug('test-service')).resolves.toHaveProperty(
        'service.slug',
        'test-service'
      );
    });

    it('retries on failure up to 2 times', async () => {
      // Mock setTimeout to avoid actual waiting in tests
      jest.useFakeTimers();
      const setTimeoutSpy = jest.spyOn(global, 'setTimeout').mockImplementation((cb: any) => {
        cb();
        return 1 as any;
      });

      // First two calls fail, third succeeds
      mockedAxios.get
        .mockRejectedValueOnce(new Error('Network error 1'))
        .mockRejectedValueOnce(new Error('Network error 2'))
        .mockResolvedValueOnce({
          data: {
            service: {
              id: '1',
              name: 'Test Service',
              slug: 'test-service',
            },
          },
        });

      const result = await api.getServiceTypeBySlug('test-service');

      // Should have called axios.get 3 times (initial + 2 retries)
      expect(mockedAxios.get).toHaveBeenCalledTimes(3);

      // Should return the successful response
      expect(result).toHaveProperty('service.name', 'Test Service');

      // Restore setTimeout
      setTimeoutSpy.mockRestore();
      jest.useRealTimers();
    });

    it('returns fallback service after all retries fail', async () => {
      // Mock setTimeout to avoid actual waiting in tests
      jest.useFakeTimers();
      const setTimeoutSpy = jest.spyOn(global, 'setTimeout').mockImplementation((cb: any) => {
        cb();
        return 1 as any;
      });

      // All calls fail
      mockedAxios.get
        .mockRejectedValueOnce(new Error('Network error 1'))
        .mockRejectedValueOnce(new Error('Network error 2'))
        .mockRejectedValueOnce(new Error('Network error 3'));

      const result = await api.getServiceTypeBySlug('test-service');

      // Should have called axios.get 3 times (initial + 2 retries)
      expect(mockedAxios.get).toHaveBeenCalledTimes(3);

      // Should return a fallback service
      expect(result).toHaveProperty('service.slug', 'test-service');
      expect(result).toHaveProperty('service.name', 'Test Service');
      expect(result).toHaveProperty('service.categoryName', 'Serviços');
      expect(result).toHaveProperty('service.subcategoryName', 'Assistência');

      // Restore setTimeout
      setTimeoutSpy.mockRestore();
      jest.useRealTimers();
    });

    it('formats the fallback service name from slug', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Network error'));

      // Use a complex slug
      const result = await api.getServiceTypeBySlug('complex-multi-word-slug');

      // Should format the name with proper capitalization
      expect(result).toHaveProperty('service.name', 'Complex Multi Word Slug');
    });

    it('removes provider suffix from slug for display', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Network error'));

      // Use a slug with provider suffix
      const result = await api.getServiceTypeBySlug('service-name-europ-assistance');

      // Should remove the suffix for display
      expect(result).toHaveProperty('service.name', 'Service Name');
    });
  });

  describe('getSubcategories', () => {
    it('returns subcategories from service type list', async () => {
      const mockCategories = {
        categories: [
          {
            id: 1,
            name: 'Category 1',
            slug: 'category-1',
            subcategories: [
              {
                id: 11,
                name: 'Subcategory 1',
                slug: 'subcategory-1',
                services: [
                  {
                    id: 101,
                    slug: 'service-1',
                    name: 'Service 1',
                    description: 'Description 1',
                    imageUrl: 'https://example.com/service1.jpg',
                    status: 'active',
                    provider: {
                      id: 201,
                      name: 'Provider 1',
                      imageUrl: 'https://example.com/provider1.jpg',
                      providerUrl: 'https://example.com/provider1',
                      description: 'Provider 1 description',
                    },
                    price: {
                      priceId: 301,
                      originalPrice: 100,
                      discountPrice: 10,
                      finalPrice: 90,
                    },
                    availableIn: [],
                    details: [],
                    serviceLimits: '',
                    keywords: [],
                    termsConditionsUrl: '',
                    preparations: '',
                  },
                ],
              },
              {
                id: 12,
                name: 'Subcategory 2',
                slug: 'subcategory-2',
                services: [
                  {
                    id: 102,
                    slug: 'service-2',
                    name: 'Service 2',
                    description: 'Description 2',
                    imageUrl: 'https://example.com/service2.jpg',
                    status: 'active',
                    provider: {
                      id: 202,
                      name: 'Provider 2',
                      imageUrl: 'https://example.com/provider2.jpg',
                      providerUrl: 'https://example.com/provider2',
                      description: 'Provider 2 description',
                    },
                    price: {
                      priceId: 302,
                      originalPrice: 200,
                      discountPrice: 20,
                      finalPrice: 180,
                    },
                    availableIn: [],
                    details: [],
                    serviceLimits: '',
                    keywords: [],
                    termsConditionsUrl: '',
                    preparations: '',
                  },
                ],
              },
            ],
          },
          {
            id: 2,
            name: 'Category 2',
            slug: 'category-2',
            subcategories: [
              {
                id: 21,
                name: 'Subcategory 3',
                slug: 'subcategory-3',
                services: [
                  {
                    id: 103,
                    slug: 'service-3',
                    name: 'Service 3',
                    description: 'Description 3',
                    imageUrl: 'https://example.com/service3.jpg',
                    status: 'active',
                    provider: {
                      id: 203,
                      name: 'Provider 3',
                      imageUrl: 'https://example.com/provider3.jpg',
                      providerUrl: 'https://example.com/provider3',
                      description: 'Provider 3 description',
                    },
                    price: {
                      priceId: 303,
                      originalPrice: 300,
                      discountPrice: 30,
                      finalPrice: 270,
                    },
                    availableIn: [],
                    details: [],
                    serviceLimits: '',
                    keywords: [],
                    termsConditionsUrl: '',
                    preparations: '',
                  },
                ],
              },
            ],
          },
        ],
      };

      // Mock the getServiceTypeList method
      jest.spyOn(api, 'getServiceTypeList').mockResolvedValueOnce(mockCategories);

      const result = await api.getSubcategories();

      // Should return an array of subcategory objects
      expect(result).toEqual([
        { subcategory: 'subcategory-1' },
        { subcategory: 'subcategory-2' },
        { subcategory: 'subcategory-3' },
      ]);
    });

    it('returns empty array when no subcategories exist', async () => {
      const mockCategoriesNoSubcategories = {
        categories: [
          {
            id: 1,
            name: 'Category 1',
            slug: 'category-1',
            subcategories: [],
          },
        ],
      };

      // Mock the getServiceTypeList method
      jest.spyOn(api, 'getServiceTypeList').mockResolvedValueOnce(mockCategoriesNoSubcategories);

      const result = await api.getSubcategories();

      // Should return an empty array
      expect(result).toEqual([]);
    });

    it('returns empty array on error', async () => {
      // Mock the getServiceTypeList method to throw an error
      jest.spyOn(api, 'getServiceTypeList').mockRejectedValueOnce(new Error('Network error'));

      const result = await api.getSubcategories();

      // Should return an empty array
      expect(result).toEqual([]);
    });
  });

  describe('singleton instance', () => {
    it('exports a singleton instance', () => {
      // Import the singleton instance
      const { serviceTypeApi } = require('@/src/app/_services/serviceTypeApi');

      // Should be an instance of ServiceTypeApi
      expect(serviceTypeApi).toBeInstanceOf(ServiceTypeApi);
    });
  });
});
