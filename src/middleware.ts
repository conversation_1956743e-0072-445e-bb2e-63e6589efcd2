import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

// Lista de rotas que devem ter cache
const CACHED_ROUTES = ['/static', '/_next/static', '/images', '/fonts'];

// Lista de extensões de arquivo que devem ter cache
const CACHED_EXTENSIONS = [
  'jpg',
  'jpeg',
  'png',
  'gif',
  'ico',
  'svg',
  'webp',
  'css',
  'js',
  'woff',
  'woff2',
  'ttf',
  'eot',
];

// Arquivos que nunca devem ser cacheados
const NEVER_CACHE_PATHS = ['/api/health', '/api/checkout', '/api/auth', '/api/user'];

export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  const { pathname } = request.nextUrl;

  // Função para verificar se é um asset estático
  const isStaticAsset = () => {
    return (
      CACHED_ROUTES.some((route) => pathname.startsWith(route)) ||
      CACHED_EXTENSIONS.some((ext) => pathname.endsWith(`.${ext}`))
    );
  };

  // Função para verificar se é uma rota de API
  const isApiRoute = () => pathname.startsWith('/api');

  // Função para verificar se é um arquivo que nunca deve ser cacheado
  const isNeverCachedPath = () =>
    NEVER_CACHE_PATHS.some((path) => pathname === path || pathname.startsWith(path));

  // Define a estratégia de cache baseada no tipo de rota
  if (pathname === '/sitemap.xml' || pathname === '/robots.txt') {
    // Configuração especial para sitemap.xml e robots.txt dinâmicos
    // Cache curto para reduzir carga no servidor, mas garantir atualizações frequentes
    response.headers.set('Cache-Control', 'public, max-age=60, s-maxage=300, must-revalidate');
  } else if (isNeverCachedPath() || isApiRoute()) {
    // Sem cache para rotas específicas que nunca devem ser cacheadas e rotas de API
    response.headers.set('Cache-Control', 'no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
  } else if (isStaticAsset()) {
    // Cache reduzido para assets estáticos
    response.headers.set('Cache-Control', 'public, max-age=60, must-revalidate');
  } else {
    // Desabilita cache para páginas HTML em todos os ambientes
    response.headers.set('Cache-Control', 'no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
  }

  // Adiciona headers de segurança
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
}

// Configuração para adicionar o middleware em todas as rotas
export const config = {
  matcher: [
    /*
     * Corresponde a todas as requisições que:
     * - não começam com api/auth
     * - não contêm um file extension (e.g. .jpg)
     */
    '/((?!api/auth).*)',
  ],
};
