#!/usr/bin/env node

/**
 * Delete Original Images Script
 *
 * This script deletes original image files (JPG, JPEG, PNG) after WebP conversion
 * to save space and only keep the optimized WebP versions.
 *
 * Usage:
 *   node scripts/delete-originals.js [--sourceDir=public/images] [--dryRun]
 *
 * Options:
 *   --sourceDir=DIR   Source directory (default: public/images)
 *   --dryRun          Only show what would be deleted without actually deleting
 *   --help            Show this help message
 */

const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
const helpArg = args.find((arg) => arg === '--help');
const dryRunArg = args.find((arg) => arg === '--dryRun');
const sourceDirArg = args.find((arg) => arg.startsWith('--sourceDir='));

const sourceDir = sourceDirArg
  ? path.join(process.cwd(), sourceDirArg.split('=')[1])
  : path.join(process.cwd(), 'public', 'images');
const dryRun = !!dryRunArg;

// Show help if requested
if (helpArg) {
  console.warn(`
Delete Original Images Script

This script deletes original image files (JPG, JPEG, PNG) after WebP conversion
to save space and only keep the optimized WebP versions.

Usage:
  node scripts/delete-originals.js [--sourceDir=public/images] [--dryRun]

Options:
  --sourceDir=DIR   Source directory (default: public/images)
  --dryRun          Only show what would be deleted without actually deleting
  --help            Show this help message
  `);
  process.exit(0);
}

// Configuration
const config = {
  sourceDir,
  dryRun,
  imageExtensions: ['.jpg', '.jpeg', '.png'],
};

// Statistics
const stats = {
  deleted: 0,
  skipped: 0,
  errors: 0,
  totalSaved: 0, // in KB
};

// Get file size in KB
function getFileSizeInKB(filePath) {
  const stats = fs.statSync(filePath);
  return stats.size / 1024;
}

// Process a single file
function processFile(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const dir = path.dirname(filePath);
  const baseName = path.basename(filePath, ext);
  const webpPath = path.join(dir, `${baseName}.webp`);

  // Check if this is an original image file (JPG, JPEG, PNG)
  if (config.imageExtensions.includes(ext)) {
    // Check if a WebP version exists
    if (fs.existsSync(webpPath)) {
      try {
        const originalSize = getFileSizeInKB(filePath);

        if (config.dryRun) {
          console.warn(`Would delete: ${filePath} (${originalSize.toFixed(2)} KB)`);
        } else {
          fs.unlinkSync(filePath);
          console.warn(`Deleted: ${filePath} (${originalSize.toFixed(2)} KB)`);
        }

        stats.deleted++;
        stats.totalSaved += originalSize;
      } catch (error) {
        console.error(`Error deleting ${filePath}:`, error.message);
        stats.errors++;
      }
    } else {
      console.warn(`Skipped: ${filePath} (no WebP version found)`);
      stats.skipped++;
    }
  }
}

// Walk through directory recursively
function walkDirectory(dir) {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      walkDirectory(filePath);
    } else if (stat.isFile()) {
      processFile(filePath);
    }
  });
}

// Main function
function main() {
  console.warn('Delete Original Images Script');
  console.warn('============================');
  console.warn(`Source directory: ${config.sourceDir}`);
  console.warn(`Mode: ${config.dryRun ? 'Dry run (no files will be deleted)' : 'Delete files'}`);
  console.warn('============================');

  if (!fs.existsSync(config.sourceDir)) {
    console.error(`Error: Source directory ${config.sourceDir} does not exist.`);
    process.exit(1);
  }

  const startTime = Date.now();

  try {
    walkDirectory(config.sourceDir);

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.warn('\nOperation Complete');
    console.warn('============================');
    console.warn(`Files ${config.dryRun ? 'that would be deleted' : 'deleted'}: ${stats.deleted}`);
    console.warn(`Files skipped: ${stats.skipped}`);
    console.warn(`Errors: ${stats.errors}`);
    console.warn(
      `Total space ${config.dryRun ? 'that would be saved' : 'saved'}: ${stats.totalSaved.toFixed(2)} KB (${(stats.totalSaved / 1024).toFixed(2)} MB)`
    );
    console.warn(`Duration: ${duration.toFixed(2)} seconds`);
  } catch (error) {
    console.error('Error during operation:', error);
    process.exit(1);
  }
}

// Run the script
main();
