#!/usr/bin/env node

/**
 * Pre-build Script
 *
 * This script runs before the build process to prepare the project:
 * 1. Optimizes images in the public/images directory (when not in CI/Docker environment)
 * 2. Performs any other pre-build tasks
 *
 * Usage:
 *   node scripts/pre-build.js
 */

const { execSync } = require('child_process');
const _path = require('path');
const fs = require('fs');

console.warn('🚀 Running pre-build tasks...');

// Start time measurement
const startTime = Date.now();

// Check if we're running in a Docker/CI environment
const isDocker = () => {
  try {
    return (
      fs.existsSync('/.dockerenv') ||
      (fs.existsSync('/proc/1/cgroup') &&
        fs.readFileSync('/proc/1/cgroup', 'utf8').includes('docker'))
    );
  } catch (_e) {
    return false;
  }
};

const isCI = () => {
  return (
    process.env.CI === 'true' ||
    process.env.CI === '1' ||
    isDocker() ||
    process.env.NODE_ENV === 'production'
  );
};

// Force skip image optimization if SKIP_IMAGE_OPTIMIZATION is set
const forceSkipImageOptimization = process.env.SKIP_IMAGE_OPTIMIZATION === 'true';

try {
  // Run complete image optimization only if not in CI/Docker environment and not explicitly skipped
  if (!isCI() && !forceSkipImageOptimization) {
    console.warn('\n📷 Optimizing all images...');
    execSync('pnpm optimize-all', { stdio: 'inherit' });
  } else {
    console.warn(
      '\n📷 Skipping image optimization in CI/Docker environment or due to SKIP_IMAGE_OPTIMIZATION flag...'
    );
  }

  // Add any other pre-build tasks here
  // For example:
  // - Generate sitemap
  // - Update version information
  // - Clean up temporary files

  // Calculate and display execution time
  const endTime = Date.now();
  const duration = (endTime - startTime) / 1000;

  console.warn(`\n✅ Pre-build tasks completed in ${duration.toFixed(2)} seconds`);
} catch (error) {
  console.error('\n❌ Pre-build tasks failed:', error.message);
  process.exit(1);
}
