#!/usr/bin/env node
/* eslint-disable no-console */

/**
 * Image Optimization Script
 *
 * This script optimizes images in the public/images directory:
 * - Compresses JPG, PNG, and WebP images
 * - Converts JPG and PNG to WebP (while keeping originals)
 * - Preserves directory structure
 * - Skips SVG files (already optimized vector graphics)
 *
 * Usage:
 *   node scripts/optimize-images.js [--quality=80] [--webp-only]
 *
 * Options:
 *   --quality=N     Set quality level (1-100, default: 80)
 *   --webp-only     Only create WebP versions without modifying originals
 *   --help          Show this help message
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const imagemin = require('imagemin');
const imageminMozjpeg = require('imagemin-mozjpeg');
const imageminPngquant = require('imagemin-pngquant');
const imageminWebp = require('imagemin-webp');

// Parse command line arguments
const args = process.argv.slice(2);
const helpArg = args.find((arg) => arg === '--help');
const webpOnlyArg = args.find((arg) => arg === '--webp-only');
const qualityArg = args.find((arg) => arg.startsWith('--quality='));
const quality = qualityArg ? parseInt(qualityArg.split('=')[1], 10) : 80;

// Show help if requested
if (helpArg) {
  console.log(`
Image Optimization Script

Optimizes images in the public/images directory:
- Compresses JPG, PNG, and WebP images
- Converts JPG and PNG to WebP (while keeping originals)
- Preserves directory structure
- Skips SVG files (already optimized vector graphics)

Usage:
  node scripts/optimize-images.js [--quality=80] [--webp-only]

Options:
  --quality=N     Set quality level (1-100, default: 80)
  --webp-only     Only create WebP versions without modifying originals
  --help          Show this help message
  `);
  process.exit(0);
}

// Configuration
const config = {
  quality,
  webpOnly: !!webpOnlyArg,
  sourceDir: path.join(process.cwd(), 'public', 'images'),
  imageExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
};

// Statistics
const stats = {
  processed: 0,
  skipped: 0,
  webpConverted: 0,
  originalOptimized: 0,
  errors: 0,
  totalSavings: 0,
};

// Ensure directory exists
function _ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Get file size in KB
function getFileSizeInKB(filePath) {
  const stats = fs.statSync(filePath);
  return stats.size / 1024;
}

// Process a single image file
async function processImage(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const dir = path.dirname(filePath);
  const baseName = path.basename(filePath, ext);
  const webpPath = path.join(dir, `${baseName}.webp`);

  try {
    // Skip SVG files
    if (ext === '.svg') {
      console.log(`Skipping SVG: ${filePath}`);
      stats.skipped++;
      return;
    }

    // Create WebP version for JPG and PNG files
    if (ext === '.jpg' || ext === '.jpeg' || ext === '.png') {
      const originalSize = getFileSizeInKB(filePath);

      // Create WebP version
      await sharp(filePath).webp({ quality: config.quality }).toFile(webpPath);

      const webpSize = getFileSizeInKB(webpPath);
      const webpSavings = originalSize - webpSize;

      console.log(`Created WebP: ${webpPath} (saved ${webpSavings.toFixed(2)} KB)`);
      stats.webpConverted++;
      stats.totalSavings += webpSavings;
    }

    // Optimize original files if not in webp-only mode
    if (!config.webpOnly) {
      const originalSize = getFileSizeInKB(filePath);

      if (ext === '.jpg' || ext === '.jpeg') {
        // Optimize JPEG
        await imagemin([filePath], {
          destination: dir,
          plugins: [imageminMozjpeg({ quality: config.quality })],
        });
      } else if (ext === '.png') {
        // Optimize PNG
        await imagemin([filePath], {
          destination: dir,
          plugins: [
            imageminPngquant({
              quality: [config.quality / 100, Math.min((config.quality + 10) / 100, 1)],
            }),
          ],
        });
      } else if (ext === '.webp') {
        // Optimize WebP
        await imagemin([filePath], {
          destination: dir,
          plugins: [imageminWebp({ quality: config.quality })],
        });
      }

      const optimizedSize = getFileSizeInKB(filePath);
      const savings = originalSize - optimizedSize;

      console.log(`Optimized: ${filePath} (saved ${savings.toFixed(2)} KB)`);
      stats.originalOptimized++;
      stats.totalSavings += savings;
    }

    stats.processed++;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    stats.errors++;
  }
}

// Walk through directory recursively
function walkDirectory(dir) {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      walkDirectory(filePath);
    } else if (stat.isFile()) {
      const ext = path.extname(filePath).toLowerCase();
      if (config.imageExtensions.includes(ext)) {
        processImage(filePath);
      } else {
        stats.skipped++;
      }
    }
  });
}

// Main function
async function main() {
  console.log('Image Optimization Script');
  console.log('========================');
  console.log(`Source directory: ${config.sourceDir}`);
  console.log(`Quality level: ${config.quality}`);
  console.log(`Mode: ${config.webpOnly ? 'WebP conversion only' : 'Full optimization'}`);
  console.log('========================');

  if (!fs.existsSync(config.sourceDir)) {
    console.error(`Error: Source directory ${config.sourceDir} does not exist.`);
    process.exit(1);
  }

  const startTime = Date.now();

  try {
    walkDirectory(config.sourceDir);

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log('\nOptimization Complete');
    console.log('========================');
    console.log(`Processed: ${stats.processed} images`);
    console.log(`WebP conversions: ${stats.webpConverted}`);
    console.log(`Original files optimized: ${stats.originalOptimized}`);
    console.log(`Skipped: ${stats.skipped} files`);
    console.log(`Errors: ${stats.errors}`);
    console.log(`Total savings: ${stats.totalSavings.toFixed(2)} KB`);
    console.log(`Duration: ${duration.toFixed(2)} seconds`);
  } catch (error) {
    console.error('Error during optimization:', error);
    process.exit(1);
  }
}

// Run the script
main();
