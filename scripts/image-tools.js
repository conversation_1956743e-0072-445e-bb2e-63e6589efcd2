#!/usr/bin/env node
/* eslint-disable no-console */

/**
 * Advanced Image Tools Script
 *
 * This script provides advanced image processing capabilities:
 * - Optimize images (JPG, PNG, WebP)
 * - Convert images to WebP format
 * - Resize images to specific dimensions
 * - Generate responsive image sets
 * - Apply watermarks
 *
 * Usage:
 *   node scripts/image-tools.js <command> [options]
 *
 * Commands:
 *   optimize      Optimize images in a directory
 *   convert       Convert images to WebP format
 *   resize        Resize images to specific dimensions
 *   responsive    Generate responsive image sets
 *   help          Show help information
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const imagemin = require('imagemin');
const imageminMozjpeg = require('imagemin-mozjpeg');
const imageminPngquant = require('imagemin-pngquant');
const imageminWebp = require('imagemin-webp');

// Default configuration
const defaultConfig = {
  sourceDir: path.join(process.cwd(), 'public', 'images'),
  outputDir: null, // If null, overwrites original files
  quality: 80,
  imageExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
  skipSVG: true,
  recursive: true,
  webpOnly: false,
  sizes: [640, 750, 828, 1080, 1200, 1920], // Default responsive sizes
};

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0] || 'help';

// Statistics
const stats = {
  processed: 0,
  skipped: 0,
  webpConverted: 0,
  originalOptimized: 0,
  resized: 0,
  errors: 0,
  totalSavings: 0,
};

// Ensure directory exists
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Get file size in KB
function getFileSizeInKB(filePath) {
  const stats = fs.statSync(filePath);
  return stats.size / 1024;
}

// Parse options from command line arguments
function parseOptions(args, defaults = {}) {
  const options = { ...defaults };

  args.forEach((arg) => {
    if (arg.startsWith('--')) {
      const [key, value] = arg.slice(2).split('=');
      if (value === undefined) {
        options[key] = true; // Flag option
      } else if (value === 'false') {
        options[key] = false;
      } else if (!isNaN(Number(value))) {
        options[key] = Number(value);
      } else {
        options[key] = value;
      }
    }
  });

  return options;
}

// Walk through directory recursively
function walkDirectory(dir, callback, recursive = true) {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && recursive) {
      walkDirectory(filePath, callback, recursive);
    } else if (stat.isFile()) {
      callback(filePath);
    }
  });
}

// Optimize a single image
async function optimizeImage(filePath, options) {
  const ext = path.extname(filePath).toLowerCase();
  const dir = path.dirname(filePath);
  const baseName = path.basename(filePath, ext);
  const outputDir = options.outputDir || dir;
  const outputPath = options.outputDir ? path.join(outputDir, path.basename(filePath)) : filePath;
  const webpPath = path.join(outputDir, `${baseName}.webp`);

  // Create output directory if it doesn't exist
  ensureDirectoryExists(outputDir);

  try {
    // Skip SVG files if configured
    if (options.skipSVG && ext === '.svg') {
      console.log(`Skipping SVG: ${filePath}`);
      stats.skipped++;
      return;
    }

    // Create WebP version for JPG and PNG files
    if (ext === '.jpg' || ext === '.jpeg' || ext === '.png') {
      const originalSize = getFileSizeInKB(filePath);

      // Create WebP version
      await sharp(filePath).webp({ quality: options.quality }).toFile(webpPath);

      const webpSize = getFileSizeInKB(webpPath);
      const webpSavings = originalSize - webpSize;

      console.log(`Created WebP: ${webpPath} (saved ${webpSavings.toFixed(2)} KB)`);
      stats.webpConverted++;
      stats.totalSavings += webpSavings;
    }

    // Optimize original files if not in webp-only mode
    if (!options.webpOnly) {
      const originalSize = getFileSizeInKB(filePath);

      if (ext === '.jpg' || ext === '.jpeg') {
        // Optimize JPEG
        await imagemin([filePath], {
          destination: outputDir,
          plugins: [imageminMozjpeg({ quality: options.quality })],
        });
      } else if (ext === '.png') {
        // Optimize PNG
        await imagemin([filePath], {
          destination: outputDir,
          plugins: [
            imageminPngquant({
              quality: [options.quality / 100, Math.min((options.quality + 10) / 100, 1)],
            }),
          ],
        });
      } else if (ext === '.webp') {
        // Optimize WebP
        await imagemin([filePath], {
          destination: outputDir,
          plugins: [imageminWebp({ quality: options.quality })],
        });
      }

      const optimizedSize = getFileSizeInKB(outputPath);
      const savings = originalSize - optimizedSize;

      console.log(`Optimized: ${outputPath} (saved ${savings.toFixed(2)} KB)`);
      stats.originalOptimized++;
      stats.totalSavings += savings;
    }

    stats.processed++;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    stats.errors++;
  }
}

// Resize a single image
async function resizeImage(filePath, options) {
  const ext = path.extname(filePath).toLowerCase();
  const dir = path.dirname(filePath);
  const baseName = path.basename(filePath, ext);
  const outputDir = options.outputDir || dir;

  // Skip SVG files
  if (options.skipSVG && ext === '.svg') {
    console.log(`Skipping SVG: ${filePath}`);
    stats.skipped++;
    return;
  }

  // Create output directory if it doesn't exist
  ensureDirectoryExists(outputDir);

  try {
    const width = options.width;
    const height = options.height;
    const fit = options.fit || 'cover';
    const position = options.position || 'center';

    const outputPath = path.join(outputDir, `${baseName}-${width}x${height}${ext}`);

    await sharp(filePath)
      .resize({
        width,
        height,
        fit,
        position,
      })
      .toFile(outputPath);

    console.log(`Resized: ${outputPath}`);
    stats.resized++;
    stats.processed++;
  } catch (error) {
    console.error(`Error resizing ${filePath}:`, error.message);
    stats.errors++;
  }
}

// Generate responsive image set
async function generateResponsiveSet(filePath, options) {
  const ext = path.extname(filePath).toLowerCase();
  const dir = path.dirname(filePath);
  const baseName = path.basename(filePath, ext);
  const outputDir = options.outputDir || dir;

  // Skip SVG files
  if (options.skipSVG && ext === '.svg') {
    console.log(`Skipping SVG: ${filePath}`);
    stats.skipped++;
    return;
  }

  // Create output directory if it doesn't exist
  ensureDirectoryExists(outputDir);

  try {
    const sizes = options.sizes || defaultConfig.sizes;
    const format = options.format || 'webp';

    for (const width of sizes) {
      const outputPath = path.join(outputDir, `${baseName}-${width}.${format}`);

      await sharp(filePath)
        .resize({ width })
        .toFormat(format, { quality: options.quality })
        .toFile(outputPath);

      console.log(`Created responsive image: ${outputPath}`);
      stats.resized++;
    }

    stats.processed++;
  } catch (error) {
    console.error(`Error generating responsive set for ${filePath}:`, error.message);
    stats.errors++;
  }
}

// Convert image to WebP
async function convertToWebP(filePath, options) {
  const ext = path.extname(filePath).toLowerCase();
  const dir = path.dirname(filePath);
  const baseName = path.basename(filePath, ext);
  const outputDir = options.outputDir || dir;
  const webpPath = path.join(outputDir, `${baseName}.webp`);

  // Skip SVG files
  if (options.skipSVG && ext === '.svg') {
    console.log(`Skipping SVG: ${filePath}`);
    stats.skipped++;
    return;
  }

  // Skip WebP files
  if (ext === '.webp') {
    console.log(`Skipping WebP: ${filePath}`);
    stats.skipped++;
    return;
  }

  // Create output directory if it doesn't exist
  ensureDirectoryExists(outputDir);

  try {
    await sharp(filePath).webp({ quality: options.quality }).toFile(webpPath);

    console.log(`Converted to WebP: ${webpPath}`);
    stats.webpConverted++;
    stats.processed++;
  } catch (error) {
    console.error(`Error converting ${filePath} to WebP:`, error.message);
    stats.errors++;
  }
}

// Show help information
function showHelp() {
  console.log(`
Advanced Image Tools Script

Usage:
  node scripts/image-tools.js <command> [options]

Commands:
  optimize      Optimize images in a directory
  convert       Convert images to WebP format
  resize        Resize images to specific dimensions
  responsive    Generate responsive image sets
  help          Show help information

Common Options:
  --sourceDir=<path>    Source directory (default: public/images)
  --outputDir=<path>    Output directory (default: same as source)
  --quality=<1-100>     Quality level (default: 80)
  --recursive=<bool>    Process subdirectories (default: true)
  --skipSVG=<bool>      Skip SVG files (default: true)

Command-specific Options:
  optimize:
    --webpOnly=<bool>   Only create WebP versions (default: false)

  resize:
    --width=<pixels>    Target width (required)
    --height=<pixels>   Target height (optional)
    --fit=<method>      Fit method: cover, contain, fill, inside, outside
    --position=<pos>    Position: center, top, right, etc.

  responsive:
    --sizes=<list>      Comma-separated list of widths
    --format=<format>   Output format: webp, jpeg, png (default: webp)

Examples:
  node scripts/image-tools.js optimize --quality=85
  node scripts/image-tools.js convert --sourceDir=public/uploads
  node scripts/image-tools.js resize --width=800 --height=600
  node scripts/image-tools.js responsive --sizes=320,640,1024,1920
  `);
}

// Main function
async function main() {
  const startTime = Date.now();

  switch (command) {
    case 'optimize': {
      const options = parseOptions(args.slice(1), defaultConfig);
      console.log('Image Optimization');
      console.log('=================');
      console.log(`Source directory: ${options.sourceDir}`);
      console.log(`Output directory: ${options.outputDir || 'Same as source'}`);
      console.log(`Quality: ${options.quality}`);
      console.log(`WebP only: ${options.webpOnly}`);
      console.log('=================');

      if (!fs.existsSync(options.sourceDir)) {
        console.error(`Error: Source directory ${options.sourceDir} does not exist.`);
        process.exit(1);
      }

      walkDirectory(
        options.sourceDir,
        (filePath) => {
          const ext = path.extname(filePath).toLowerCase();
          if (options.imageExtensions.includes(ext)) {
            optimizeImage(filePath, options);
          } else {
            stats.skipped++;
          }
        },
        options.recursive
      );
      break;
    }

    case 'convert': {
      const options = parseOptions(args.slice(1), defaultConfig);
      console.log('WebP Conversion');
      console.log('===============');
      console.log(`Source directory: ${options.sourceDir}`);
      console.log(`Output directory: ${options.outputDir || 'Same as source'}`);
      console.log(`Quality: ${options.quality}`);
      console.log('===============');

      if (!fs.existsSync(options.sourceDir)) {
        console.error(`Error: Source directory ${options.sourceDir} does not exist.`);
        process.exit(1);
      }

      walkDirectory(
        options.sourceDir,
        (filePath) => {
          const ext = path.extname(filePath).toLowerCase();
          if (options.imageExtensions.includes(ext)) {
            convertToWebP(filePath, options);
          } else {
            stats.skipped++;
          }
        },
        options.recursive
      );
      break;
    }

    case 'resize': {
      const options = parseOptions(args.slice(1), defaultConfig);

      if (!options.width) {
        console.error('Error: Width is required for resize command.');
        console.log('Example: node scripts/image-tools.js resize --width=800 --height=600');
        process.exit(1);
      }

      console.log('Image Resizing');
      console.log('==============');
      console.log(`Source directory: ${options.sourceDir}`);
      console.log(`Output directory: ${options.outputDir || 'Same as source'}`);
      console.log(`Width: ${options.width}`);
      console.log(`Height: ${options.height || 'auto'}`);
      console.log('==============');

      if (!fs.existsSync(options.sourceDir)) {
        console.error(`Error: Source directory ${options.sourceDir} does not exist.`);
        process.exit(1);
      }

      walkDirectory(
        options.sourceDir,
        (filePath) => {
          const ext = path.extname(filePath).toLowerCase();
          if (options.imageExtensions.includes(ext)) {
            resizeImage(filePath, options);
          } else {
            stats.skipped++;
          }
        },
        options.recursive
      );
      break;
    }

    case 'responsive': {
      const options = parseOptions(args.slice(1), defaultConfig);

      // Parse sizes if provided as comma-separated string
      if (typeof options.sizes === 'string') {
        options.sizes = options.sizes.split(',').map(Number);
      }

      console.log('Responsive Image Generation');
      console.log('==========================');
      console.log(`Source directory: ${options.sourceDir}`);
      console.log(`Output directory: ${options.outputDir || 'Same as source'}`);
      console.log(`Sizes: ${options.sizes.join(', ')}`);
      console.log(`Format: ${options.format || 'webp'}`);
      console.log(`Quality: ${options.quality}`);
      console.log('==========================');

      if (!fs.existsSync(options.sourceDir)) {
        console.error(`Error: Source directory ${options.sourceDir} does not exist.`);
        process.exit(1);
      }

      walkDirectory(
        options.sourceDir,
        (filePath) => {
          const ext = path.extname(filePath).toLowerCase();
          if (options.imageExtensions.includes(ext)) {
            generateResponsiveSet(filePath, options);
          } else {
            stats.skipped++;
          }
        },
        options.recursive
      );
      break;
    }

    case 'help':
    default:
      showHelp();
      process.exit(0);
  }

  // Wait for all async operations to complete
  setTimeout(() => {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log('\nOperation Complete');
    console.log('=================');
    console.log(`Processed: ${stats.processed} images`);
    console.log(`WebP conversions: ${stats.webpConverted}`);
    console.log(`Original files optimized: ${stats.originalOptimized}`);
    console.log(`Resized: ${stats.resized}`);
    console.log(`Skipped: ${stats.skipped} files`);
    console.log(`Errors: ${stats.errors}`);

    if (command === 'optimize') {
      console.log(`Total savings: ${stats.totalSavings.toFixed(2)} KB`);
    }

    console.log(`Duration: ${duration.toFixed(2)} seconds`);
  }, 1000);
}

// Run the script
main();
