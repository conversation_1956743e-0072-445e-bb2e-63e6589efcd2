#!/usr/bin/env node
/* eslint-disable no-console */

/**
 * Image Optimization Script using Sharp
 *
 * This script optimizes images in the specified directory:
 * - Compresses JPG, PNG, and WebP images
 * - Converts JPG and PNG to WebP (while keeping originals)
 * - Preserves directory structure
 * - Skips SVG files (already optimized vector graphics)
 *
 * Usage:
 *   node scripts/optimize-images-sharp.js [--sourceDir=public/images] [--quality=80] [--webp-only]
 *
 * Options:
 *   --sourceDir=DIR   Source directory (default: public/images)
 *   --quality=N       Set quality level (1-100, default: 80)
 *   --webp-only       Only create WebP versions without modifying originals
 *   --help            Show this help message
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Parse command line arguments
const args = process.argv.slice(2);
const helpArg = args.find((arg) => arg === '--help');
const webpOnlyArg = args.find((arg) => arg === '--webp-only');
const qualityArg = args.find((arg) => arg.startsWith('--quality='));
const sourceDirArg = args.find((arg) => arg.startsWith('--sourceDir='));

const quality = qualityArg ? parseInt(qualityArg.split('=')[1], 10) : 80;
const sourceDir = sourceDirArg
  ? path.join(process.cwd(), sourceDirArg.split('=')[1])
  : path.join(process.cwd(), 'public', 'images');

// Show help if requested
if (helpArg) {
  console.log(`
Image Optimization Script using Sharp

Optimizes images in the specified directory:
- Compresses JPG, PNG, and WebP images
- Converts JPG and PNG to WebP (while keeping originals)
- Preserves directory structure
- Skips SVG files (already optimized vector graphics)

Usage:
  node scripts/optimize-images-sharp.js [--sourceDir=public/images] [--quality=80] [--webp-only]

Options:
  --sourceDir=DIR   Source directory (default: public/images)
  --quality=N       Set quality level (1-100, default: 80)
  --webp-only       Only create WebP versions without modifying originals
  --help            Show this help message
  `);
  process.exit(0);
}

// Configuration
const config = {
  quality,
  webpOnly: !!webpOnlyArg,
  sourceDir,
  imageExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
};

// Statistics
const stats = {
  processed: 0,
  skipped: 0,
  webpConverted: 0,
  originalOptimized: 0,
  errors: 0,
  totalSavings: 0,
};

// Ensure directory exists
function _ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Get file size in KB
function getFileSizeInKB(filePath) {
  const stats = fs.statSync(filePath);
  return stats.size / 1024;
}

// Process a single image file
async function processImage(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const dir = path.dirname(filePath);
  const baseName = path.basename(filePath, ext);
  const webpPath = path.join(dir, `${baseName}.webp`);
  const _optimizedPath = path.join(dir, `${baseName}_optimized${ext}`);

  try {
    // Skip SVG files
    if (ext === '.svg') {
      console.log(`Skipping SVG: ${filePath}`);
      stats.skipped++;
      return;
    }

    // Get original file size
    const originalSize = getFileSizeInKB(filePath);

    // Create WebP version for all image types
    if (ext === '.jpg' || ext === '.jpeg' || ext === '.png' || ext === '.webp') {
      await sharp(filePath).webp({ quality: config.quality }).toFile(webpPath);

      const webpSize = getFileSizeInKB(webpPath);
      const webpSavings = originalSize - webpSize;

      console.log(`Created WebP: ${webpPath} (saved ${webpSavings.toFixed(2)} KB)`);
      stats.webpConverted++;
      stats.totalSavings += webpSavings;
    }

    // Optimize original files if not in webp-only mode
    if (!config.webpOnly) {
      if (ext === '.jpg' || ext === '.jpeg') {
        // Optimize JPEG
        await sharp(filePath)
          .jpeg({ quality: config.quality, mozjpeg: true })
          .toBuffer()
          .then((buffer) => {
            fs.writeFileSync(filePath, buffer);
          });
      } else if (ext === '.png') {
        // Optimize PNG
        await sharp(filePath)
          .png({ quality: config.quality })
          .toBuffer()
          .then((buffer) => {
            fs.writeFileSync(filePath, buffer);
          });
      } else if (ext === '.webp') {
        // Optimize WebP
        await sharp(filePath)
          .webp({ quality: config.quality })
          .toBuffer()
          .then((buffer) => {
            fs.writeFileSync(filePath, buffer);
          });
      }

      const optimizedSize = getFileSizeInKB(filePath);
      const savings = originalSize - optimizedSize;

      console.log(`Optimized: ${filePath} (saved ${savings.toFixed(2)} KB)`);
      stats.originalOptimized++;
      stats.totalSavings += savings;
    }

    stats.processed++;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    stats.errors++;
  }
}

// Walk through directory recursively
function walkDirectory(dir) {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      walkDirectory(filePath);
    } else if (stat.isFile()) {
      const ext = path.extname(filePath).toLowerCase();
      if (config.imageExtensions.includes(ext)) {
        processImage(filePath);
      } else {
        stats.skipped++;
      }
    }
  });
}

// Main function
async function main() {
  console.log('Image Optimization Script using Sharp');
  console.log('====================================');
  console.log(`Source directory: ${config.sourceDir}`);
  console.log(`Quality level: ${config.quality}`);
  console.log(`Mode: ${config.webpOnly ? 'WebP conversion only' : 'Full optimization'}`);
  console.log('====================================');

  if (!fs.existsSync(config.sourceDir)) {
    console.error(`Error: Source directory ${config.sourceDir} does not exist.`);
    process.exit(1);
  }

  const startTime = Date.now();

  try {
    walkDirectory(config.sourceDir);

    // Wait for async operations to complete
    setTimeout(() => {
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;

      console.log('\nOptimization Complete');
      console.log('====================================');
      console.log(`Processed: ${stats.processed} images`);
      console.log(`WebP conversions: ${stats.webpConverted}`);
      console.log(`Original files optimized: ${stats.originalOptimized}`);
      console.log(`Skipped: ${stats.skipped} files`);
      console.log(`Errors: ${stats.errors}`);
      console.log(`Total savings: ${stats.totalSavings.toFixed(2)} KB`);
      console.log(`Duration: ${duration.toFixed(2)} seconds`);
    }, 1000);
  } catch (error) {
    console.error('Error during optimization:', error);
    process.exit(1);
  }
}

// Run the script
main();
