{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint .", "lint:fix": "eslint . --fix", "param:fix": "npx @next/codemod@canary next-async-request-api .", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "check": "pnpm run lint && pnpm run format:check", "test": "jest --no-cache", "test:watch": "jest --watch", "test:coverage": "jest --coverage --no-cache --testSequencer", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:e2e:chromium": "playwright test --project=chromium", "test:e2e:firefox": "playwright test --project=firefox", "test:e2e:webkit": "INCLUDE_WEBKIT_TESTS=true playwright test --project=webkit", "test:e2e:mobile-chrome": "playwright test --project=\"Mobile Chrome\"", "test:e2e:mobile-safari": "INCLUDE_WEBKIT_TESTS=true playwright test --project=\"Mobile Safari\"", "test:e2e:install-deps": "playwright install-deps", "watch": "concurrently \"pnpm run lint -- --watch\" \"pnpm run type-check -- --watch\"", "optimize-images": "node scripts/optimize-images.js", "optimize-images-sharp": "node scripts/optimize-images-sharp.js", "optimize-large-webp": "node scripts/optimize-large-webp.js", "image-tools": "node scripts/image-tools.js", "delete-originals": "node scripts/delete-originals.js", "optimize-all": "node scripts/optimize-images-sharp.js && node scripts/optimize-large-webp.js --quality=60 --resize=1200x800 --sizeThreshold=100 && node scripts/delete-originals.js", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "latest", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@tanstack/react-query": "^5.76.0", "@tanstack/react-query-devtools": "^5.76.0", "@types/js-cookie": "^3.0.6", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "js-cookie": "^3.0.5", "libphonenumber-js": "^1.12.8", "lucide-react": "^0.454.0", "mixpanel-browser": "^2.64.0", "next": "15.3.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-country-flag": "^3.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-error-boundary": "latest", "react-hook-form": "latest", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.9", "remark-gfm": "^4.0.1", "scheduler": "^0.25.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "typescript": "latest", "zod": "latest", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/js": "^9.26.0", "@playwright/test": "^1.52.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/mixpanel-browser": "^2.60.0", "@types/node": "^22.15.17", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.4", "@types/react-portal": "^4.0.7", "@types/scheduler": "^0.23.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "babel-jest": "^29.7.0", "concurrently": "^9.1.2", "dotenv": "^16.5.0", "eslint": "^9.26.0", "eslint-config-next": "^15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-next": "^0.0.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-testing-library": "^7.1.1", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-fetch-mock": "^3.0.3", "lint-staged": "^15.5.2", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5", "typescript-eslint": "^8.32.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}}