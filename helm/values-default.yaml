---
global:
  application: ecommerce-frontend
  namespace: default
  version: 1
  env: default

  image:
    repository: ************.dkr.ecr.us-east-1.amazonaws.com/ecommerce-frontend
    tag: latest

  serviceaccount:
    enabled: true

  vault:
    enabled: true
    env: default

  revisionHistoryLimit: 4

deployments:
  web:
    deployment:
      enabled: false
  frontend:
    name: ecommerce-frontend
    deployment:
      enabled: true
      useRedisBridge: false
      resources:
        requests:
          memory: 50Mi
          cpu: 50m
        limits:
          memory: 200Mi
          cpu: 200m
      probes:
        enabled: true
        path: /api/health
        initialDelaySeconds: 30
        timeoutSeconds: 15
        failureThreshold: 3
        periodSeconds: 60
      dnsConfig:
        enabled: true
        options:
          - name: ndots
            value: '2'
    hpa:
      enabled: true
      minReplicas: 1
      maxReplicas: 1

    servers:
      http:
        - name: http
          port: 80
          protocol: HTTP
      https:
        - name: https
          port: 443
          protocol: HTTP

    monitoring:
      enabled: false

    service:
      targetPort: 3000
      port: 80

    gwPrivate:
      enabled: false

    gwPublic:
      enabled: true

    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
                - key: role
                  operator: In
                  values: ['app']

    tolerations:
      - effect: NoSchedule
        key: dedicated
        value: app

migrationjob:
  enabled: false
