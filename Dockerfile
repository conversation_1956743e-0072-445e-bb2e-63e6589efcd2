# Build stage
FROM node:21-alpine AS builder

# Set working directory
WORKDIR /app

# Install pnpm and other required tools
RUN apk add --no-cache libc6-compat
RUN corepack enable && corepack prepare pnpm@latest --activate

# Copy package.json first
COPY package.json ./

# Copy the rest of the source code
COPY . .

# Ensure global.d.ts is properly copied
COPY global.d.ts ./global.d.ts

# Clean any existing lockfile and node_modules to ensure a fresh install
RUN rm -f pnpm-lock.yaml && rm -rf node_modules

# Install dependencies to regenerate the lockfile
RUN pnpm install

# Set timezone to America/Sao_Paulo
RUN apk add --no-cache tzdata
RUN ln -sf /usr/share/zoneinfo/America/Sao_Paulo /etc/localtime
ENV TZ="America/Sao_Paulo"

# Environment variables are required at build time
ARG NEXT_PRIVATE_API_BASE_URL
ARG NEXT_PUBLIC_FOOTER_RATING_IMAGE_URL
ARG NEXT_PUBLIC_YOUTUBE_URL
ARG NEXT_PUBLIC_INSTAGRAM_URL
ARG NEXT_PUBLIC_TIKTOK_URL
ARG NEXT_PUBLIC_LOGO_PATH
ARG NEXT_PUBLIC_GETNINJAS_URL
ARG NEXT_PUBLIC_META_PIXEL_ID
ARG NEXT_PUBLIC_GA4_ID
ARG NEXT_PUBLIC_GTM_ID
ARG NEXT_PUBLIC_ANALYTICS_ENVIRONMENT
ARG NEXT_PUBLIC_MIXPANEL_TOKEN

# Build application with increased memory limit
ENV NODE_OPTIONS="--max-old-space-size=8192"
# Skip image optimization during Docker build
ENV SKIP_IMAGE_OPTIMIZATION="true"
RUN pnpm build || (echo "Build failed, check logs above for details" && exit 1)

# Production stage
FROM node:21-alpine AS runner

# Set working directory
WORKDIR /app

# Install production dependencies only
RUN apk add --no-cache libc6-compat
COPY --from=builder /app/package.json /app/pnpm-lock.yaml /app/entrypoint.sh ./
RUN corepack enable
RUN corepack prepare pnpm@latest --activate
# HUSKY=0 prevents the prepare script from running, --ignore-scripts skips build scripts like sharp
RUN HUSKY=0 pnpm install --prod --frozen-lockfile --ignore-scripts

# Set timezone to America/Sao_Paulo
RUN apk add --no-cache tzdata
RUN ln -sf /usr/share/zoneinfo/America/Sao_Paulo /etc/localtime
ENV TZ="America/Sao_Paulo"

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
RUN chown nextjs:nodejs /app -Rf
USER nextjs

# Copy built assets from builder
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs --chmod=770 /app/entrypoint.sh ./

# Set runtime environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV TZ="America/Sao_Paulo"

# Environment variables are also required at runtime
ENV NEXT_PRIVATE_API_BASE_URL=${NEXT_PRIVATE_API_BASE_URL}
ENV NEXT_PUBLIC_FOOTER_RATING_IMAGE_URL=${NEXT_PUBLIC_FOOTER_RATING_IMAGE_URL}
ENV NEXT_PUBLIC_YOUTUBE_URL=${NEXT_PUBLIC_YOUTUBE_URL}
ENV NEXT_PUBLIC_INSTAGRAM_URL=${NEXT_PUBLIC_INSTAGRAM_URL}
ENV NEXT_PUBLIC_TIKTOK_URL=${NEXT_PUBLIC_TIKTOK_URL}
ENV NEXT_PUBLIC_LOGO_PATH=${NEXT_PUBLIC_LOGO_PATH}
ENV NEXT_PUBLIC_GETNINJAS_URL=${NEXT_PUBLIC_GETNINJAS_URL}
ENV NEXT_PUBLIC_META_PIXEL_ID=${NEXT_PUBLIC_META_PIXEL_ID}
ENV NEXT_PUBLIC_GA4_ID=${NEXT_PUBLIC_GA4_ID}
ENV NEXT_PUBLIC_GTM_ID=${NEXT_PUBLIC_GTM_ID}
ENV NEXT_PUBLIC_ANALYTICS_ENVIRONMENT=${NEXT_PUBLIC_ANALYTICS_ENVIRONMENT}
ENV NEXT_PUBLIC_MIXPANEL_TOKEN=${NEXT_PUBLIC_MIXPANEL_TOKEN}

# Expose port
EXPOSE 3000

# Start the application
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["node", "server.js"]