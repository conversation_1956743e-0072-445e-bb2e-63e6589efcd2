'use client';

import { debounce } from '@/src/app/_utils/debounce';
import { useCallback } from 'react';
import { Path, UseFormReturn } from 'react-hook-form';

interface UseDebouncedValidationOptions {
  delay?: number;
}

/**
 * Hook for debounced form field validation
 * @param form The form returned by useForm
 * @param options Configuration options
 * @returns An object with validation functions
 */
export function useDebouncedValidation<T extends Record<string, unknown>>(
  form: UseFormReturn<T>,
  { delay = 300 }: UseDebouncedValidationOptions = {}
) {
  // Debounced validation for single field
  const validateField = useCallback(() => {
    return debounce((fieldName: Path<T>) => {
      form.trigger(fieldName);
    }, delay);
  }, [form, delay]);

  // Immediate validation for a field (no debounce)
  const validateFieldImmediate = useCallback(
    (fieldName: Path<T>) => {
      form.trigger(fieldName);
    },
    [form]
  );

  // Validate multiple fields at once with debounce
  const validateFields = useCallback(() => {
    return debounce((fieldNames: Path<T>[]) => {
      fieldNames.forEach((fieldName) => {
        form.trigger(fieldName);
      });
    }, delay);
  }, [form, delay]);

  // Check if specific field has error
  const hasError = useCallback(
    (fieldName: Path<T>) => {
      return !!form.formState.errors[fieldName];
    },
    [form.formState.errors]
  );

  return {
    validateField,
    validateFieldImmediate,
    validateFields,
    hasError,
  };
}
