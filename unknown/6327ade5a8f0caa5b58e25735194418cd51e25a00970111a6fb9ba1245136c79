import { useOrderData } from '@/src/app/_hooks/useOrderData';
import { ApiOrderResponse } from '@/src/app/_interfaces';
import { OrderService } from '@/src/app/_services/order';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook, waitFor } from '@testing-library/react';
import React from 'react';

// Mock the OrderService
jest.mock('@/src/app/_services/order', () => ({
  OrderService: {
    getOrderByUuid: jest.fn(),
  },
}));

// Mock sessionStorage
const mockSessionStorage = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage,
});

// Create a wrapper with QueryClient for testing
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
      },
    },
  });

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={createTestQueryClient()}>{children}</QueryClientProvider>
);

describe('useOrderData Hook (Integration Tests)', () => {
  // This variable will hold our real API data
  let realOrderData: ApiOrderResponse | null = null;

  // Create a mock order response
  const createMockOrderResponse = (): ApiOrderResponse => ({
    uuid: 'test-uuid',
    orderId: 'ORDER123',
    status: 'completed',
    createdAt: '2023-01-01T12:00:00Z',
    service: {
      id: 1,
      slug: 'test-service',
      name: 'Test Service',
      description: 'Description for Test Service',
      imageUrl: '/images/test-service.jpg',
      status: 'active',
      price: {
        priceId: 1,
        originalPrice: 150,
        discountPrice: 30,
        finalPrice: 120,
      },
      provider: {
        id: 1,
        name: 'Test Provider',
        imageUrl: '/provider-image.jpg',
        providerUrl: '/provider/1',
        description: 'Provider description',
      },
      availableIn: ['São Paulo', 'Rio de Janeiro'],
      details: ['Detail 1', 'Detail 2'],
      serviceLimits: 'Service limits information',
      keywords: ['test', 'service'],
      categoryName: 'Test Category',
      categorySlug: 'test-category',
      subcategoryName: 'Test Subcategory',
      subcategorySlug: 'test-subcategory',
      termsConditionsUrl: '/terms',
      preparations: 'Preparation 1, Preparation 2',
    },
    customer: {
      customerId: 'CUST123',
      fullName: 'Test User',
      phone: '11999999999',
      email: '<EMAIL>',
      document: '123.456.789-00',
    },
    address: {
      numberAd: '123',
      street: 'Test Street',
      neighborhood: 'Test Neighborhood',
      cityName: 'Test City',
      uf: 'SP',
      zipCode: '01234-567',
      complement: 'Apt 456',
    },
    schedule: {
      date: '2023-12-31',
      period: 'morning',
    },
    payment: {
      method: 'credit_card',
      totalPaid: 120,
    },
  });

  beforeEach(() => {
    // Reset mocks between tests
    jest.clearAllMocks();
    mockSessionStorage.clear();
  });

  it('fetches order data with real order ID', async () => {
    // Create mock order data
    realOrderData = createMockOrderResponse();

    // Mock the OrderService to return our mock data
    OrderService.getOrderByUuid.mockResolvedValue(realOrderData);

    // Render the hook with an order ID
    const { result } = renderHook(() => useOrderData({ orderId: 'test-uuid' }), { wrapper });

    // Initially, it should be loading
    expect(result.current.isLoading).toBe(true);

    // Wait for the data to be loaded
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Check that the hook returns the correct data
    expect(result.current.orderData).toEqual(realOrderData);
    expect(result.current.error).toBeNull();
    // The hook doesn't return isError property according to the interface
    // expect(result.current.isError).toBe(false);

    // Verify OrderService was called with the correct ID
    expect(OrderService.getOrderByUuid).toHaveBeenCalledWith('test-uuid');
  });

  it('retrieves order ID from sessionStorage if not provided', async () => {
    // Create mock order data
    realOrderData = createMockOrderResponse();

    // Set the order ID in sessionStorage
    mockSessionStorage.setItem('lastOrderId', 'stored-uuid');

    // Mock the OrderService to return our mock data
    OrderService.getOrderByUuid.mockResolvedValue(realOrderData);

    // Render the hook without an order ID
    const { result } = renderHook(() => useOrderData({ orderId: null }), { wrapper });

    // Wait for the data to be loaded
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Check that the hook returns the correct data
    expect(result.current.orderData).toEqual(realOrderData);

    // Verify OrderService was called with the ID from sessionStorage
    expect(OrderService.getOrderByUuid).toHaveBeenCalledWith('stored-uuid');
  });

  it('handles API errors gracefully', async () => {
    // Mock the OrderService to simulate an error
    OrderService.getOrderByUuid.mockRejectedValue(new Error('API error'));

    // Render the hook with an order ID and no retries to fail immediately
    const { result } = renderHook(() => useOrderData({ orderId: 'error-uuid', maxRetries: 0 }), {
      wrapper,
    });

    // Wait for the error to be handled - use a longer timeout
    await waitFor(
      () => {
        // Check that the hook handles the error correctly
        expect(result.current.orderData).toBeNull();
        expect(result.current.error).not.toBeNull();
      },
      { timeout: 2000 }
    );
  });

  it('retries failed requests', async () => {
    // Mock the OrderService to fail once then succeed
    OrderService.getOrderByUuid
      .mockRejectedValueOnce(new Error('Temporary error'))
      .mockResolvedValueOnce(createMockOrderResponse());

    // Render the hook with an order ID and custom retry settings
    const { result } = renderHook(
      () =>
        useOrderData({ orderId: 'retry-uuid', maxRetries: 1, retryDelay: 100, initialDelay: 50 }),
      { wrapper }
    );

    // Initially, it should be loading
    expect(result.current.isLoading).toBe(true);

    // Wait for the retry to succeed
    await waitFor(
      () => {
        expect(result.current.isLoading).toBe(false);
        expect(result.current.orderData).not.toBeNull();
      },
      { timeout: 500 }
    );

    // Verify OrderService was called twice (initial failure + successful retry)
    expect(OrderService.getOrderByUuid).toHaveBeenCalledTimes(2);
  });

  it('returns null when no order ID is available', async () => {
    // Clear sessionStorage to ensure no stored order ID
    mockSessionStorage.clear();

    // Mock the console.error to prevent error output
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Render the hook without an order ID and with empty sessionStorage
    const { result } = renderHook(() => useOrderData({ orderId: null, maxRetries: 0 }), {
      wrapper,
    });

    // Wait for any async operations to complete
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Check that the hook returns null data when no order ID is available
    expect(result.current.orderData).toBeNull();

    // Restore console.error
    console.error = originalConsoleError;
  });
});
