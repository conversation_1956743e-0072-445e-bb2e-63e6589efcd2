import { useCheckoutForm } from '@/src/app/_hooks/useCheckoutForm';
import { act, renderHook } from '@testing-library/react';

// Mocking the imported modules
jest.mock('react-hook-form', () => ({
  useForm: () => ({
    getValues: () => ({
      firstName: 'John',
      lastName: 'Doe',
      date: new Date(),
      period: 'morning',
      cep: '12345678',
      street: 'Test Street',
      streetNumber: '123',
      neighborhood: 'Test Neighborhood',
      city: 'Test City',
      state: 'TS',
      phone: '999999999',
      cpf: '12345678900',
      terms: true,
      countryCode: '+55',
      email: '<EMAIL>',
      complement: 'Apt 101',
    }),
    formState: {
      isValid: true,
    },
    trigger: jest.fn(),
  }),
}));

jest.mock('@hookform/resolvers/zod', () => ({
  zodResolver: jest.fn(),
}));

jest.mock('@/src/app/_utils/debounce', () => ({
  debounce: (fn: any) => fn,
}));

describe('useCheckoutForm', () => {
  it('should initialize with default state', () => {
    // Act
    const { result } = renderHook(() => useCheckoutForm());

    // Assert
    expect(result.current.focusedBlock).toBeNull();
    expect(result.current.form).toBeDefined();
  });

  it('should update focusedBlock state', () => {
    // Act
    const { result } = renderHook(() => useCheckoutForm());

    act(() => {
      result.current.setFocusedBlock('personal-info');
    });

    // Assert
    expect(result.current.focusedBlock).toBe('personal-info');
  });

  it('should provide debouncedValidation function', () => {
    // Act
    const { result } = renderHook(() => useCheckoutForm());

    // Assert
    expect(typeof result.current.debouncedValidation).toBe('function');

    // Verify it returns a function
    const validationFn = result.current.debouncedValidation();
    expect(typeof validationFn).toBe('function');

    // Test the returned function
    act(() => {
      validationFn('firstName');
    });

    // The mock implementation should have called trigger
    expect(result.current.form.trigger).toHaveBeenCalledWith('firstName');
  });

  it('should provide isFormValid function', () => {
    // Act
    const { result } = renderHook(() => useCheckoutForm());

    // Assert
    expect(typeof result.current.isFormValid).toBe('function');

    // Verify function works
    const isValid = result.current.isFormValid();
    expect(isValid).toBe(true);
  });
});
