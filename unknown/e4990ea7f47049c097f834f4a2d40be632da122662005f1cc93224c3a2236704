import axios from 'axios';
import { OrderService } from '../../app/_services/order';
import { axiosInstance, formatCPF, formatPhoneNumber } from '../../app/_utils';
import { orderApiResponses } from '../fixtures/orderResponses';

// Mock the external dependencies
jest.mock('../../app/_utils', () => ({
  axiosInstance: {
    get: jest.fn(),
  },
  formatCPF: jest.fn((cpf) => `formatted-${cpf}`),
  formatPhoneNumber: jest.fn((phone, prefix) => `${prefix}-formatted-${phone}`),
}));

jest.mock('axios', () => {
  const mockAxios = {
    isAxiosError: jest.fn(),
  };
  return {
    ...mockAxios,
    __esModule: true,
    default: mockAxios,
  };
});

describe('OrderService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.error = jest.fn();
  });

  describe('getOrderByUuid', () => {
    it('should fetch and transform order data correctly', async () => {
      // Mock the API response
      const mockApiResponse = {
        service: {
          id: 123,
          slug: 'test-service',
          name: 'Test Service',
          description: 'Service description',
          imageUrl: 'https://example.com/service.jpg',
          status: 'active',
          preparations: 'Preparation instructions',
          termsConditionsUrl: 'https://example.com/terms',
          availableIn: ['region1', 'region2'],
          details: ['detail1', 'detail2'],
          serviceLimits: 'Service limits',
          keywords: ['keyword1', 'keyword2'],
          price: {
            priceId: 456,
            originalPrice: 100,
            discountPrice: 80,
            finalPrice: 80,
          },
          provider: {
            id: 789,
            name: 'Test Provider',
            imageUrl: 'https://example.com/provider.jpg',
            providerUrl: 'https://example.com/provider',
            description: 'Provider description',
          },
        },
        appointment: {
          date: '2023-05-01',
          period: 'MANHA',
        },
        customer: {
          customerId: 'cust-123',
          fullName: 'John Doe',
          phone: '11987654321',
          email: '<EMAIL>',
          document: '12345678900',
        },
        address: {
          numberAd: '42',
          street: 'Test Street',
          neighborhood: 'Test Neighborhood',
          cityName: 'Test City',
          uf: 'TS',
          zipCode: '12345678',
          complement: 'Apt 123',
        },
        payment: {
          method: 'credit_card',
          totalPaid: 80,
        },
      };

      // Mock the API call
      (axiosInstance.get as jest.Mock).mockResolvedValueOnce({ data: mockApiResponse });

      // Call the service
      const result = await OrderService.getOrderByUuid('order-123');

      // Check API call
      expect(axiosInstance.get).toHaveBeenCalledWith('/api/order/by-uuid', {
        params: { uuid: 'order-123' },
      });

      // Check data transformation
      expect(formatCPF).toHaveBeenCalledWith('12345678900');
      expect(formatPhoneNumber).toHaveBeenCalledWith('11987654321', '+55');

      // Check result
      expect(result).toEqual({
        service: {
          id: 123,
          slug: 'test-service',
          name: 'Test Service',
          description: 'Service description',
          imageUrl: 'https://example.com/service.jpg',
          status: 'active',
          preparations: 'Preparation instructions',
          termsConditionsUrl: 'https://example.com/terms',
          availableIn: ['region1', 'region2'],
          details: ['detail1', 'detail2'],
          serviceLimits: 'Service limits',
          keywords: ['keyword1', 'keyword2'],
          price: {
            priceId: 456,
            originalPrice: 100,
            discountPrice: 80,
            finalPrice: 80,
          },
          provider: {
            id: 789,
            name: 'Test Provider',
            imageUrl: 'https://example.com/provider.jpg',
            providerUrl: 'https://example.com/provider',
            description: 'Provider description',
          },
        },
        appointment: {
          date: '2023-05-01',
          period: 'morning', // Transformed from MANHA to morning
        },
        customer: {
          customerId: 'cust-123',
          fullName: 'John Doe',
          phone: '+55-formatted-11987654321', // Formatted by the mock function
          email: '<EMAIL>',
          document: 'formatted-12345678900', // Formatted by the mock function
        },
        address: {
          numberAd: '42',
          street: 'Test Street',
          neighborhood: 'Test Neighborhood',
          cityName: 'Test City',
          uf: 'TS',
          zipCode: '12345678',
          complement: 'Apt 123',
        },
        payment: {
          method: 'credit_card',
          totalPaid: 80,
        },
      });
    });

    it('should process a real-like order API response correctly (snapshot test)', async () => {
      // Use fixture data that resembles a real API response
      const realOrderData = orderApiResponses.orderByUuid;

      // Mock the API call with snapshot data
      (axiosInstance.get as jest.Mock).mockResolvedValueOnce({ data: realOrderData });

      // Call the service
      const result = await OrderService.getOrderByUuid('order-real-123');

      // Check API call
      expect(axiosInstance.get).toHaveBeenCalledWith('/api/order/by-uuid', {
        params: { uuid: 'order-real-123' },
      });

      // Verify formatting functions were called with correct data
      expect(formatCPF).toHaveBeenCalledWith('12345678900');
      expect(formatPhoneNumber).toHaveBeenCalledWith('11987654321', '+55');

      // Verify period was correctly transformed
      expect(result.appointment.period).toBe('morning');

      // Verify customer info was correctly formatted
      expect(result.customer.document).toBe('formatted-12345678900');
      expect(result.customer.phone).toBe('+55-formatted-11987654321');

      // Verify service data was preserved
      expect(result.service.name).toBe('Plumbing Service');
      expect(result.service.price.finalPrice).toBe(129.99);

      // Verify payment info was preserved
      expect(result.payment.method).toBe('credit_card');
      expect(result.payment.totalPaid).toBe(129.99);
    });

    it('should transform "TARDE" period to "afternoon"', async () => {
      // Use fixture with afternoon appointment
      const afternoonOrderData = orderApiResponses.orderWithAfternoonAppointment;

      // Mock the API call
      (axiosInstance.get as jest.Mock).mockResolvedValueOnce({ data: afternoonOrderData });

      const result = await OrderService.getOrderByUuid('order-afternoon');

      // Check that TARDE was transformed to afternoon
      expect(result.appointment.period).toBe('afternoon');

      // Verify other key transformed fields
      expect(result.service.name).toBe('Electrical Repair');
      expect(result.customer.fullName).toBe('Maria Oliveira');
      expect(result.payment.method).toBe('pix');
    });

    it('should handle missing array fields gracefully', async () => {
      // Mock API response with missing array fields
      const mockApiResponse = {
        service: {
          id: 123,
          slug: 'test-service',
          name: 'Test Service',
          description: 'Service description',
          imageUrl: 'https://example.com/service.jpg',
          status: 'active',
          preparations: 'Preparation instructions',
          termsConditionsUrl: 'https://example.com/terms',
          // Missing availableIn, details, and keywords
          serviceLimits: 'Service limits',
          price: {
            priceId: 456,
            originalPrice: 100,
            discountPrice: 80,
            finalPrice: 80,
          },
          provider: {
            id: 789,
            name: 'Test Provider',
            imageUrl: 'https://example.com/provider.jpg',
            providerUrl: 'https://example.com/provider',
            description: 'Provider description',
          },
        },
        appointment: {
          date: '2023-05-01',
          period: 'MANHA',
        },
        customer: {
          customerId: 'cust-123',
          fullName: 'John Doe',
          phone: '11987654321',
          email: '<EMAIL>',
          document: '12345678900',
        },
        address: {
          numberAd: '42',
          street: 'Test Street',
          neighborhood: 'Test Neighborhood',
          cityName: 'Test City',
          uf: 'TS',
          zipCode: '12345678',
          complement: 'Apt 123',
        },
        payment: {
          method: 'credit_card',
          totalPaid: 80,
        },
      };

      (axiosInstance.get as jest.Mock).mockResolvedValueOnce({ data: mockApiResponse });

      const result = await OrderService.getOrderByUuid('order-123');

      // Check that empty arrays were provided as fallbacks
      expect(result.service.availableIn).toEqual([]);
      expect(result.service.details).toEqual([]);
      expect(result.service.keywords).toEqual([]);
    });

    it('should handle Axios errors correctly', async () => {
      // Use fixture error response
      const errorResponse = orderApiResponses.errorResponse;

      // Create mock Axios error
      const axiosError = new Error('API error') as any;
      axiosError.response = {
        statusText: 'Not Found',
        status: errorResponse.status,
        data: errorResponse,
      };

      // Mock Axios error response
      (axiosInstance.get as jest.Mock).mockRejectedValueOnce(axiosError);
      (axios.isAxiosError as unknown as jest.Mock).mockReturnValueOnce(true);

      await expect(OrderService.getOrderByUuid('invalid-id')).rejects.toThrow(
        'Failed to fetch order: Not Found'
      );

      expect(console.error).toHaveBeenCalled();
    });

    it('should handle generic errors correctly', async () => {
      // Create generic error
      const error = new Error('Generic error');

      // Mock error response
      (axiosInstance.get as jest.Mock).mockRejectedValueOnce(error);
      (axios.isAxiosError as unknown as jest.Mock).mockReturnValueOnce(false);

      await expect(OrderService.getOrderByUuid('invalid-id')).rejects.toThrow('Generic error');

      expect(console.error).toHaveBeenCalled();
    });

    it('should handle string details in service data', async () => {
      // Mock API response with string details
      const mockApiResponse = {
        service: {
          id: 123,
          slug: 'test-service',
          name: 'Test Service',
          description: 'Service description',
          imageUrl: 'https://example.com/service.jpg',
          status: 'active',
          preparations: 'Preparation instructions',
          termsConditionsUrl: 'https://example.com/terms',
          availableIn: ['region1', 'region2'],
          details: 'Detail 1\nDetail 2\n\nDetail 3', // String details with line breaks
          serviceLimits: 'Service limits',
          keywords: ['keyword1', 'keyword2'],
          price: {
            priceId: 456,
            originalPrice: 100,
            discountPrice: 80,
            finalPrice: 80,
          },
          provider: {
            id: 789,
            name: 'Test Provider',
            imageUrl: 'https://example.com/provider.jpg',
            providerUrl: 'https://example.com/provider',
            description: 'Provider description',
          },
        },
        appointment: {
          date: '2023-05-01',
          period: 'MANHA',
        },
        customer: {
          customerId: 'cust-123',
          fullName: 'John Doe',
          phone: '11987654321',
          email: '<EMAIL>',
          document: '12345678900',
        },
        address: {
          numberAd: '42',
          street: 'Test Street',
          neighborhood: 'Test Neighborhood',
          cityName: 'Test City',
          uf: 'TS',
          zipCode: '12345678',
          complement: 'Apt 123',
        },
        payment: {
          method: 'credit_card',
          totalPaid: 80,
        },
      };

      // Mock the API call
      (axiosInstance.get as jest.Mock).mockResolvedValueOnce({ data: mockApiResponse });

      // Call the service
      const result = await OrderService.getOrderByUuid('order-123');

      // Check that details were split into an array
      expect(Array.isArray(result.service.details)).toBe(true);
      expect(result.service.details).toEqual(['Detail 1', 'Detail 2', 'Detail 3']);
    });

    it('should handle array details in service data', async () => {
      // Mock API response with array details
      const mockApiResponse = {
        service: {
          id: 123,
          slug: 'test-service',
          name: 'Test Service',
          description: 'Service description',
          imageUrl: 'https://example.com/service.jpg',
          status: 'active',
          preparations: 'Preparation instructions',
          termsConditionsUrl: 'https://example.com/terms',
          availableIn: ['region1', 'region2'],
          details: ['Detail 1', 'Detail 2', 'Detail 3'], // Array details
          serviceLimits: 'Service limits',
          keywords: ['keyword1', 'keyword2'],
          price: {
            priceId: 456,
            originalPrice: 100,
            discountPrice: 80,
            finalPrice: 80,
          },
          provider: {
            id: 789,
            name: 'Test Provider',
            imageUrl: 'https://example.com/provider.jpg',
            providerUrl: 'https://example.com/provider',
            description: 'Provider description',
          },
        },
        appointment: {
          date: '2023-05-01',
          period: 'MANHA',
        },
        customer: {
          customerId: 'cust-123',
          fullName: 'John Doe',
          phone: '11987654321',
          email: '<EMAIL>',
          document: '12345678900',
        },
        address: {
          numberAd: '42',
          street: 'Test Street',
          neighborhood: 'Test Neighborhood',
          cityName: 'Test City',
          uf: 'TS',
          zipCode: '12345678',
          complement: 'Apt 123',
        },
        payment: {
          method: 'credit_card',
          totalPaid: 80,
        },
      };

      // Mock the API call
      (axiosInstance.get as jest.Mock).mockResolvedValueOnce({ data: mockApiResponse });

      // Call the service
      const result = await OrderService.getOrderByUuid('order-123');

      // Check that details array is preserved
      expect(Array.isArray(result.service.details)).toBe(true);
      expect(result.service.details).toEqual(['Detail 1', 'Detail 2', 'Detail 3']);
    });

    it('should handle null details in service data', async () => {
      // Mock API response with null details
      const mockApiResponse = {
        service: {
          id: 123,
          slug: 'test-service',
          name: 'Test Service',
          description: 'Service description',
          imageUrl: 'https://example.com/service.jpg',
          status: 'active',
          preparations: 'Preparation instructions',
          termsConditionsUrl: 'https://example.com/terms',
          availableIn: ['region1', 'region2'],
          details: null, // Null details
          serviceLimits: 'Service limits',
          keywords: ['keyword1', 'keyword2'],
          price: {
            priceId: 456,
            originalPrice: 100,
            discountPrice: 80,
            finalPrice: 80,
          },
          provider: {
            id: 789,
            name: 'Test Provider',
            imageUrl: 'https://example.com/provider.jpg',
            providerUrl: 'https://example.com/provider',
            description: 'Provider description',
          },
        },
        appointment: {
          date: '2023-05-01',
          period: 'MANHA',
        },
        customer: {
          customerId: 'cust-123',
          fullName: 'John Doe',
          phone: '11987654321',
          email: '<EMAIL>',
          document: '12345678900',
        },
        address: {
          numberAd: '42',
          street: 'Test Street',
          neighborhood: 'Test Neighborhood',
          cityName: 'Test City',
          uf: 'TS',
          zipCode: '12345678',
          complement: 'Apt 123',
        },
        payment: {
          method: 'credit_card',
          totalPaid: 80,
        },
      };

      // Mock the API call
      (axiosInstance.get as jest.Mock).mockResolvedValueOnce({ data: mockApiResponse });

      // Call the service
      const result = await OrderService.getOrderByUuid('order-123');

      // Check that details is an empty array
      expect(Array.isArray(result.service.details)).toBe(true);
      expect(result.service.details).toEqual([]);
    });

    it('should handle undefined details in service data', async () => {
      // Mock API response with undefined details
      const mockApiResponse = {
        service: {
          id: 123,
          slug: 'test-service',
          name: 'Test Service',
          description: 'Service description',
          imageUrl: 'https://example.com/service.jpg',
          status: 'active',
          preparations: 'Preparation instructions',
          termsConditionsUrl: 'https://example.com/terms',
          availableIn: ['region1', 'region2'],
          // details is undefined
          serviceLimits: 'Service limits',
          keywords: ['keyword1', 'keyword2'],
          price: {
            priceId: 456,
            originalPrice: 100,
            discountPrice: 80,
            finalPrice: 80,
          },
          provider: {
            id: 789,
            name: 'Test Provider',
            imageUrl: 'https://example.com/provider.jpg',
            providerUrl: 'https://example.com/provider',
            description: 'Provider description',
          },
        },
        appointment: {
          date: '2023-05-01',
          period: 'MANHA',
        },
        customer: {
          customerId: 'cust-123',
          fullName: 'John Doe',
          phone: '11987654321',
          email: '<EMAIL>',
          document: '12345678900',
        },
        address: {
          numberAd: '42',
          street: 'Test Street',
          neighborhood: 'Test Neighborhood',
          cityName: 'Test City',
          uf: 'TS',
          zipCode: '12345678',
          complement: 'Apt 123',
        },
        payment: {
          method: 'credit_card',
          totalPaid: 80,
        },
      };

      // Mock the API call
      (axiosInstance.get as jest.Mock).mockResolvedValueOnce({ data: mockApiResponse });

      // Call the service
      const result = await OrderService.getOrderByUuid('order-123');

      // Check that details is an empty array
      expect(Array.isArray(result.service.details)).toBe(true);
      expect(result.service.details).toEqual([]);
    });

    it('should handle empty string details in service data', async () => {
      // Mock API response with empty string details
      const mockApiResponse = {
        service: {
          id: 123,
          slug: 'test-service',
          name: 'Test Service',
          description: 'Service description',
          imageUrl: 'https://example.com/service.jpg',
          status: 'active',
          preparations: 'Preparation instructions',
          termsConditionsUrl: 'https://example.com/terms',
          availableIn: ['region1', 'region2'],
          details: '', // Empty string details
          serviceLimits: 'Service limits',
          keywords: ['keyword1', 'keyword2'],
          price: {
            priceId: 456,
            originalPrice: 100,
            discountPrice: 80,
            finalPrice: 80,
          },
          provider: {
            id: 789,
            name: 'Test Provider',
            imageUrl: 'https://example.com/provider.jpg',
            providerUrl: 'https://example.com/provider',
            description: 'Provider description',
          },
        },
        appointment: {
          date: '2023-05-01',
          period: 'MANHA',
        },
        customer: {
          customerId: 'cust-123',
          fullName: 'John Doe',
          phone: '11987654321',
          email: '<EMAIL>',
          document: '12345678900',
        },
        address: {
          numberAd: '42',
          street: 'Test Street',
          neighborhood: 'Test Neighborhood',
          cityName: 'Test City',
          uf: 'TS',
          zipCode: '12345678',
          complement: 'Apt 123',
        },
        payment: {
          method: 'credit_card',
          totalPaid: 80,
        },
      };

      // Mock the API call
      (axiosInstance.get as jest.Mock).mockResolvedValueOnce({ data: mockApiResponse });

      // Call the service
      const result = await OrderService.getOrderByUuid('order-123');

      // Check that details is an empty array
      expect(Array.isArray(result.service.details)).toBe(true);
      expect(result.service.details).toEqual([]);
    });

    it('should handle Axios error without response data', async () => {
      // Create mock Axios error without response data
      const axiosError = new Error('API error') as any;
      axiosError.response = {
        statusText: 'Not Found',
        status: 404,
        // No data property
      };

      // Mock Axios error response
      (axiosInstance.get as jest.Mock).mockRejectedValueOnce(axiosError);
      (axios.isAxiosError as unknown as jest.Mock).mockReturnValueOnce(true);

      await expect(OrderService.getOrderByUuid('invalid-id')).rejects.toThrow(
        'Failed to fetch order: Not Found'
      );

      expect(console.error).toHaveBeenCalled();
    });

    it('should handle Axios error without statusText', async () => {
      // Create mock Axios error without statusText
      const axiosError = new Error('API error') as any;
      axiosError.response = {
        status: 500,
        data: { message: 'Internal Server Error' },
        // No statusText property
      };

      // Mock Axios error response
      (axiosInstance.get as jest.Mock).mockRejectedValueOnce(axiosError);
      (axios.isAxiosError as unknown as jest.Mock).mockReturnValueOnce(true);

      await expect(OrderService.getOrderByUuid('invalid-id')).rejects.toThrow(
        'Failed to fetch order: API error'
      );

      expect(console.error).toHaveBeenCalled();
    });

    it('should handle Axios error without response', async () => {
      // Create mock Axios error without response
      const axiosError = new Error('Network error') as any;
      // No response property

      // Mock Axios error response
      (axiosInstance.get as jest.Mock).mockRejectedValueOnce(axiosError);
      (axios.isAxiosError as unknown as jest.Mock).mockReturnValueOnce(true);

      await expect(OrderService.getOrderByUuid('invalid-id')).rejects.toThrow(
        'Failed to fetch order: Network error'
      );

      expect(console.error).toHaveBeenCalled();
    });
  });
});
