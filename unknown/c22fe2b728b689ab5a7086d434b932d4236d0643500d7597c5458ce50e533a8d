import axios from 'axios';
import Cookies from 'js-cookie';

// Mock the external dependencies
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    interceptors: {
      request: {
        use: jest.fn(),
      },
      response: {
        use: jest.fn(),
      },
    },
  })),
}));

jest.mock('js-cookie', () => ({
  get: jest.fn(),
  remove: jest.fn(),
}));

describe('Axios Instance', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should create an axios instance with the correct configuration', () => {
    // Reload the module to trigger a new instance creation
    jest.isolateModules(() => {
      require('@/src/app/_utils/axios');
      expect(axios.create).toHaveBeenCalledWith({
        headers: {
          'Content-Type': 'application/json',
          'service-provider': 'EUR',
        },
      });
    });
  });

  describe('Request Interceptor', () => {
    it('should add authorization header if token exists', () => {
      // Set up a mock token
      (Cookies.get as jest.Mock).mockReturnValue('test-token');

      // Reload and get the request interceptor
      let requestInterceptor: any;
      jest.isolateModules(() => {
        const { axiosInstance } = require('@/src/app/_utils/axios');
        requestInterceptor = axiosInstance.interceptors.request.use.mock.calls[0][0];
      });

      // Create a mock config object
      const config = { headers: {}, url: 'example.com/endpoint' };
      const result = requestInterceptor(config);

      // Check that the token was added to the Authorization header
      expect(result.headers.Authorization).toBe('Bearer test-token');
      expect(Cookies.get).toHaveBeenCalledWith('token');
      // Check baseURL was set for external URLs
      expect(result.baseURL).toBe(process.env.NEXT_PRIVATE_API_BASE_URL);
    });

    it('should not add authorization header if token does not exist', () => {
      // Set up no token
      (Cookies.get as jest.Mock).mockReturnValue(undefined);

      // Reload and get the request interceptor
      let requestInterceptor: any;
      jest.isolateModules(() => {
        const { axiosInstance } = require('@/src/app/_utils/axios');
        requestInterceptor = axiosInstance.interceptors.request.use.mock.calls[0][0];
      });

      // Create a mock config object
      const config = { headers: {}, url: 'example.com/endpoint' };
      const result = requestInterceptor(config);

      // Check that no Authorization header was added
      expect(result.headers.Authorization).toBeUndefined();
    });

    it('should not set baseURL for internal API routes', () => {
      // Reload and get the request interceptor
      let requestInterceptor: any;
      jest.isolateModules(() => {
        const { axiosInstance } = require('@/src/app/_utils/axios');
        requestInterceptor = axiosInstance.interceptors.request.use.mock.calls[0][0];
      });

      // Create a mock config object with an internal API route
      const config = { headers: {}, url: '/api/endpoint' };
      const result = requestInterceptor(config);

      // Check that baseURL was not set for internal URLs
      expect(result.baseURL).toBeUndefined();
    });

    it('should reject if there is an error', async () => {
      // Reload and get the request error handler
      let requestErrorHandler: any;
      jest.isolateModules(() => {
        const { axiosInstance } = require('@/src/app/_utils/axios');
        requestErrorHandler = axiosInstance.interceptors.request.use.mock.calls[0][1];
      });

      const error = new Error('Request error');

      // The error handler should reject with the error
      await expect(requestErrorHandler(error)).rejects.toThrow('Request error');
    });
  });

  describe('Response Interceptor', () => {
    it('should return response directly if successful', () => {
      // Reload and get the response interceptor
      let responseInterceptor: any;
      jest.isolateModules(() => {
        const { axiosInstance } = require('@/src/app/_utils/axios');
        responseInterceptor = axiosInstance.interceptors.response.use.mock.calls[0][0];
      });

      const response = { data: 'test' };
      const result = responseInterceptor(response);

      // Check that the response is returned directly
      expect(result).toBe(response);
    });

    it('should remove token if response status is 401', async () => {
      // Reload and get the response error handler
      let responseErrorHandler: any;
      jest.isolateModules(() => {
        const { axiosInstance } = require('@/src/app/_utils/axios');
        responseErrorHandler = axiosInstance.interceptors.response.use.mock.calls[0][1];
      });

      const error = { response: { status: 401 } };

      try {
        await responseErrorHandler(error);
      } catch (_e) {
        // Expected to throw
      }

      // Check that the token was removed
      expect(Cookies.remove).toHaveBeenCalledWith('token');
    });

    it('should not remove token if response status is not 401', async () => {
      // Reload and get the response error handler
      let responseErrorHandler: any;
      jest.isolateModules(() => {
        const { axiosInstance } = require('@/src/app/_utils/axios');
        responseErrorHandler = axiosInstance.interceptors.response.use.mock.calls[0][1];
      });

      const error = { response: { status: 500 } };

      try {
        await responseErrorHandler(error);
      } catch (_e) {
        // Expected to throw
      }

      // Check that the token was not removed
      expect(Cookies.remove).not.toHaveBeenCalled();
    });

    it('should reject with the error', async () => {
      // Reload and get the response error handler
      let responseErrorHandler: any;
      jest.isolateModules(() => {
        const { axiosInstance } = require('@/src/app/_utils/axios');
        responseErrorHandler = axiosInstance.interceptors.response.use.mock.calls[0][1];
      });

      const error = new Error('Response error');

      // The error handler should reject with the error
      await expect(responseErrorHandler(error)).rejects.toThrow('Response error');
    });
  });
});
