import { useServiceBySlug } from '@/src/app/_hooks/useServiceBySlug';
import { ServiceType } from '@/src/app/_interfaces';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook, waitFor } from '@testing-library/react';
import React from 'react';

// Create a wrapper with QueryClient for testing
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
    },
  });

// Mock the ServiceContext
jest.mock('@/src/app/_context/ServiceContext', () => ({
  useServiceContext: jest.fn().mockReturnValue({
    services: [],
  }),
}));

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={createTestQueryClient()}>{children}</QueryClientProvider>
);

describe('useServiceBySlug Hook (Integration Tests)', () => {
  // This variable will hold our real API data
  let realApiService: ServiceType | null = null;

  // Fetch real data before running tests
  beforeAll(async () => {
    // Increase timeout to 30 seconds for API calls
    jest.setTimeout(30000);
    try {
      console.warn('🌐 Fetching real API data for integration tests...');

      // Make a real API call
      const apiUrl = 'https://ecommerce-bff-api-smoke.getninjas.io/api/v1/service-type/list';
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'service-provider': 'EUR',
          Accept: 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }

      const data = await response.json();

      // Process the nested API response to extract a service
      if (data && data.categories) {
        for (const category of data.categories) {
          if (category.subcategories) {
            for (const subcategory of category.subcategories) {
              if (subcategory.services && subcategory.services.length > 0) {
                // Use the first service for testing
                realApiService = {
                  ...subcategory.services[0],
                  categoryName: category.name,
                  categorySlug: category.slug,
                  subcategoryName: subcategory.name,
                  subcategorySlug: subcategory.slug,
                };
                console.warn(
                  `✅ Successfully fetched service: ${realApiService?.name} (${realApiService?.slug})`
                );
                break;
              }
            }
            if (realApiService) break;
          }
        }
      }
    } catch (error) {
      console.error('❌ Error fetching API data:', error);
    }
  });

  beforeEach(() => {
    // Reset mocks between tests
    jest.clearAllMocks();
  });

  it('fetches service data with real API slug', async () => {
    // Skip test if no API data is available
    if (!realApiService) {
      console.warn('⚠️ Skipping test due to missing API data');
      return;
    }

    // Mock the ServiceContext to return our real API data
    const mockUseServiceContext = jest.requireMock(
      '@/src/app/_context/ServiceContext'
    ).useServiceContext;

    // Create a mock services array with our real API data
    mockUseServiceContext.mockReturnValue({
      services: [
        {
          id: 1,
          name: realApiService.categoryName || 'Test Category',
          slug: realApiService.categorySlug || 'test-category',
          subcategories: [
            {
              id: 101,
              name: realApiService.subcategoryName || 'Test Subcategory',
              slug: realApiService.subcategorySlug || 'test-subcategory',
              services: [realApiService],
            },
          ],
        },
      ],
      isLoading: false,
      error: null,
    });

    // Render the hook with the slug from our real service
    const { result } = renderHook(() => useServiceBySlug(realApiService?.slug || ''), { wrapper });

    // Wait for the data to be loaded
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Check that the hook returns the expected data
    expect(result.current.service).not.toBeNull();
    expect(result.current.service?.slug).toBe(realApiService.slug);
    expect(result.current.provider).not.toBeNull();
    expect(result.current.error).toBeNull();
    expect(result.current.isError).toBe(false);

    // Log whether we're using real or mock data
    console.warn('✅ Test running with REAL API data');
  });

  it('handles API errors gracefully', async () => {
    // Mock the ServiceContext to return an empty services array
    const mockUseServiceContext = jest.requireMock(
      '@/src/app/_context/ServiceContext'
    ).useServiceContext;

    mockUseServiceContext.mockReturnValue({
      services: [],
      isLoading: false,
      error: null,
    });

    // Render the hook with an invalid slug
    const { result } = renderHook(() => useServiceBySlug('invalid-slug'), { wrapper });

    // Wait for the error to be handled
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Check that the hook handles the error correctly
    expect(result.current.service).toBeNull();
    expect(result.current.provider).toBeNull();
    expect(result.current.isError).toBe(true);
    expect(result.current.error).not.toBeNull();
    expect(result.current.error).toContain('invalid-slug');
  });

  it('returns null when slug is empty', async () => {
    // Render the hook with an empty slug
    const { result } = renderHook(() => useServiceBySlug(''), { wrapper });

    // Wait for any async operations to complete
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Check that the hook returns null for empty slug
    expect(result.current.service).toBeNull();
    expect(result.current.provider).toBeNull();
    expect(result.current.isError).toBe(false);
    expect(result.current.error).toBeNull();
  });
});
