'use client';

import {
  AskForService,
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  DynamicFAQ,
  JsonLd,
  Separator,
  ServiceCarouselSkeleton,
  ServiceDetails,
  ServiceNavigationMenuDesktop,
  ServicePageSkeleton,
} from '@/src/app/_components';
import { useServiceContext } from '@/src/app/_context/ServiceContext';
import { useServiceBySlug } from '@/src/app/_hooks/useServiceBySlug';
import type { ServiceType } from '@/src/app/_interfaces';
import { Home } from 'lucide-react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { useEffect, useState } from 'react';
import MainCard from './MainCard';

interface ClientServicePageProps {
  service?: ServiceType;
  slug?: string;
}

export function ClientServicePage({ service: initialService, slug }: ClientServicePageProps) {
  const [isLoading, setIsLoading] = useState(true);

  // Get services from context for the carousel (moved to top level)
  const { services: _services } = useServiceContext();

  // Always call the hook to avoid Rules of Hooks violations
  const {
    service: hookService,
    isLoading: hookLoading,
    error: hookError,
  } = useServiceBySlug(slug || ''); // Pass empty string if slug is undefined

  // If no slug is provided, ignore the hook results
  const effectiveHookService = slug ? hookService : null;
  const effectiveHookLoading = slug ? hookLoading : false;
  const effectiveHookError = slug ? hookError : null;

  // Use either the provided service or the one from the hook
  const service = initialService || effectiveHookService;

  useEffect(() => {
    // If we have a service or an error, we're done loading
    if (service || effectiveHookError) {
      setIsLoading(false);
      return;
    }

    // If we're still waiting for the hook to load, keep loading
    if (effectiveHookLoading) {
      return;
    }

    // If we don't have a service and we're not loading, simulate a short delay
    const fetchData = async () => {
      await new Promise((resolve) => setTimeout(resolve, 500));
      setIsLoading(false);
    };

    fetchData();
  }, [service, effectiveHookError, effectiveHookLoading]);

  // Show loading state if either local loading or hook loading is true
  const showLoading = isLoading || effectiveHookLoading;

  // Show error if hook returned an error
  if (effectiveHookError) {
    return (
      <div className="container mx-auto px-8 py-16 text-center">
        <h1 className="mb-4 text-2xl font-bold">Erro ao carregar serviço</h1>
        <p className="text-gray-600">{effectiveHookError}</p>
      </div>
    );
  }

  // Show not found if no service is available and we're not loading
  if (!service && !showLoading) {
    notFound();
  }

  // If we're still loading or don't have a service yet, show loading state
  if (showLoading || !service) {
    return (
      <>
        <ServicePageSkeleton />
        <ServiceCarouselSkeleton />
      </>
    );
  }

  // Loading state is now handled above

  return (
    <div className="container mx-auto px-4 pt-8 md:px-8">
      {/* Breadcrumb - Espaçamento definido no componente pai */}
      <div className="mb-8">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/" className="flex items-center gap-1">
                  <Home className="my-1 ml-2 h-4 w-4" />{' '}
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="font-semibold text-muted-foreground">
                {service.name}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* MainCard - Espaçamento definido no componente pai */}
      <div className="mb-16">
        <MainCard service={service} />
      </div>

      {/* ServiceDetails - Espaçamento definido no componente pai */}
      <div className="mb-24">
        <ServiceDetails service={service} />
      </div>

      <Separator />

      {/* Carousel de serviços sem subcategorias
      {services && services.length > 0 && (
        <div className="my-16 lg:my-32">
          <ServiceCarousel
            services={convertToCarouselCategories(services)}
            showSubcategories={false}
          />
        </div>
      )} */}

      {/* FAQ Section - Espaçamento definido no componente pai */}
      <div className="my-24">
        <DynamicFAQ />
      </div>

      <Separator />

      {/* Menu de Navegação - Espaçamento definido no componente pai */}
      <div className="my-24">
        <section className="w-full">
          <div className="mb-12">
            <h3 className="text-3xl font-extrabold text-muted-foreground">O que você precisa?</h3>
          </div>
          <ServiceNavigationMenuDesktop
            className="w-full 2xl:-ml-40"
            containerClassName="grid-cols-2 md:grid-cols-4 gap-x-10 gap-y-6"
            categoryClassName="mb-6"
            categoryTitleClassName="flex items-center gap-2 mb-2"
            subcategoryListClassName="ml-7 space-y-3"
            subcategoryLinkClassName="text-sm font-medium text-muted-foreground hover:text-gray-900"
          />
        </section>
      </div>

      {/* Seção de pedir serviço personalizado - Espaçamento definido no componente pai */}
      <div className="mb-24">
        <AskForService
          variant="custom"
          className="relative w-full overflow-hidden rounded-3xl lg:mx-auto"
          containerClassName="relative z-10 flex flex-col items-start justify-between gap-10 lg:flex-row lg:items-center"
          titleClassName="text-2xl font-bold leading-tight sm:text-3xl"
          descriptionClassName="mt-4 max-w-2xl text-base sm:text-lg"
          buttonClassName="w-full rounded-xl bg-white px-6 py-4 text-base font-bold text-black transition duration-300 hover:bg-gray-100 sm:px-8 sm:py-6 sm:text-lg"
          showIcon={true}
        />
      </div>

      {/* Enhanced JSON-LD Structured Data */}
      <JsonLd
        data={{
          '@context': 'https://schema.org',
          '@type': 'Service',
          name: service.name,
          description: service.description,
          url: typeof window !== 'undefined' ? window.location.href : '',
          provider: {
            '@type': 'Organization',
            name: service.provider?.name,
            url: service.provider?.providerUrl,
            logo: service.provider?.imageUrl,
            description: service.provider?.description,
          },
          serviceType: service.categoryName || 'Professional Service',
          areaServed:
            service.availableIn && service.availableIn.length > 0
              ? service.availableIn.join(', ')
              : 'Brasil',
          offers: {
            '@type': 'Offer',
            price: service.price.finalPrice,
            priceCurrency: 'BRL',
            availability: 'https://schema.org/InStock',
            priceValidUntil: new Date(new Date().setFullYear(new Date().getFullYear() + 1))
              .toISOString()
              .split('T')[0],
          },
          image: service.imageUrl,
          keywords: service.keywords?.join(', '),
          mainEntityOfPage: typeof window !== 'undefined' ? window.location.href : '',
          datePublished: new Date().toISOString(),
          dateModified: new Date().toISOString(),
        }}
      />
    </div>
  );
}
