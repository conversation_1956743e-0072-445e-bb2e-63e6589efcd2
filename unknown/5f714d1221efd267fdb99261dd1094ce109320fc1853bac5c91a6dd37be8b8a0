'use client';

import { createContext, ReactNode, useContext } from 'react';

export interface Service {
  id: number;
  name: string;
  slug: string;
  description: string;
  imageUrl?: string;
  status: string;
  price: {
    priceId: number;
    originalPrice: number;
    discountPrice: number;
    finalPrice: number;
  };
  provider: {
    id: number;
    name: string;
    imageUrl?: string;
    providerUrl: string;
    description?: string;
  };
  availableIn: string[];
  details: string[];
  serviceLimits?: string;
  keywords: string[];
  termsConditionsUrl?: string;
  preparations?: string;
  categoryId: number;
  categoryName: string;
  categorySlug: string;
  subcategoryId: number;
  subcategoryName: string;
  subcategorySlug: string;
}

export interface Subcategory {
  id: number;
  name: string;
  slug: string;
  services: Service[];
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  subcategories: Subcategory[];
}

interface ServiceContextProps {
  services: Category[]; // Agora corretamente tipado
}

const ServiceContext = createContext<ServiceContextProps | undefined>(undefined);

export const useServiceContext = () => {
  const context = useContext(ServiceContext);
  if (!context) {
    throw new Error('useServiceContext deve ser usado dentro de um ServiceProvider');
  }
  return context;
};

interface ServiceProviderProps {
  children: ReactNode;
  services: Category[]; // Corrigido aqui também
}

export const ServiceProvider = ({ children, services }: ServiceProviderProps) => {
  // Ensure services is always an array, even if null or undefined is passed
  const safeServices = Array.isArray(services) ? services : [];

  return (
    <ServiceContext.Provider value={{ services: safeServices }}>{children}</ServiceContext.Provider>
  );
};
