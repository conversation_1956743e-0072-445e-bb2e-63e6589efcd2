/**
 * API Contract Tests for Service API
 *
 * These tests verify that the API returns data in the expected structure.
 * They are separate from component tests to clearly identify API-related issues.
 *
 * NOTE: This file uses mock data since we're running in a test environment.
 * In a real environment, you would run these tests against the actual API.
 */

import { ServiceType, validateService } from '@/src/app/_interfaces/service-type';

// Create mock data for testing
const createMockService = (id: number, name: string, slug: string): ServiceType => ({
  id,
  name,
  slug,
  description: `Description for ${name}`,
  imageUrl: `/images/${slug}.jpg`,
  status: 'active',
  price: {
    priceId: id,
    originalPrice: 150 + id * 10,
    discountPrice: 30,
    finalPrice: 120 + id * 10,
  },
  provider: {
    id: 1,
    name: 'Test Provider',
    imageUrl: '/provider-image.jpg',
    providerUrl: '/provider/1',
    description: 'Provider description',
  },
  availableIn: ['São Paulo', 'Rio de Janeiro'],
  details: [`Detail ${id}-1`, `Detail ${id}-2`],
  serviceLimits: 'Service limits information',
  keywords: ['test', 'service'],
  categoryName: 'Test Category',
  categorySlug: 'test-category',
  subcategoryName: 'Test Subcategory',
  subcategorySlug: 'test-subcategory',
  termsConditionsUrl: '/terms',
  preparations: `Preparation ${id}-1, Preparation ${id}-2`,
});

// Create mock API response structure
const createMockApiResponse = () => {
  const service1 = createMockService(1, 'Test Service', 'test-service');
  const service2 = createMockService(2, 'Another Service', 'another-service');

  return {
    categories: [
      {
        id: 1,
        name: 'Test Category',
        slug: 'test-category',
        subcategories: [
          {
            id: 101,
            name: 'Test Subcategory',
            slug: 'test-subcategory',
            services: [service1, service2],
          },
        ],
      },
    ],
  };
};

describe('Service API Contract', () => {
  // Test the service list endpoint
  describe('GET /service-type/list', () => {
    // Use mock data instead of real API
    const apiResponse = createMockApiResponse();

    it('returns a valid response structure', () => {
      // Check top-level structure
      expect(apiResponse).toHaveProperty('categories');
      expect(Array.isArray(apiResponse.categories)).toBe(true);
    });

    it('has categories with expected properties', () => {
      // Check first category structure
      const category = apiResponse.categories[0];
      expect(category).toHaveProperty('id');
      expect(category).toHaveProperty('name');
      expect(category).toHaveProperty('slug');
      expect(category).toHaveProperty('subcategories');
      expect(Array.isArray(category.subcategories)).toBe(true);
    });

    it('has subcategories with expected properties', () => {
      // Find a category with subcategories
      const categoryWithSubcategories = apiResponse.categories.find(
        (cat: any) => cat.subcategories && cat.subcategories.length
      );

      // Check subcategory structure
      const subcategory = categoryWithSubcategories!.subcategories[0];
      expect(subcategory).toHaveProperty('id');
      expect(subcategory).toHaveProperty('name');
      expect(subcategory).toHaveProperty('slug');
      expect(subcategory).toHaveProperty('services');
      expect(Array.isArray(subcategory.services)).toBe(true);
    });

    it('has services with expected properties', () => {
      // Extract a service from the response
      const service = apiResponse.categories[0].subcategories[0].services[0];

      // Check service structure using our validator
      expect(validateService(service)).toBe(true);

      // Check specific required properties
      expect(service).toHaveProperty('id');
      expect(service).toHaveProperty('name');
      expect(service).toHaveProperty('slug');
      expect(service).toHaveProperty('description');
      expect(service).toHaveProperty('imageUrl');
      expect(service).toHaveProperty('status');
      expect(service).toHaveProperty('price');
      expect(service).toHaveProperty('provider');
      expect(service).toHaveProperty('availableIn');
      expect(service).toHaveProperty('details');
      expect(service).toHaveProperty('serviceLimits');
      expect(service).toHaveProperty('keywords');
      expect(service).toHaveProperty('termsConditionsUrl');
      expect(service).toHaveProperty('preparations');
    });

    it('has price with expected properties', () => {
      // Extract a service from the response
      const service = apiResponse.categories[0].subcategories[0].services[0];

      // Check price structure
      const price = service.price;
      expect(price).toHaveProperty('priceId');
      expect(price).toHaveProperty('originalPrice');
      expect(price).toHaveProperty('discountPrice');
      expect(price).toHaveProperty('finalPrice');

      // Check price types
      expect(typeof price.priceId).toBe('number');
      expect(typeof price.originalPrice).toBe('number');
      expect(typeof price.discountPrice).toBe('number');
      expect(typeof price.finalPrice).toBe('number');
    });

    it('has provider with expected properties', () => {
      // Extract a service from the response
      const service = apiResponse.categories[0].subcategories[0].services[0];

      // Check provider structure
      const provider = service.provider;
      expect(provider).toHaveProperty('id');
      expect(provider).toHaveProperty('name');
      expect(provider).toHaveProperty('imageUrl');
      expect(provider).toHaveProperty('providerUrl');
      expect(provider).toHaveProperty('description');

      // Check provider types
      expect(typeof provider.id).toBe('number');
      expect(typeof provider.name).toBe('string');
      expect(typeof provider.imageUrl).toBe('string');
      expect(typeof provider.providerUrl).toBe('string');
      expect(typeof provider.description).toBe('string');
    });
  });

  // Test the individual service endpoint
  describe('GET /service-type/{slug}', () => {
    // Use mock data instead of real API
    const mockService = createMockService(1, 'Test Service', 'test-service');
    const apiResponse = { service: mockService };

    it('returns a valid response structure', () => {
      // Check top-level structure
      expect(apiResponse).toHaveProperty('service');
    });

    it('has service with expected properties', () => {
      // Check service structure
      const service = apiResponse.service;
      expect(service).toHaveProperty('id');
      expect(service).toHaveProperty('name');
      expect(service).toHaveProperty('slug');
      expect(service).toHaveProperty('description');
      expect(service).toHaveProperty('imageUrl');
      expect(service).toHaveProperty('status');
      expect(service).toHaveProperty('price');
      expect(service).toHaveProperty('provider');
    });
  });
});
