import { ApiOrderResponse } from '@/src/app/_interfaces';

export interface CepResponse {
  id: number;
  cityName: string;
  codIbge: string;
  neighborhood: string;
  street: string;
  uf: string;
  zipCode: string;
  latitude: number;
  longitude: number;
}

export interface UseInputFormatReturn {
  formatCep: (value: string) => string;
  formatNumbersOnly: (value: string) => string;
  capitalizeWords: (value: string) => string;
  formatCpf: (value: string) => string;
}

export interface UseMenuReturn {
  isMenuOpen: boolean;
  toggleMenu: () => void;
}

// useOrderData.ts interfaces

export interface UseOrderDataProps {
  orderId: string | null;
  maxRetries?: number;
  retryDelay?: number;
  initialDelay?: number;
}

export interface UseOrderDataReturn {
  orderData: ApiOrderResponse | null;
  isLoading: boolean;
  error: string | null;
}

// useSubmenu.ts interfaces

export interface UseSubmenuReturn {
  isSubmenuOpen: boolean;
  toggleSubmenu: () => void;
  handleMouseEnter: () => void;
  handleMouseLeave: () => void;
  handleClickOutside: (event: MouseEvent) => void;
}

// useTextExpansion.ts interfaces

export interface UseTextExpansionOptions {
  truncateThreshold?: number;
  charsPerLine?: number;
}

export interface UseTextExpansionReturn {
  showFullText: boolean;
  toggleTextExpansion: () => void;
  truncateText: (text: string | undefined, maxLines?: number) => string;
  formatText: (text: string | string[] | undefined, defaultText: string) => string;
  shouldShowExpandButton: (text: string | string[] | undefined) => boolean;
}

export function validateCepResponse(cep: unknown): cep is CepResponse {
  const typedCep = cep as Partial<CepResponse>;
  return (
    typeof typedCep?.id === 'number' &&
    typeof typedCep?.cityName === 'string' &&
    typeof typedCep?.codIbge === 'string' &&
    typeof typedCep?.neighborhood === 'string' &&
    typeof typedCep?.street === 'string' &&
    typeof typedCep?.uf === 'string' &&
    typeof typedCep?.zipCode === 'string' &&
    typeof typedCep?.latitude === 'number' &&
    typeof typedCep?.longitude === 'number'
  );
}

export function validateUseInputFormatReturn(
  formatter: unknown
): formatter is UseInputFormatReturn {
  const typedFormatter = formatter as Partial<UseInputFormatReturn>;
  return (
    typeof typedFormatter?.formatCep === 'function' &&
    typeof typedFormatter?.formatNumbersOnly === 'function' &&
    typeof typedFormatter?.capitalizeWords === 'function' &&
    typeof typedFormatter?.formatCpf === 'function'
  );
}

export function validateUseMenuReturn(menuReturn: unknown): menuReturn is UseMenuReturn {
  const typedMenuReturn = menuReturn as Partial<UseMenuReturn>;
  return (
    typeof typedMenuReturn?.isMenuOpen === 'boolean' &&
    typeof typedMenuReturn?.toggleMenu === 'function'
  );
}

export function validateUseOrderDataProps(props: unknown): props is UseOrderDataProps {
  const typedProps = props as Partial<UseOrderDataProps>;
  return (
    (typedProps?.orderId === null || typeof typedProps?.orderId === 'string') &&
    (typedProps?.maxRetries === undefined || typeof typedProps?.maxRetries === 'number') &&
    (typedProps?.retryDelay === undefined || typeof typedProps?.retryDelay === 'number') &&
    (typedProps?.initialDelay === undefined || typeof typedProps?.initialDelay === 'number')
  );
}

export function validateUseOrderDataReturn(returnData: unknown): returnData is UseOrderDataReturn {
  const typedReturnData = returnData as Partial<UseOrderDataReturn>;
  return (
    (typedReturnData?.orderData === null ||
      (typeof typedReturnData?.orderData === 'object' && typedReturnData?.orderData !== null)) &&
    typeof typedReturnData?.isLoading === 'boolean' &&
    (typedReturnData?.error === null || typeof typedReturnData?.error === 'string')
  );
}

export function validateUseSubmenuReturn(
  submenuReturn: unknown
): submenuReturn is UseSubmenuReturn {
  const typedSubmenuReturn = submenuReturn as Partial<UseSubmenuReturn>;
  return (
    typeof typedSubmenuReturn?.isSubmenuOpen === 'boolean' &&
    typeof typedSubmenuReturn?.toggleSubmenu === 'function' &&
    typeof typedSubmenuReturn?.handleMouseEnter === 'function' &&
    typeof typedSubmenuReturn?.handleMouseLeave === 'function' &&
    typeof typedSubmenuReturn?.handleClickOutside === 'function'
  );
}

export function validateUseTextExpansionOptions(
  options: unknown
): options is UseTextExpansionOptions {
  if (options === undefined) return true;

  const typedOptions = options as Partial<UseTextExpansionOptions>;
  return (
    (typedOptions?.truncateThreshold === undefined ||
      typeof typedOptions?.truncateThreshold === 'number') &&
    (typedOptions?.charsPerLine === undefined || typeof typedOptions?.charsPerLine === 'number')
  );
}

export function validateUseTextExpansionReturn(
  returnData: unknown
): returnData is UseTextExpansionReturn {
  const typedReturnData = returnData as Partial<UseTextExpansionReturn>;
  return (
    typeof typedReturnData?.showFullText === 'boolean' &&
    typeof typedReturnData?.toggleTextExpansion === 'function' &&
    typeof typedReturnData?.truncateText === 'function' &&
    typeof typedReturnData?.formatText === 'function' &&
    typeof typedReturnData?.shouldShowExpandButton === 'function'
  );
}
