'use client';

import { Icon, IconName } from '@/src/app/_components';
import { useServiceContext } from '@/src/app/_context/ServiceContext';
import { categories } from '@/src/app/_data/categories';
import { useAnalyticsEventGeneric } from '@/src/app/_hooks';
import { cn } from '@/src/app/_utils';
import Link from 'next/link';
import { HTMLAttributes } from 'react';

interface ServiceNavigationMenuDesktopProps extends HTMLAttributes<HTMLElement> {
  className?: string;
  containerClassName?: string;
  categoryClassName?: string;
  categoryTitleClassName?: string;
  categoryIconClassName?: string;
  subcategoryListClassName?: string;
  subcategoryItemClassName?: string;
  subcategoryLinkClassName?: string;
}

export function ServiceNavigationMenuDesktop({
  className,
  containerClassName,
  categoryClassName,
  categoryTitleClassName,
  categoryIconClassName,
  subcategoryListClassName,
  subcategoryItemClassName,
  subcategoryLinkClassName,
  ...props
}: ServiceNavigationMenuDesktopProps) {
  const { services = [] } = useServiceContext();
  const { sendEvent } = useAnalyticsEventGeneric();

  return (
    <nav
      className={cn('w-full 2xl:px-40', className)}
      aria-label="Categorias de serviços"
      {...props}
    >
      <div className={cn('grid grid-cols-2 gap-x-10 gap-y-6 md:grid-cols-4', containerClassName)}>
        {services &&
          services.map((category) => {
            const matched = categories.find((c) => c.id === category.slug);
            const iconName = matched ? matched.icon : 'CircleHelp';

            return (
              <div key={category.id} className={categoryClassName}>
                <div className={cn('flex items-center gap-2', categoryTitleClassName)}>
                  <Icon
                    name={iconName as IconName}
                    className={cn('h-5 w-5 shrink-0', categoryIconClassName)}
                  />
                  <span className="font-semibold text-foreground">{category.name}</span>
                </div>

                <ul className={cn('ml-7 mt-2 space-y-2', subcategoryListClassName)}>
                  {category.subcategories?.map((subcategory) => {
                    // Create the URL for the first service in the subcategory
                    const href =
                      subcategory.services && subcategory.services.length > 0
                        ? `/servicos/${subcategory.slug}?=${subcategory.services[0]?.slug}`
                        : `/servicos/${subcategory.slug}`;

                    return (
                      <li
                        key={`${category.id}-${subcategory.id}`}
                        className={subcategoryItemClassName}
                      >
                        <Link
                          href={href}
                          onClick={() => sendEvent(`home_click_${subcategory.name}`)}
                          className={cn(
                            'block text-left text-sm font-medium text-muted-foreground transition-colors hover:text-gray-900',
                            subcategoryLinkClassName
                          )}
                          title={`Ver serviços de ${subcategory.name}`}
                        >
                          {subcategory.name}
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            );
          })}
      </div>
    </nav>
  );
}
