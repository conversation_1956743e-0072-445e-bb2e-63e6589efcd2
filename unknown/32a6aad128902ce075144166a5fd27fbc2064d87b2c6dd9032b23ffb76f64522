#!/usr/bin/env node
/* eslint-disable no-console */

/**
 * Optimize Large WebP Images Script
 *
 * This script further optimizes large WebP images to reduce their file size
 * while maintaining acceptable quality.
 *
 * Usage:
 *   node scripts/optimize-large-webp.js [options]
 *
 * Options:
 *   --sourceDir=DIR     Source directory (default: public/images)
 *   --quality=N         Set quality level (1-100, default: 60)
 *   --sizeThreshold=N   Minimum size in KB to optimize (default: 1000)
 *   --resize=WxH        Resize images to specified dimensions (e.g., 1200x800)
 *   --dryRun            Only show what would be optimized without actually optimizing
 *   --help              Show this help message
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Parse command line arguments
const args = process.argv.slice(2);
const helpArg = args.find((arg) => arg === '--help');
const dryRunArg = args.find((arg) => arg === '--dryRun');
const sourceDirArg = args.find((arg) => arg.startsWith('--sourceDir='));
const qualityArg = args.find((arg) => arg.startsWith('--quality='));
const sizeThresholdArg = args.find((arg) => arg.startsWith('--sizeThreshold='));
const resizeArg = args.find((arg) => arg.startsWith('--resize='));

const sourceDir = sourceDirArg
  ? path.join(process.cwd(), sourceDirArg.split('=')[1])
  : path.join(process.cwd(), 'public', 'images');
const quality = qualityArg ? parseInt(qualityArg.split('=')[1], 10) : 60;
const sizeThreshold = sizeThresholdArg ? parseInt(sizeThresholdArg.split('=')[1], 10) : 1000; // in KB
const dryRun = !!dryRunArg;

// Parse resize dimensions if provided
let resizeWidth, resizeHeight;
if (resizeArg) {
  const dimensions = resizeArg.split('=')[1].split('x');
  if (dimensions.length === 2) {
    resizeWidth = parseInt(dimensions[0], 10);
    resizeHeight = parseInt(dimensions[1], 10);
  }
}

// Show help if requested
if (helpArg) {
  console.log(`
Optimize Large WebP Images Script

This script further optimizes large WebP images to reduce their file size
while maintaining acceptable quality.

Usage:
  node scripts/optimize-large-webp.js [options]

Options:
  --sourceDir=DIR     Source directory (default: public/images)
  --quality=N         Set quality level (1-100, default: 60)
  --sizeThreshold=N   Minimum size in KB to optimize (default: 1000)
  --resize=WxH        Resize images to specified dimensions (e.g., 1200x800)
  --dryRun            Only show what would be optimized without actually optimizing
  --help              Show this help message
  `);
  process.exit(0);
}

// Configuration
const config = {
  sourceDir,
  quality,
  sizeThreshold,
  dryRun,
  resize: resizeWidth && resizeHeight ? { width: resizeWidth, height: resizeHeight } : null,
};

// Statistics
const stats = {
  processed: 0,
  skipped: 0,
  errors: 0,
  totalSaved: 0, // in KB
};

// Get file size in KB
function getFileSizeInKB(filePath) {
  const stats = fs.statSync(filePath);
  return stats.size / 1024;
}

// Process a single WebP file
async function processWebP(filePath) {
  try {
    const fileSize = getFileSizeInKB(filePath);

    // Skip if file is smaller than threshold
    if (fileSize < config.sizeThreshold) {
      console.log(`Skipped: ${filePath} (${fileSize.toFixed(2)} KB, below threshold)`);
      stats.skipped++;
      return;
    }

    // Create a temporary file path
    const dir = path.dirname(filePath);
    const baseName = path.basename(filePath);
    const tempPath = path.join(dir, `temp_${baseName}`);

    if (config.dryRun) {
      console.log(`Would optimize: ${filePath} (${fileSize.toFixed(2)} KB)`);
      stats.processed++;
      return;
    }

    // Get image info
    const _metadata = await sharp(filePath).metadata();

    // Create Sharp instance
    let sharpInstance = sharp(filePath);

    // Resize if configured
    if (config.resize) {
      sharpInstance = sharpInstance.resize({
        width: config.resize.width,
        height: config.resize.height,
        fit: 'inside',
        withoutEnlargement: true,
      });
    }

    // Optimize and save to temp file
    await sharpInstance
      .webp({
        quality: config.quality,
        effort: 6, // Higher effort = better compression but slower
        smartSubsample: true, // Improves quality of color conversion
        reductionEffort: 6, // Higher = better quality/size ratio but slower
      })
      .toFile(tempPath);

    // Get new file size
    const newFileSize = getFileSizeInKB(tempPath);
    const savings = fileSize - newFileSize;

    // Only replace if we actually saved space
    if (savings > 0) {
      // Replace original with optimized version
      fs.unlinkSync(filePath);
      fs.renameSync(tempPath, filePath);

      console.log(
        `Optimized: ${filePath} (${fileSize.toFixed(2)} KB → ${newFileSize.toFixed(2)} KB, saved ${savings.toFixed(2)} KB)`
      );
      stats.totalSaved += savings;
    } else {
      // Delete temp file if no savings
      fs.unlinkSync(tempPath);
      console.log(
        `No improvement: ${filePath} (original: ${fileSize.toFixed(2)} KB, new: ${newFileSize.toFixed(2)} KB)`
      );
    }

    stats.processed++;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    stats.errors++;
  }
}

// Walk through directory recursively
async function walkDirectory(dir) {
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      await walkDirectory(filePath);
    } else if (stat.isFile() && path.extname(filePath).toLowerCase() === '.webp') {
      await processWebP(filePath);
    }
  }
}

// Main function
async function main() {
  console.log('Optimize Large WebP Images Script');
  console.log('=================================');
  console.log(`Source directory: ${config.sourceDir}`);
  console.log(`Quality level: ${config.quality}`);
  console.log(`Size threshold: ${config.sizeThreshold} KB`);
  if (config.resize) {
    console.log(`Resize dimensions: ${config.resize.width}x${config.resize.height}`);
  }
  console.log(`Mode: ${config.dryRun ? 'Dry run (no files will be modified)' : 'Optimize files'}`);
  console.log('=================================');

  if (!fs.existsSync(config.sourceDir)) {
    console.error(`Error: Source directory ${config.sourceDir} does not exist.`);
    process.exit(1);
  }

  const startTime = Date.now();

  try {
    await walkDirectory(config.sourceDir);

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log('\nOperation Complete');
    console.log('=================================');
    console.log(
      `Files ${config.dryRun ? 'that would be processed' : 'processed'}: ${stats.processed}`
    );
    console.log(`Files skipped: ${stats.skipped}`);
    console.log(`Errors: ${stats.errors}`);
    console.log(
      `Total space ${config.dryRun ? 'that would be saved' : 'saved'}: ${stats.totalSaved.toFixed(2)} KB (${(stats.totalSaved / 1024).toFixed(2)} MB)`
    );
    console.log(`Duration: ${duration.toFixed(2)} seconds`);
  } catch (error) {
    console.error('Error during operation:', error);
    process.exit(1);
  }
}

// Run the script
main();
