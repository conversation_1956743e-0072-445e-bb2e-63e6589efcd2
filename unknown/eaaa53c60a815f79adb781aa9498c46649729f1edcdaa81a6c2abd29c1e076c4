import { debounce } from '@/src/app/_utils/debounce';

describe('debounce utility', () => {
  // Mock timers for controlling setTimeout
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.clearAllMocks();
  });

  it('should delay function execution', () => {
    // Arrange
    const mockFn = jest.fn();
    const debounced = debounce(mockFn, 100);

    // Act
    debounced();

    // Assert - function should not be called immediately
    expect(mockFn).not.toHaveBeenCalled();

    // Fast-forward time
    jest.advanceTimersByTime(100);

    // Assert - function should be called after the delay
    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  it('should only execute the function once when called multiple times within delay period', () => {
    // Arrange
    const mockFn = jest.fn();
    const debounced = debounce(mockFn, 100);

    // Act - call multiple times
    debounced();
    debounced();
    debounced();

    // Assert - function should not be called immediately
    expect(mockFn).not.toHaveBeenCalled();

    // Fast-forward time
    jest.advanceTimersByTime(100);

    // Assert - function should only be called once
    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  it('should reset the timer when called again within delay period', () => {
    // Arrange
    const mockFn = jest.fn();
    const debounced = debounce(mockFn, 100);

    // Act
    debounced();

    // Fast-forward time partially
    jest.advanceTimersByTime(50);

    // Call again before the first timer completes
    debounced();

    // Fast-forward time partially again
    jest.advanceTimersByTime(50);

    // Assert - function should not be called yet (only 50ms after the second call)
    expect(mockFn).not.toHaveBeenCalled();

    // Fast-forward the remaining time
    jest.advanceTimersByTime(50);

    // Assert - function should be called once after the full delay from the second call
    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  it('should pass the correct arguments to the debounced function', () => {
    // Arrange
    const mockFn = jest.fn();
    const debounced = debounce(mockFn, 100);
    const arg1 = 'test';
    const arg2 = { key: 'value' };

    // Act
    debounced(arg1, arg2);
    jest.advanceTimersByTime(100);

    // Assert
    expect(mockFn).toHaveBeenCalledWith(arg1, arg2);
  });

  it('should maintain the correct context (this) when called', () => {
    // Arrange
    const context = {
      value: 'test',
      method: function () {
        return this.value;
      },
    };

    // Create a spy that we can check was called with the right context
    const spy = jest.spyOn(context, 'method');

    // Create debounced version of the method
    const debouncedMethod = debounce(context.method, 100);

    // Act - call with bind to set context
    debouncedMethod.call(context);
    jest.advanceTimersByTime(100);

    // Assert
    expect(spy).toHaveBeenCalledTimes(1);
  });

  it('should handle the case when timeout is cleared', () => {
    // Arrange
    const mockFn = jest.fn();
    const debounced = debounce(mockFn, 100);

    // Act
    debounced();

    // Clear all timers (simulating browser tab close or component unmount)
    jest.clearAllTimers();

    // Fast-forward time
    try {
      jest.advanceTimersByTime(100);
    } catch (_e) {
      // Ignore errors from advancing cleared timers
    }

    // Assert
    expect(mockFn).not.toHaveBeenCalled();
  });

  it('should work with zero delay', () => {
    // Arrange
    const mockFn = jest.fn();
    const debounced = debounce(mockFn, 0);

    // Act
    debounced();

    // We still need to advance timers because setTimeout executes after current
    // execution context even with 0ms delay
    jest.advanceTimersByTime(0);

    // Assert
    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  it('should work with negative delay (treating it as 0)', () => {
    // Arrange
    const mockFn = jest.fn();
    const debounced = debounce(mockFn, -10);

    // Act
    debounced();

    // We still need to advance timers because setTimeout executes after current
    // execution context even with 0ms delay
    jest.advanceTimersByTime(0);

    // Assert
    expect(mockFn).toHaveBeenCalledTimes(1);
  });
});
