'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Suspense, useState, type ReactNode } from 'react';

// Create a constant for default query client options
const DEFAULT_QUERY_OPTIONS = {
  queries: {
    staleTime: 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    refetchOnWindowFocus: false,
  },
};

interface QueryProviderProps {
  children: ReactNode;
}

// Fallback component for Suspense
function QueryDevToolsFallback() {
  return null;
}

export function QueryProvider({ children }: QueryProviderProps) {
  // Use useState to create a singleton instance of QueryClient
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: DEFAULT_QUERY_OPTIONS,
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <Suspense fallback={<QueryDevToolsFallback />}>
        <ReactQueryDevtools initialIsOpen={false} />
      </Suspense>
    </QueryClientProvider>
  );
}
