/**
 * ServiceSummary Component Tests
 *
 * This file contains unit tests for the ServiceSummary component.
 * Integration tests are in ServiceSummary.integration.test.tsx.
 *
 * Unit tests focus on component behavior with mocked dependencies,
 * while integration tests use real API data.
 */

import ServiceSummary from '@/src/app/_components/Pages/Home/ServiceSummary';
import { ServiceSummaryCard } from '@/src/app/_components/Pages/Home/ServiceSummaryCard';
import { render, screen } from '@testing-library/react';

// Mock the fetch function
global.fetch = jest.fn();

// Mock the ServiceSummaryCard component
jest.mock('@/src/app/_components/Pages/Home/ServiceSummaryCard', () => ({
  ServiceSummaryCard: jest.fn(({ service }) => (
    <div data-testid="service-summary-card">
      <span>Service Name: {service?.name || 'No service'}</span>
    </div>
  )),
}));

describe('ServiceSummary Component', () => {
  const mockService = {
    id: 1,
    slug: 'test-service',
    name: 'Test Service',
    description: 'This is a test service',
    imageUrl: '/test-image.jpg',
    status: 'active',
    categoryId: 1,
    categoryName: 'Test Category',
    categorySlug: 'test-category',
    subcategoryId: 2,
    subcategoryName: 'Test Subcategory',
    subcategorySlug: 'test-subcategory',
    provider: {
      id: 1,
      name: 'Test Provider',
      imageUrl: '/provider-image.jpg',
      providerUrl: 'https://provider.com',
      description: 'Provider description',
    },
    price: {
      priceId: 1,
      originalPrice: 120,
      discountPrice: 20,
      finalPrice: 100,
    },
    availableIn: ['São Paulo', 'Rio de Janeiro'],
    details: ['Detail 1', 'Detail 2'],
    serviceLimits: 'Service limits description',
    keywords: ['keyword1', 'keyword2'],
    termsConditionsUrl: 'https://terms.com',
    preparations: 'Preparation instructions',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock the fetch response
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: jest.fn().mockResolvedValue(mockService),
    });
  });

  it('fetches service data and passes it to ServiceSummaryCard', async () => {
    // Set up environment variable
    process.env.NEXT_PRIVATE_API_BASE_URL = 'https://api.example.com';

    // Render the component
    const { findByTestId } = render(await ServiceSummary({ slug: 'test-service' }));

    // Verify fetch was called with the correct URL
    expect(global.fetch).toHaveBeenCalledWith(
      'https://api.example.com/service-type/test-service',
      expect.objectContaining({
        headers: {
          'service-provider': 'EUR',
        },
        cache: 'no-store',
      })
    );

    // Verify the ServiceSummaryCard was rendered with the correct props
    const summaryCard = await findByTestId('service-summary-card');
    expect(summaryCard).toBeInTheDocument();

    // Check that ServiceSummaryCard was called with the service data
    expect(ServiceSummaryCard).toHaveBeenCalled();
    const callArgs = (ServiceSummaryCard as jest.Mock).mock.calls[0][0];
    expect(callArgs.service).toEqual(mockService);
  });

  it('handles fetch errors gracefully', async () => {
    // Mock a fetch error
    (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

    // Render the component
    const { findByTestId } = render(await ServiceSummary({ slug: 'test-service' }));

    // Verify the ServiceSummaryCard was rendered with null service
    const summaryCard = await findByTestId('service-summary-card');
    expect(summaryCard).toBeInTheDocument();
    expect(screen.getByText('Service Name: No service')).toBeInTheDocument();
  });

  it('handles API response errors gracefully', async () => {
    // Mock a non-ok response
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: false,
      statusText: 'Not Found',
    });

    // Render the component
    const { findByTestId } = render(await ServiceSummary({ slug: 'test-service' }));

    // Verify the ServiceSummaryCard was rendered with null service
    const summaryCard = await findByTestId('service-summary-card');
    expect(summaryCard).toBeInTheDocument();
    expect(screen.getByText('Service Name: No service')).toBeInTheDocument();
  });
});
