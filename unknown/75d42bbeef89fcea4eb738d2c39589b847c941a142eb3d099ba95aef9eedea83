import { ProviderInfoFlex } from '@/src/app/_components/Pages/Home/ProviderInfoFlex';
import { fireEvent, render, screen } from '@testing-library/react';

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, width, height }: any) => (
    <img src={src} alt={alt} width={width} height={height} data-testid="next-image" />
  ),
}));

// Mock useAnalyticsEventGeneric hook
const mockSendEvent = jest.fn();
jest.mock('@/src/app/_hooks', () => ({
  useAnalyticsEventGeneric: () => ({
    sendEvent: mockSendEvent,
  }),
}));

describe('ProviderInfoFlex Component', () => {
  beforeEach(() => {
    // Clear mock before each test
    mockSendEvent.mockClear();
  });

  it('renders the provider info section with correct content', () => {
    const { container } = render(<ProviderInfoFlex />);

    // Check for the main container
    const mainContainer = container.firstChild;
    expect(mainContainer).toHaveClass('flex', 'flex-col', 'md:flex-row');

    // Check for the text content in the first column
    expect(screen.getByText(/Agora você pode contratar serviços essenciais/)).toBeInTheDocument();
    expect(screen.getByText('GetNinjas')).toBeInTheDocument();
    expect(screen.getByText('Europ Assistance')).toBeInTheDocument();

    expect(screen.getByText(/Com o/)).toBeInTheDocument();
    expect(screen.getByText('GetNinjas + Europ')).toBeInTheDocument();
    expect(screen.getByText(/você agenda um serviço confiável/)).toBeInTheDocument();

    // Check for the logos and text in the second column
    const images = screen.getAllByTestId('next-image');
    expect(images).toHaveLength(2);
    // The order of images might be different in the implementation
    const getninjas = images.find((img) => img.getAttribute('alt') === 'GetNinjas logo');
    const europ = images.find((img) => img.getAttribute('alt') === 'Europ logo');
    expect(getninjas).toHaveAttribute('src', '/images/GetNinjas_logo.svg');
    expect(europ).toHaveAttribute('src', '/images/Europ_Logo.svg');

    // Check for the descriptions in the second column
    expect(
      screen.getByText(/Mais de 13 anos os encontrando melhores profissionais/)
    ).toBeInTheDocument();
    expect(
      screen.getByText(/Profissionais especializados e mais de 60 anos de experiência/)
    ).toBeInTheDocument();
  });

  it('has the correct layout classes for responsive design', () => {
    const { container } = render(<ProviderInfoFlex />);

    // Check for the main container
    const mainContainer = container.firstChild;
    expect(mainContainer).toHaveClass('flex-col', 'md:flex-row');

    // Check for the columns
    const columns = mainContainer?.childNodes;
    expect(columns?.[0]).toHaveClass('flex', 'flex-col', 'gap-6');
    expect(columns?.[0]).toHaveClass('md:w-1/2');
    expect(columns?.[1]).toHaveClass('flex', 'flex-col', 'gap-6');
    expect(columns?.[1]).toHaveClass('md:w-1/2');

    // Check for the border styling in the second column
    const borderDivs = screen
      .getAllByText(/Mais de 13 anos|Profissionais especializados/)
      .map((element) => element.closest('div')?.parentElement?.firstChild);

    borderDivs.forEach((div) => {
      expect(div).toHaveClass('border-l-2', 'p-8');
    });
  });

  it('sends analytics events when links are clicked', () => {
    render(<ProviderInfoFlex />);

    // Find the Europ Assistance link
    const europLink = screen
      .getByText(/Profissionais especializados e mais de 60 anos de experiência/)
      .closest('a');
    expect(europLink).toHaveAttribute('href', 'https://www.europ-assistance.com.br/');

    // Click the Europ Assistance link
    fireEvent.click(europLink!);

    // Verify that sendEvent was called with the correct parameter
    expect(mockSendEvent).toHaveBeenCalledWith('home_click_https://www.europ-assistance.com.br/');

    // Find the GetNinjas link
    const getNinjasLink = screen
      .getByText(/Mais de 13 anos os encontrando melhores profissionais/)
      .closest('a');
    expect(getNinjasLink).toHaveAttribute('href', 'https://www.getninjas.com.br/');

    // Click the GetNinjas link
    fireEvent.click(getNinjasLink!);

    // Verify that sendEvent was called with the correct parameter
    expect(mockSendEvent).toHaveBeenCalledWith('home_click_https://www.getninjas.com.br/');

    // Verify that sendEvent was called exactly twice
    expect(mockSendEvent).toHaveBeenCalledTimes(2);
  });
});
