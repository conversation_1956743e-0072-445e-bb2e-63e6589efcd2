/**
 * MainCard Component Tests
 *
 * This file serves as an entry point for all MainCard tests.
 * It imports and re-exports the unit tests.
 *
 * We've separated the tests into three categories:
 * 1. Unit Tests: Test component behavior with controlled inputs (no API dependency)
 * 2. Integration Tests: Test component with real API data (in separate file)
 * 3. API Contract Tests: Test API structure separately
 *
 * This approach makes it clear when failures are due to API issues vs. component issues.
 */

// Import the unit tests
import './MainCard.unit.test';

// Note: Integration tests are in MainCard.integration.test.tsx
// Note: API contract tests are in src/__tests__/api/service-api.contract.test.ts
