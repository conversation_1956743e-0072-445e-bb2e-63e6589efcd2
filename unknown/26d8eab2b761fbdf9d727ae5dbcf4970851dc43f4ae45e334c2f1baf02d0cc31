import {
  validateCepResponse,
  validateUseInputFormatReturn,
  validateUseMenuReturn,
  validateUseOrderDataProps,
  validateUseOrderDataReturn,
  validateUseSubmenuReturn,
  validateUseTextExpansionOptions,
  validateUseTextExpansionReturn,
} from '@/src/app/_interfaces/hooks';

test('validateCepResponse valida corretamente', () => {
  const validCep = {
    id: 1,
    cityName: 'São Paulo',
    codIbge: '3550308',
    neighborhood: 'Centro',
    street: 'Rua da Consolação',
    uf: 'SP',
    zipCode: '01001-000',
    latitude: -23.55052,
    longitude: -46.633308,
  };
  expect(validateCepResponse(validCep)).toBe(true);
});

test('validateCepResponse rejeita objeto inválido', () => {
  const invalidCep = {
    id: 1,
    cityName: 'São Paulo',
    // codIbge está faltando
    neighborhood: 'Centro',
    street: 'Rua da Consolação',
    uf: 'SP',
    zipCode: '01001-000',
    latitude: -23.55052,
    longitude: -46.633308,
  };
  expect(validateCepResponse(invalidCep)).toBe(false);
});

test('validateUseInputFormatReturn valida corretamente', () => {
  const validFormatter = {
    formatCep: (value: string) => value,
    formatNumbersOnly: (value: string) => value,
    capitalizeWords: (value: string) => value,
    formatCpf: (value: string) => value,
  };
  expect(validateUseInputFormatReturn(validFormatter)).toBe(true);
});

test('validateUseInputFormatReturn rejeita objeto inválido', () => {
  const invalidFormatter = {
    formatCep: (value: string) => value,
    formatNumbersOnly: (value: string) => value,
    // capitalizeWords está faltando
    formatCpf: (value: string) => value,
  };
  expect(validateUseInputFormatReturn(invalidFormatter)).toBe(false);
});

test('validateUseMenuReturn valida corretamente', () => {
  const validMenu = {
    isMenuOpen: false,
    toggleMenu: () => {},
  };
  expect(validateUseMenuReturn(validMenu)).toBe(true);
});

test('validateUseMenuReturn rejeita objeto inválido', () => {
  const invalidMenu = {
    isMenuOpen: false,
    // toggleMenu está faltando
  };
  expect(validateUseMenuReturn(invalidMenu)).toBe(false);
});

test('validateUseOrderDataProps valida corretamente com todos os campos', () => {
  const validProps = {
    orderId: '123',
    maxRetries: 3,
    retryDelay: 1000,
    initialDelay: 500,
  };
  expect(validateUseOrderDataProps(validProps)).toBe(true);
});

test('validateUseOrderDataProps valida corretamente com orderId null', () => {
  const validProps = {
    orderId: null,
    maxRetries: 3,
    retryDelay: 1000,
    initialDelay: 500,
  };
  expect(validateUseOrderDataProps(validProps)).toBe(true);
});

test('validateUseOrderDataProps valida corretamente com campos opcionais', () => {
  const validProps = {
    orderId: '123',
    // campos opcionais não incluídos
  };
  expect(validateUseOrderDataProps(validProps)).toBe(true);
});

test('validateUseOrderDataProps rejeita objeto inválido', () => {
  const invalidProps = {
    // orderId está faltando
    maxRetries: 3,
    retryDelay: 1000,
    initialDelay: 500,
  };
  expect(validateUseOrderDataProps(invalidProps)).toBe(false);
});

test('validateUseOrderDataReturn valida corretamente com orderData', () => {
  const validReturn = {
    orderData: {
      /* mock de ApiOrderResponse */
    },
    isLoading: false,
    error: null,
  };
  expect(validateUseOrderDataReturn(validReturn)).toBe(true);
});

test('validateUseOrderDataReturn valida corretamente com orderData null', () => {
  const validReturn = {
    orderData: null,
    isLoading: true,
    error: 'Error message',
  };
  expect(validateUseOrderDataReturn(validReturn)).toBe(true);
});

test('validateUseOrderDataReturn rejeita objeto inválido', () => {
  const invalidReturn = {
    orderData: null,
    isLoading: true,
    // error está faltando
  };
  expect(validateUseOrderDataReturn(invalidReturn)).toBe(false);
});

test('validateUseSubmenuReturn valida corretamente', () => {
  const validSubmenu = {
    isSubmenuOpen: false,
    toggleSubmenu: () => {},
    handleMouseEnter: () => {},
    handleMouseLeave: () => {},
    handleClickOutside: (_event: MouseEvent) => {},
  };
  expect(validateUseSubmenuReturn(validSubmenu)).toBe(true);
});

test('validateUseSubmenuReturn rejeita objeto inválido', () => {
  const invalidSubmenu = {
    isSubmenuOpen: false,
    toggleSubmenu: () => {},
    // handleMouseEnter está faltando
    handleMouseLeave: () => {},
    handleClickOutside: (_event: MouseEvent) => {},
  };
  expect(validateUseSubmenuReturn(invalidSubmenu)).toBe(false);
});

test('validateUseTextExpansionOptions valida corretamente com todos os campos', () => {
  const validOptions = {
    truncateThreshold: 100,
    charsPerLine: 50,
  };
  expect(validateUseTextExpansionOptions(validOptions)).toBe(true);
});

test('validateUseTextExpansionOptions valida corretamente sem campos', () => {
  const validOptions = {};
  expect(validateUseTextExpansionOptions(validOptions)).toBe(true);
});

test('validateUseTextExpansionOptions valida corretamente com undefined', () => {
  const validOptions = undefined;
  expect(validateUseTextExpansionOptions(validOptions)).toBe(true);
});

test('validateUseTextExpansionReturn valida corretamente', () => {
  const validReturn = {
    showFullText: false,
    toggleTextExpansion: () => {},
    truncateText: (_text: string | undefined, _maxLines?: number) => 'truncated text',
    formatText: (_text: string | string[] | undefined, _defaultText: string) => 'formatted text',
    shouldShowExpandButton: (_text: string | string[] | undefined) => true,
  };
  expect(validateUseTextExpansionReturn(validReturn)).toBe(true);
});

test('validateUseTextExpansionReturn rejeita objeto inválido', () => {
  const invalidReturn = {
    showFullText: false,
    toggleTextExpansion: () => {},
    truncateText: (_text: string | undefined, _maxLines?: number) => 'truncated text',
    // formatText está faltando
    shouldShowExpandButton: (_text: string | string[] | undefined) => true,
  };
  expect(validateUseTextExpansionReturn(invalidReturn)).toBe(false);
});
