# End-to-End Testing with <PERSON><PERSON>

This document describes how to run and create end-to-end tests for the GetNinjas E-commerce Frontend project using <PERSON><PERSON>.

## Setup

The project is configured to use <PERSON><PERSON> for end-to-end testing. Playwright allows you to write tests that run in real browsers (Chrome, Firefox, Safari) and simulate user interactions.

### Prerequisites

- Node.js 16 or higher
- pnpm package manager

### Installation

The necessary dependencies are already included in the project's `package.json`. If you need to install them manually, run:

```bash
pnpm add -D @playwright/test dotenv
```

To install the browser binaries required by <PERSON><PERSON>:

```bash
npx playwright install
```

## Running Tests

The project includes several scripts for running end-to-end tests:

```bash
# Run all e2e tests
pnpm test:e2e

# Run tests in a specific browser
pnpm test:e2e --project=chromium
pnpm test:e2e --project=firefox
pnpm test:e2e --project=webkit

# Run tests with UI mode (for debugging)
pnpm test:e2e:ui

# Run tests in debug mode
pnpm test:e2e:debug

# Show the HTML report after tests have run
pnpm test:e2e:report
```

## Test Structure

The end-to-end tests are located in the `e2e` directory:

- `home.spec.ts`: Tests for the homepage functionality
- `api.spec.ts`: Tests for API integration
- `navigation.spec.ts`: Tests for navigation and user interactions
- `utils/`: Utility functions for tests
  - `env.ts`: Functions to read environment variables

## Configuration

The tests are configured in `playwright.config.ts` in the project root. Key configuration options:

- Tests run against the URL specified in `NEXT_PUBLIC_BASE_URL` environment variable (defaults to `http://localhost:3000`)
- API requests use the URL from `NEXT_PRIVATE_API_BASE_URL` environment variable
- Tests run in multiple browsers: Chrome, Firefox, Safari, and mobile viewports
- Screenshots are captured on test failures
- A local development server is started automatically before tests run

## Environment Variables

The tests read environment variables from the `.env` file. The following variables are used:

- `NEXT_PUBLIC_BASE_URL`: The base URL of the application (default: `http://localhost:3000`)
- `NEXT_PRIVATE_API_BASE_URL`: The base URL for API requests (default: `https://ecommerce-api-smoke.getninjas.io/api/v1`)

## Writing New Tests

To add new tests:

1. Create a new `.spec.ts` file in the `e2e` directory
2. Import the necessary Playwright utilities:
   ```typescript
   import { test, expect } from '@playwright/test';
   ```
3. Write your tests using the Playwright API
4. Run the tests to verify they work as expected

### Example Test

```typescript
import { test, expect } from '@playwright/test';

test.describe('Example Test Group', () => {
  test('should navigate to a page', async ({ page }) => {
    // Navigate to a page
    await page.goto('/some-page');

    // Check if an element is visible
    const element = page.locator('.some-selector');
    await expect(element).toBeVisible();

    // Click a button
    await page.click('button.some-button');

    // Check if the URL changed
    expect(page.url()).toContain('/new-page');
  });
});
```

## Best Practices

- Keep tests independent of each other
- Use descriptive test names
- Group related tests using `test.describe()`
- Use page objects for complex pages
- Avoid hard-coding selectors when possible
- Use data attributes like `data-testid` for more reliable selectors
- Take screenshots at key points for visual verification
- Mock API responses when testing UI components in isolation

## Debugging

If tests are failing, you can:

1. Run tests in UI mode: `pnpm test:e2e:ui`
2. Run tests in debug mode: `pnpm test:e2e:debug`
3. Check the screenshots in the `screenshots` directory
4. View the HTML report: `pnpm test:e2e:report`

## Continuous Integration

The tests can be run in a CI environment. The configuration in `playwright.config.ts` includes settings for CI:

- `forbidOnly: !!process.env.CI`: Fails the build if `test.only` is used in the source code
- `retries: process.env.CI ? 2 : 0`: Retries failed tests in CI environments

## Resources

- [Playwright Documentation](https://playwright.dev/docs/intro)
- [Playwright API Reference](https://playwright.dev/docs/api/class-playwright)
- [Playwright Test Assertions](https://playwright.dev/docs/test-assertions)
