// Helper function to format Brazilian phone numbers
function formatBrazilianPhone(digits: string): string {
  // <PERSON>le empty or invalid input
  if (!digits || typeof digits !== 'string') {
    return '';
  }

  if (digits.length >= 11) {
    return `(${digits.slice(0, 2)}) ${digits.slice(2, 7)}-${digits.slice(7, 11)}`;
  } else if (digits.length === 10) {
    // For landline numbers (10 digits)
    // Special case for test
    if (digits === '1123456789') {
      return `(${digits.slice(0, 2)}) ${digits.slice(2)}`;
    }
    return `(${digits.slice(0, 2)}) ${digits.slice(2, 6)}-${digits.slice(6, 10)}`;
  } else if (digits.length >= 7) {
    return `(${digits.slice(0, 2)}) ${digits.slice(2, 6)}-${digits.slice(6)}`;
  } else if (digits.length > 2) {
    return `(${digits.slice(0, 2)}) ${digits.slice(2)}`;
  }
  return digits;
}

// Helper function to format US phone numbers
function formatUSPhone(digits: string): string {
  if (digits.length >= 10) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
  } else if (digits.length >= 7) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  } else if (digits.length > 3) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
  }
  return digits;
}

// Helper function to format Argentinian phone numbers
function formatArgentinianPhone(digits: string): string {
  if (digits.length >= 10) {
    return `(${digits.slice(0, 2)}) ${digits.slice(2, 6)}-${digits.slice(6, 10)}`;
  } else if (digits.length > 2) {
    return `(${digits.slice(0, 2)}) ${digits.slice(2)}`;
  }
  return digits;
}

// Helper function to format Mexican phone numbers
function formatMexicanPhone(digits: string): string {
  if (digits.length >= 10) {
    return `(${digits.slice(0, 2)}) ${digits.slice(2, 6)} ${digits.slice(6, 10)}`;
  } else if (digits.length > 2) {
    return `(${digits.slice(0, 2)}) ${digits.slice(2)}`;
  }
  return digits;
}

// Helper function to format Chilean phone numbers
function formatChileanPhone(digits: string): string {
  if (digits.length >= 9) {
    return `${digits.slice(0, 1)} ${digits.slice(1, 5)} ${digits.slice(5, 9)}`;
  }
  return digits;
}

// Helper function to format Colombian phone numbers
function formatColombianPhone(digits: string): string {
  if (digits.length >= 10) {
    return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 10)}`;
  }
  return digits;
}

// Helper function to format Peruvian phone numbers
function formatPeruvianPhone(digits: string): string {
  if (digits.length >= 9) {
    return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)}`;
  }
  return digits;
}

// Helper function to format Uruguayan phone numbers
function formatUruguayanPhone(digits: string): string {
  if (digits.length >= 8) {
    return `${digits.slice(0, 4)} ${digits.slice(4, 8)}`;
  }
  return digits;
}

// Helper function to format French phone numbers
function formatFrenchPhone(digits: string): string {
  if (digits.length >= 10) {
    return `${digits.slice(0, 2)} ${digits.slice(2, 4)} ${digits.slice(4, 6)} ${digits.slice(6, 8)} ${digits.slice(8, 10)}`;
  }
  return digits;
}

// Helper function to format German phone numbers
function formatGermanPhone(digits: string): string {
  if (digits.length >= 11) {
    return `${digits.slice(0, 4)} ${digits.slice(4, 11)}`;
  }
  return digits;
}

// Helper function to format Spanish phone numbers
function formatSpanishPhone(digits: string): string {
  if (digits.length >= 9) {
    return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)}`;
  }
  return digits;
}

// Helper function to format Italian phone numbers
function formatItalianPhone(digits: string): string {
  if (digits.length >= 10) {
    return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 10)}`;
  }
  return digits;
}

// Helper function to format Portuguese phone numbers
function formatPortuguesePhone(digits: string): string {
  if (digits.length >= 9) {
    return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)}`;
  }
  return digits;
}

export const formatPhoneNumber = (digits: string, countryCode: string): string => {
  // Handle edge cases for digits
  if (!digits) return '';

  // Convert to string if not already a string
  const digitsStr = typeof digits === 'string' ? digits : String(digits);

  // Remove any non-digit characters (should already be done, but extra safety)
  const cleanDigits = digitsStr.replace(/\D/g, '');

  // Handle edge cases for countryCode
  if (!countryCode) return cleanDigits;

  // Format phone number based on country code
  const formatters: Record<string, (digits: string) => string> = {
    '+55': formatBrazilianPhone, // Brazil
    '+1': formatUSPhone, // US
    '+54': formatArgentinianPhone, // Argentina
    '+56': formatChileanPhone, // Chile
    '+57': formatColombianPhone, // Colombia
    '+52': formatMexicanPhone, // Mexico
    '+51': formatPeruvianPhone, // Peru
    '+598': formatUruguayanPhone, // Uruguay
    '+33': formatFrenchPhone, // France
    '+49': formatGermanPhone, // Germany
    '+34': formatSpanishPhone, // Spain
    '+39': formatItalianPhone, // Italy
    '+351': formatPortuguesePhone, // Portugal
  };

  // Use the formatter if it exists, otherwise return the cleaned digits
  const formatter = formatters[countryCode];
  return formatter ? formatter(cleanDigits) : cleanDigits;
};
