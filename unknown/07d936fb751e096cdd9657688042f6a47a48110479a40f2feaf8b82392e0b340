import axios from 'axios';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

// Default value for build time - will be replaced at runtime by Vault value
const DEFAULT_API_URL = 'https://api.placeholder-for-build.com';

// Use the runtime value from Vault if available, otherwise fall back to build-time default
const API_URL = process.env.NEXT_PRIVATE_API_BASE_URL || DEFAULT_API_URL;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    // Check for both parameter names for backward compatibility
    const uuid = searchParams.get('uuid');
    const orderId = searchParams.get('orderId');
    // Use the first one that's available
    const orderIdentifier = uuid || orderId;

    if (!orderIdentifier) {
      return NextResponse.json({ message: 'UUID or orderId is required' }, { status: 400 });
    }

    const { data } = await axios.get(`${API_URL}/order/by-uuid`, {
      params: { uuid: orderIdentifier },
      headers: {
        'service-provider': 'EUR',
      },
    });

    // Pass through the API response as-is
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in order by-uuid API route:', error);
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data || error.message;
      return NextResponse.json(
        {
          message: `Failed to fetch order: ${error.response?.statusText || 'Unknown error'}`,
          details: errorMessage,
        },
        { status: error.response?.status || 500 }
      );
    }
    return NextResponse.json(
      { message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}
