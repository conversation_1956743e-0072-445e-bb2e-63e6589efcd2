// Import Jest DOM matchers
import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    pathname: '/',
    query: {},
  }),
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
}));

// Mock Next.js image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, ...props }) => {
    // Using img element is necessary for testing
    return <img src={src} alt={alt} {...props} data-testid={`next-image-${alt}`} />;
  },
}));

// Mock window.gtag for analytics testing
Object.defineProperty(window, 'gtag', {
  writable: true,
  value: jest.fn(),
});

// Mock window.dataLayer for analytics testing
Object.defineProperty(window, 'dataLayer', {
  writable: true,
  value: [],
});

// Mock window.fbq for Meta Pixel testing
Object.defineProperty(window, 'fbq', {
  writable: true,
  value: jest.fn(),
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Reset mocks between tests
beforeEach(() => {
  jest.clearAllMocks();
  window.dataLayer = [];
});
