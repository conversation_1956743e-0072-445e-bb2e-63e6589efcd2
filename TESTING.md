# Testes no Projeto GetNinjas E-commerce Frontend

Este documento descreve como executar e criar testes para o projeto GetNinjas E-commerce Frontend.

## Configuração de Testes

O projeto utiliza as seguintes bibliotecas para testes:

- **Jest**: Framework de testes
- **React Testing Library**: Biblioteca para testar componentes React
- **@testing-library/user-event**: Biblioteca para simular eventos de usuário
- **@testing-library/jest-dom**: Extensões do Jest para DOM

## Executando os Testes

Para executar os testes, você pode usar os seguintes comandos:

```bash
# Executar todos os testes
pnpm test

# Executar testes em modo de observação (watch mode)
pnpm test:watch

# Executar testes com cobertura
pnpm test:coverage
```

## Estrutura de Testes

Os testes estão organizados na pasta `src/__tests__/` com a seguinte estrutura:

- `src/__tests__/components/`: Testes para componentes React
- `src/__tests__/utils/`: Testes para funções utilitárias

## Exemplos de Testes

### Testando Funções Utilitárias

Exemplo de teste para validação de CPF e CEP:

```typescript
import { isValidBrazilianCPF, isValidBrazilianPostalCode } from '@/src/app/_utils/formValidation';

describe('isValidBrazilianCPF', () => {
  test('should return true for valid CPF', () => {
    expect(isValidBrazilianCPF('529.982.247-25')).toBe(true);
  });

  test('should return false for invalid CPF', () => {
    expect(isValidBrazilianCPF('111.111.111-11')).toBe(false);
  });
});
```

### Testando Componentes React

Exemplo de teste para o componente PhoneInput:

```typescript
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { PhoneInput } from "@/src/app/_components/PhoneInput/PhoneInput";

describe("PhoneInput Component", () => {
  test("renders correctly with default props", () => {
    render(<PhoneInput value="" countryCode="+55" onChange={() => {}} />);
    const inputElement = screen.getByRole("textbox");
    expect(inputElement).toBeInTheDocument();
  });

  test("shows error message for invalid phone number", () => {
    render(<PhoneInput value="123" countryCode="+55" onChange={() => {}} />);
    const inputElement = screen.getByRole("textbox");
    fireEvent.blur(inputElement);
    expect(
      screen.getByText("Insira um número de telefone válido.")
    ).toBeInTheDocument();
  });
});
```

### Testando Componentes com Modal (Radix UI)

Para testar componentes que utilizam modais do Radix UI, é necessário criar mocks para os componentes do Radix UI:

```typescript
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { CheckoutErrorModal } from "@/src/app/_components/Checkout/CheckoutErrorModal";

// Mock do componente Dialog do Radix UI
jest.mock("@/src/app/_components/Ui/dialog", () => {
  return {
    Dialog: ({ children, open, onOpenChange }: any) => (
      <div data-testid="dialog" data-open={open}>
        {open && children}
      </div>
    ),
    DialogContent: ({ children }: any) => (
      <div data-testid="dialog-content">{children}</div>
    ),
    DialogHeader: ({ children }: any) => (
      <div data-testid="dialog-header">{children}</div>
    ),
    DialogTitle: ({ children }: any) => (
      <div data-testid="dialog-title">{children}</div>
    ),
    DialogFooter: ({ children }: any) => (
      <div data-testid="dialog-footer">{children}</div>
    ),
  };
});

describe("CheckoutErrorModal Component", () => {
  test("renders correctly when open", () => {
    render(
      <CheckoutErrorModal isOpen={true} onClose={() => {}} onRetry={() => {}} />
    );

    // Verifica se o diálogo está aberto
    const dialog = screen.getByTestId("dialog");
    expect(dialog).toHaveAttribute("data-open", "true");

    // Verifica se o título está renderizado
    expect(screen.getByTestId("dialog-title")).toHaveTextContent(
      "Agendamento não realizado."
    );
  });

  test("calls onClose when close button is clicked", () => {
    const mockOnClose = jest.fn();
    render(
      <CheckoutErrorModal
        isOpen={true}
        onClose={mockOnClose}
        onRetry={() => {}}
      />
    );

    // Encontra e clica no botão de fechar
    const buttons = screen.getAllByTestId("button");
    fireEvent.click(buttons[0]);

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });
});
```

## Boas Práticas

1. **Teste comportamentos, não implementações**: Foque em testar como o usuário interage com o componente, não detalhes de implementação.

2. **Use mocks com moderação**: Mocks são úteis, mas podem tornar os testes frágeis se usados em excesso.

3. **Teste casos de erro**: Não teste apenas o caminho feliz, teste também casos de erro e comportamentos inesperados.

4. **Mantenha os testes independentes**: Cada teste deve ser independente e não depender do estado de outros testes.

5. **Use dados realistas**: Use dados que se assemelham ao que seria usado na aplicação real.

6. **Mock de componentes externos**: Para componentes de UI complexos como os do Radix UI, crie mocks simples que permitam testar a lógica do seu componente sem depender da implementação interna do componente externo.

## Adicionando Novos Testes

Para adicionar novos testes:

1. Crie um arquivo de teste na pasta apropriada (`components` ou `utils`)
2. Nomeie o arquivo como `[nome-do-componente-ou-funcao].test.tsx` ou `[nome-do-componente-ou-funcao].test.ts`
3. Escreva os testes seguindo os exemplos acima
4. Execute os testes para verificar se estão passando

## Cobertura de Testes

O projeto está configurado para gerar relatórios de cobertura de testes. Execute `pnpm test:coverage` para gerar um relatório detalhado.

## Testando Formulários com React Hook Form

Para testar formulários que utilizam React Hook Form, você pode seguir estas estratégias:

1. **Teste a validação de campos**: Verifique se os erros de validação são exibidos corretamente quando o usuário insere dados inválidos.

2. **Teste o envio do formulário**: Verifique se a função de envio é chamada com os dados corretos quando o formulário é válido.

3. **Teste o comportamento de campos dependentes**: Verifique se campos que dependem de outros campos são atualizados corretamente.

Exemplo:

```typescript
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { PersonalInfoForm } from "@/src/app/_components/Checkout/PersonalInfoForm";

test("exibe erro quando CPF é inválido", async () => {
  render(<PersonalInfoForm onSubmit={() => {}} />);

  // Preenche o campo de CPF com um valor inválido
  const cpfInput = screen.getByLabelText(/CPF/i);
  fireEvent.change(cpfInput, { target: { value: "111.111.111-11" } });
  fireEvent.blur(cpfInput);

  // Verifica se a mensagem de erro é exibida
  await waitFor(() => {
    expect(
      screen.getByText("CPF inválido. Verifique os números digitados.")
    ).toBeInTheDocument();
  });
});
```
