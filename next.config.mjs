let userConfig = undefined;
try {
  userConfig = await import('./v0-user-next.config');
} catch (e) {
  // ignore error
}

const isProd = process.env.NODE_ENV === 'production';

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: false,
  },
  typescript: {
    ignoreBuildErrors: false,
  },
  env: {
    NEXT_PRIVATE_API_BASE_URL: process.env.NEXT_PRIVATE_API_BASE_URL,
  },
  async redirects() {
    return [
      // Handle variations and autocorrections
      {
        source: '/europa/:path*',
        destination: '/europ/:path*',
        permanent: true,
      },
      {
        source: '/europe/:path*',
        destination: '/europ/:path*',
        permanent: true,
      },
      {
        source: '/europ-:path*',
        destination: '/europ/:path*',
        permanent: true,
      },
      {
        source: '/europa-:path*',
        destination: '/europ/:path*',
        permanent: true,
      },
    ];
  },
  images: {
    unoptimized: true,
    domains: [
      process.env.NEXT_PUBLIC_ASSETS_DOMAIN?.replace('https://', '').replace('http://', ''),
    ].filter(Boolean),
    minimumCacheTTL: 60, // 1 minute cache for all environments
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  reactStrictMode: true,
  output: 'standalone', //don't remove as it's needed for docker build
  assetPrefix: process.env.NEXT_PUBLIC_ASSETS_DOMAIN
    ? `https://${process.env.NEXT_PUBLIC_ASSETS_DOMAIN}`
    : undefined,
  headers: async () => [
    // Static assets (images, fonts, etc.) - short cache with must-revalidate
    {
      source: '/:path*(jpg|jpeg|png|gif|ico|svg|webp|avif|woff|woff2|ttf|eot|css|js)',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=60, must-revalidate',
        },
      ],
    },
    // Next.js internal static files
    {
      source: '/_next/static/:path*',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=60, must-revalidate',
        },
      ],
    },
    // Next.js image optimization
    {
      source: '/_next/image/:path*',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=60, must-revalidate',
        },
      ],
    },
    // Static files in public directory
    {
      source: '/static/:path*',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=60, must-revalidate',
        },
      ],
    },
    // Font files
    {
      source: '/fonts/:path*',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=60, must-revalidate',
        },
      ],
    },
    // API routes - no cache
    {
      source: '/api/:path*',
      headers: [
        {
          key: 'Cache-Control',
          value: 'no-store, must-revalidate',
        },
        {
          key: 'Pragma',
          value: 'no-cache',
        },
        {
          key: 'Expires',
          value: '0',
        },
      ],
    },
    // SWR API routes - no cache
    {
      source: '/api/swr/:path*',
      headers: [
        {
          key: 'Cache-Control',
          value: 'no-store, must-revalidate',
        },
      ],
    },
    // All other routes - no cache
    {
      source: '/:path*',
      headers: [
        {
          key: 'Cache-Control',
          value: 'no-store, must-revalidate',
        },
        {
          key: 'Pragma',
          value: 'no-cache',
        },
        {
          key: 'Expires',
          value: '0',
        },
      ],
    },
  ],
  bundlePagesRouterDependencies: true,
  generateEtags: true,
  staticPageGenerationTimeout: 120,
  compress: true,
  poweredByHeader: false,
  productionBrowserSourceMaps: false,
};

mergeConfig(nextConfig, userConfig);

function mergeConfig(nextConfig, userConfig) {
  if (!userConfig) {
    return;
  }

  for (const key in userConfig) {
    if (typeof nextConfig[key] === 'object' && !Array.isArray(nextConfig[key])) {
      nextConfig[key] = {
        ...nextConfig[key],
        ...userConfig[key],
      };
    } else {
      nextConfig[key] = userConfig[key];
    }
  }
}

export default nextConfig;
