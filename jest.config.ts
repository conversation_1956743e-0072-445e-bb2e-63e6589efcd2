import { Config } from 'jest';
import nextJest from 'next/jest.js';

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './',
});

// Add any custom config to be passed to Jest
const config: Config = {
  coverageProvider: 'v8',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleDirectories: ['node_modules', '<rootDir>'],
  moduleNameMapper: {
    // Alias para facilitar as importações
    '^@/src/(.*)$': '<rootDir>/src/$1',
    '^@/(.*)$': '<rootDir>/$1',
    '^@/tests/(.*)$': '<rootDir>/src/__tests__/$1',
    // Mock para arquivos estáticos
    '\\.(jpg|jpeg|png|gif|svg)$': '<rootDir>/src/__tests__/__mocks__/fileMock.js',
    '\\.(css|scss)$': '<rootDir>/src/__tests__/__mocks__/styleMock.js',
    // Mock for problematic modules
    'react-error-boundary': '<rootDir>/src/__tests__/__mocks__/react-error-boundary.js',
    // Mock for client components
    '@/src/app/\\(pages\\)/servicos/\\[subcategory\\]/\\[slug\\]/checkout/CheckoutPageClient':
      '<rootDir>/src/__tests__/__mocks__/app/(pages)/servicos/[subcategory]/[slug]/checkout/CheckoutPageClient.tsx',
  },
  transform: {
    '^.+\\.(ts|tsx|js|jsx|mjs)$': ['babel-jest', { presets: ['next/babel'] }],
  },
  transformIgnorePatterns: [
    '/node_modules/(?!(react-markdown|vfile|vfile-message|unist-.*|unified|bail|is-plain-obj|trough|remark-.*|mdast-util-.*|micromark.*|decode-named-character-reference|character-entities|property-information|hast-util-whitespace|space-separated-tokens|comma-separated-tokens|@types/mdast|markdown-table|escape-string-regexp|lucide-react|next|node-fetch|data-uri-to-buffer|fetch-blob|formdata-polyfill|react-error-boundary))',
  ],
  roots: ['<rootDir>/src'],
  modulePaths: ['<rootDir>/src'],
  extensionsToTreatAsEsm: ['.ts', '.tsx', '.jsx'],
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/src/__tests__/fixtures/',
    '/src/__tests__/__mocks__/',
    '/src/__tests__/__test-utils__/',
    '/src/__tests__/__helpers__/',
    '/src/__tests__/hooks/__helpers__/renderHook.tsx',
    '/src/__tests__/utils/renderHook.tsx',
  ],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/app/_components/Ui/*.{ts,tsx}', // Ignorar componentes shadcn UI
    '!src/**/*.d.ts', // Ignorar arquivos de definição de tipos
    '!src/**/*.stories.{ts,tsx}', // Ignorar arquivos de storybook
    '!src/app/api/**', // Ignorar arquivos de API routes
    '!src/app/layout.tsx', // Ignorar layout raiz
    '!src/app/page.tsx', // Ignorar página inicial
    '!src/app/(pages)/avengers-assemble/**/*.{ts,tsx}', // Ignorar página avengers-assemble conforme solicitado
    '!**/avengers-assemble/**', // Garantir que todos os arquivos da pasta avengers-assemble sejam ignorados
    '!src/app/robots.ts', // Ignorar arquivos de configuração
    '!src/app/sitemap.ts', // Ignorar arquivos de configuração
    '!src/app/not-found.tsx', // Ignorar página de erro
    '!**/node_modules/**',
    '!**/dist/**',
    '!src/__tests__/**', // Ignorar os próprios arquivos de teste
  ],
  coverageThreshold: {
    global: {
      statements: 95,
      branches: 95,
      functions: 95,
      lines: 95,
    },
  },
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
export default createJestConfig(config);
