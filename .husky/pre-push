# Run linting on changed files only
echo "Running pre-push checks..."
# Get list of changed files
CHANGED_FILES=$(git diff --name-only --cached --diff-filter=ACMR | grep -E '\.(js|jsx|ts|tsx)$' | tr '\n' ' ')

if [ -n "$CHANGED_FILES" ]; then
  echo "Linting changed files: $CHANGED_FILES"
  pnpm eslint $CHANGED_FILES
else
  echo "No relevant files changed, skipping lint"
fi

# Skip tests for now, they will run in CI
# pnpm run test
